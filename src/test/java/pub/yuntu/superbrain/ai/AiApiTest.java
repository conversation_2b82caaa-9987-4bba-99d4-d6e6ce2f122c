package pub.yuntu.superbrain.ai;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import pub.yuntu.api.ai.Alibaba.AliApi;
import pub.yuntu.api.ai.Alibaba.constants.AliConstants;
import pub.yuntu.api.ai.baidu.BaiduApi;
import pub.yuntu.api.ai.baidu.BaiduModel;
import pub.yuntu.api.ai.xunfei.XunFeiThirdJarApi;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 测试调用大模型接口
 * @createTime 2023年11月22日 10:05:04
 */
@Slf4j
public class AiApiTest extends TestCase {
    String prompt = "";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：总体满意度显著提升。销售阶段表现良好。签约后等待阶段、交付阶段、居住阶段客户实际体验与期望落差大。";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：关键业务指标多有下滑，和标杆落差明显，质量、区内规划设计、交付服务、整改维修、投诉处理，明显薄弱，滑落至行业总体水平或以下";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：产品端：房屋设计有所进步，房屋质量、园林绿化、区内规划设计连续两年下挫";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：服务端：销售服务较19年进步，行业优势不明显，物业服务表现平稳，维持行业优良水平，其余指标发生不同程度的下滑，交付服务大幅下挫至行业总体以下";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：总体满意度表现：2020年越秀地产住宅客户满意度85%，位于行业60~70分位间，各业主类型前后两端维持高位，保内新交付项目表现走低";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：客户评价整体表现：客户满意度逐年提升，20年较19年提升5%，好于行业均值，与行业增长水平趋同";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：业主全生命周期表现：准业主、老业主评价提升，磨合期新交付项目客户体验走低，关注两个节点（交付体验落差大于行业；磨1-磨2维修阶段体验持续下滑）";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：从总部整体表现来看，满意度高分项目密集，但存在极端低分项目";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：从区域表现来看：各区域内项目稳定性表现不均衡，湾中、湾东、华东存在项目表现落差大，湾中、湾东、华东、北方存在极端案例";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：行业业务指标呈现3类趋势，上升-销售服务、签约后沟通服务、维修服务、物业服务提升显著，平稳-服务整体呈现上升趋势，房屋质量和交付服务均呈现下降趋势，预警-近三年房屋设计、园林绿化、区内规划设施表现较平稳";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：业务指标表现：关键业务指标普遍提升，产品设计、销售服务、物业服务提升幅度大，产品交付环节的“房屋质量-交付表现-维修服务”对标行业显薄弱";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：业主推荐意向高于总体满意度，与行业趋势相反，部分不满意的业主会向他人推荐国贸的楼盘，国贸的产品和服务精细度还不够，损失了一部分溢价空间";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：随着入住时间推移，业主的推荐意向步衰退，国贸不采取措施改善老业主的居住体验，逐步流失已经获得的客户资源";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：中国金茂总体由行业95分位降至80分位，准业主由行业80分位降至60分位，磨合期由行业95分位降至90分位，稳定期由行业95分位降至90分位，老业主由行业95分位降至65分位";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：产品表现，各指标均表现优良，基本达到行业标杆水平，国贸产品优势明显";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：服务表现，销售服务、投诉处理表现略低于行业总体，签约后服务、交付服务、维修服务提升幅度大，均显著超过行业总体水平，物业服务是目前国贸的唯一短板，相比15年虽有提升，但满意度仍然偏低";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：23年3季度累计，中国金茂住宅客户总体满意度为90%，23年3季度累计较22年3季度累计增长0.14%\\n各个节点中，磨合期2样框占比最高\\n老业主较22年3季度累计退步最为明显\\n二级关键业务指标层面，物业服务、科技系统、园林绿化、区内规划和公区设施、房屋设计、交付服务、会员服务较22年3季度累计有所退步，其中，物业服务较22年3季度累计退步最为明显，需警惕\\n区域层面，23年3季度累计共6个区域参与23年3季度累计调研，其中，共有3个区域较22年3季度累计有提升，3个区域较22年3季度累计有下滑\\n金茂华北客户满意度领先于其他区域，客户主要集中于磨合期2节点\\n金茂华中客户满意度落后于其他区域，注意房屋设计指标的改进，金茂华中的客户评价较低，受武汉华发金茂国际社区项目低分的影响较大\\n城市（事业部）层面，23年3季度累计共25个城市（事业部）参与调研，其中，共有10个城市（事业部）较22年3季度累计有提升，13个城市（事业部）较22年3季度累计有下滑，合肥、南昌表现相对较弱\\n项目层面，23年3季度累计调研共覆盖138个项目，其中，共有52个项目较22年3季度累计有提升，62个项目较22年3季度累计有下滑\\n北京未来金茂府项目的客户满意度较22年3季度累计进步最为明显，其次进步较明显的项目是北京大兴金茂悦\\n武汉华发金茂国际社区、成都锦江金茂府、武汉东湖金茂府、台州首开金茂金玉上城、武汉方岛金茂智慧科学城（岛外）、北京通州台湖金茂悦、南昌宸南里、成都武侯金茂府、无锡蠡湖金茂府、温州九山金茂府、杭州滨江金茂府、成都东叁金茂逸墅、武汉阳逻金茂逸墅、广州南沙灵山岛金茂湾、上海西郊金茂府、南昌九龙湖金茂悦、合肥北雁湖金茂湾、株洲金茂悦等项目在各个项目中排名靠后，且较22年3季度累计发生下滑，需关注";
//    String prompt = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：总体满意度低于行业总体9%，准业主、稳定期、老业主均大幅下挫且低于行业总体，老业主体量大，拉低了集团满意度得分，磨合期大幅提升，高于行业总体8%，表现良好。";

//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n23年3季度累计，中国金茂住宅客户总体满意度为90%，23年3季度累计较22年3季度累计增长0.14%\\n各个节点中，磨合期2样框占比最高\\n老业主较22年3季度累计退步最为明显\\n二级关键业务指标层面，物业服务、科技系统、园林绿化、区内规划和公区设施、房屋设计、交付服务、会员服务较22年3季度累计有所退步，其中，物业服务较22年3季度累计退步最为明显，需警惕\\n区域层面，23年3季度累计共6个区域参与23年3季度累计调研，其中，共有3个区域较22年3季度累计有提升，3个区域较22年3季度累计有下滑\\n金茂华北客户满意度领先于其他区域，客户主要集中于磨合期2节点\\n金茂华中客户满意度落后于其他区域，注意房屋设计指标的改进，金茂华中的客户评价较低，受武汉华发金茂国际社区项目低分的影响较大\\n城市（事业部）层面，23年3季度累计共25个城市（事业部）参与调研，其中，共有10个城市（事业部）较22年3季度累计有提升，13个城市（事业部）较22年3季度累计有下滑，合肥、南昌表现相对较弱\\n项目层面，23年3季度累计调研共覆盖138个项目，其中，共有52个项目较22年3季度累计有提升，62个项目较22年3季度累计有下滑\\n北京未来金茂府项目的客户满意度较22年3季度累计进步最为明显，其次进步较明显的项目是北京大兴金茂悦\\n武汉华发金茂国际社区、成都锦江金茂府、武汉东湖金茂府、台州首开金茂金玉上城、武汉方岛金茂智慧科学城（岛外）、北京通州台湖金茂悦、南昌宸南里、成都武侯金茂府、无锡蠡湖金茂府、温州九山金茂府、杭州滨江金茂府、成都东叁金茂逸墅、武汉阳逻金茂逸墅、广州南沙灵山岛金茂湾、上海西郊金茂府、南昌九龙湖金茂悦、合肥北雁湖金茂湾、株洲金茂悦等项目在各个项目中排名靠后，且较22年3季度累计发生下滑，需关注";
//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n总体满意度显著提升。\\n销售阶段表现良好。\\n签约后等待阶段、交付阶段、居住阶段客户实际体验与期望落差大。";
//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n关键业务指标多有下滑\\n和标杆落差明显\\n质量、区内规划设计、交付服务、整改维修、投诉处理，明显薄弱，滑落至行业总体水平或以下";
//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n产品端：\\n房屋设计有所进步\\n房屋质量、园林绿化、区内规划设计连续两年下挫";
//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n服务端：\\n销售服务较19年进步，行业优势不明显\\n物业服务表现平稳，维持行业优良水平\\n其余指标发生不同程度的下滑\\n交付服务大幅下挫至行业总体以下\n";
//    String prompt = "请将换行符后面的每一行拼接成一句，并使用适当的连词以确保句子的连贯性和流畅性。请在不添加其他内容的情况下增加适当的连词，以确保句子之间的连贯性和流畅性。请注意，您的回答应该包括适当的连词和语法结构，以确保句子之间的连接自然和清晰。\\n总体满意度表现：\\n2020年越秀地产住宅客户满意度85%\\n位于行业60~70分位间\\n各业主类型前后两端维持高位\\n保内新交付项目表现走低";

    private static String PROMPT1_PREFIX = "我有几句话如下，帮我增加连接词，不要增加其他内容，变成一句，读起来更顺畅的话。这几句话是：";
    private static List<String> prompt1 = Lists.newArrayList();

    static {
        prompt1.add("总体满意度显著提升。销售阶段表现良好。签约后等待阶段、交付阶段、居住阶段客户实际体验与期望落差大。");
        prompt1.add("关键业务指标多有下滑，和标杆落差明显，质量、区内规划设计、交付服务、整改维修、投诉处理，明显薄弱，滑落至行业总体水平或以下");
        prompt1.add("产品端：房屋设计有所进步，房屋质量、园林绿化、区内规划设计连续两年下挫");
        prompt1.add("服务端：销售服务较19年进步，行业优势不明显，物业服务表现平稳，维持行业优良水平，其余指标发生不同程度的下滑，交付服务大幅下挫至行业总体以下");
        prompt1.add("总体满意度表现：2020年越秀地产住宅客户满意度85%，位于行业60~70分位间，各业主类型前后两端维持高位，保内新交付项目表现走低");
        prompt1.add("客户评价整体表现：客户满意度逐年提升，20年较19年提升5%，好于行业均值，与行业增长水平趋同");
        prompt1.add("业主全生命周期表现：准业主、老业主评价提升，磨合期新交付项目客户体验走低，关注两个节点（交付体验落差大于行业；磨1-磨2维修阶段体验持续下滑）");
        prompt1.add("从总部整体表现来看，满意度高分项目密集，但存在极端低分项目");
        prompt1.add("从区域表现来看：各区域内项目稳定性表现不均衡，湾中、湾东、华东存在项目表现落差大，湾中、湾东、华东、北方存在极端案例");
        prompt1.add("行业业务指标呈现3类趋势，上升-销售服务、签约后沟通服务、维修服务、物业服务提升显著，平稳-服务整体呈现上升趋势，房屋质量和交付服务均呈现下降趋势，预警-近三年房屋设计、园林绿化、区内规划设施表现较平稳");
        prompt1.add("业务指标表现：关键业务指标普遍提升，产品设计、销售服务、物业服务提升幅度大，产品交付环节的“房屋质量-交付表现-维修服务”对标行业显薄弱");
        prompt1.add("业主推荐意向高于总体满意度，与行业趋势相反，部分不满意的业主会向他人推荐国贸的楼盘，国贸的产品和服务精细度还不够，损失了一部分溢价空间");
        prompt1.add("随着入住时间推移，业主的推荐意向步衰退，国贸不采取措施改善老业主的居住体验，逐步流失已经获得的客户资源");
        prompt1.add("中国金茂总体由行业95分位降至80分位，准业主由行业80分位降至60分位，磨合期由行业95分位降至90分位，稳定期由行业95分位降至90分位，老业主由行业95分位降至65分位");
        prompt1.add("产品表现，各指标均表现优良，基本达到行业标杆水平，国贸产品优势明显");
        prompt1.add("服务表现，销售服务、投诉处理表现略低于行业总体，签约后服务、交付服务、维修服务提升幅度大，均显著超过行业总体水平，物业服务是目前国贸的唯一短板，相比15年虽有提升，但满意度仍然偏低");
        prompt1.add("23年3季度累计，中国金茂住宅客户总体满意度为90%，23年3季度累计较22年3季度累计增长0.14%\\n各个节点中，磨合期2样框占比最高\\n老业主较22年3季度累计退步最为明显\\n二级关键业务指标层面，物业服务、科技系统、园林绿化、区内规划和公区设施、房屋设计、交付服务、会员服务较22年3季度累计有所退步，其中，物业服务较22年3季度累计退步最为明显，需警惕\\n区域层面，23年3季度累计共6个区域参与23年3季度累计调研，其中，共有3个区域较22年3季度累计有提升，3个区域较22年3季度累计有下滑\\n金茂华北客户满意度领先于其他区域，客户主要集中于磨合期2节点\\n金茂华中客户满意度落后于其他区域，注意房屋设计指标的改进，金茂华中的客户评价较低，受武汉华发金茂国际社区项目低分的影响较大\\n城市（事业部）层面，23年3季度累计共25个城市（事业部）参与调研，其中，共有10个城市（事业部）较22年3季度累计有提升，13个城市（事业部）较22年3季度累计有下滑，合肥、南昌表现相对较弱\\n项目层面，23年3季度累计调研共覆盖138个项目，其中，共有52个项目较22年3季度累计有提升，62个项目较22年3季度累计有下滑\\n北京未来金茂府项目的客户满意度较22年3季度累计进步最为明显，其次进步较明显的项目是北京大兴金茂悦\\n武汉华发金茂国际社区、成都锦江金茂府、武汉东湖金茂府、台州首开金茂金玉上城、武汉方岛金茂智慧科学城（岛外）、北京通州台湖金茂悦、南昌宸南里、成都武侯金茂府、无锡蠡湖金茂府、温州九山金茂府、杭州滨江金茂府、成都东叁金茂逸墅、武汉阳逻金茂逸墅、广州南沙灵山岛金茂湾、上海西郊金茂府、南昌九龙湖金茂悦、合肥北雁湖金茂湾、株洲金茂悦等项目在各个项目中排名靠后，且较22年3季度累计发生下滑，需关注");
        prompt1.add("总体满意度低于行业总体9%，准业主、稳定期、老业主均大幅下挫且低于行业总体，老业主体量大，拉低了集团满意度得分，磨合期大幅提升，高于行业总体8%，表现良好。");
    }

    @Test
    public void testAll() {
        Map<String, List<String>> result = Maps.newHashMap();

        // result.put(BaiduModel.ERNIE_Bot_4_0.getValue(), baiduList(BaiduModel.ERNIE_Bot_4_0.getValue()));
        baiduList(BaiduModel.ERNIE_Bot.getValue());
        baiduList(BaiduModel.ERNIE_Bot_turbo.getValue());
        baiduList(BaiduModel.bloomz_7b1.getValue());
        baiduList(BaiduModel.qianfan_bloomz_7b_compressed.getValue());
        baiduList(BaiduModel.QIANFAN_CHINESE_LLAMA_2_7B.getValue());
        baiduList(BaiduModel.chatglm2_6b_32k.getValue());
        baiduList(BaiduModel.aquilachat_7b.getValue());

        aliList(AliConstants.QWEN_TURBO);
        aliList(AliConstants.QWEN_PLUS); // 评分最高
        aliList(AliConstants.QWEN_MAX);
        aliList(AliConstants.QWEN_7B_CHAT);
        aliList(AliConstants.QWEN_14B_CHAT);
//        aliList(AliConstants.LLAMA2_7B_CHAT_V2);
//        aliList(AliConstants.LLAMA2_13B_CHAT_V2);
    }

    @Test
    public void testBaiduModelOneByOne() {
        List<String> modelList = Lists.newArrayList();
        modelList.add("baidu###" + BaiduModel.ERNIE_Bot.getValue());
        modelList.add("baidu###" + BaiduModel.ERNIE_Bot_turbo.getValue());
        modelList.add("baidu###" + BaiduModel.bloomz_7b1.getValue());
        modelList.add("baidu###" + BaiduModel.qianfan_bloomz_7b_compressed.getValue());
        modelList.add("baidu###" + BaiduModel.QIANFAN_CHINESE_LLAMA_2_7B.getValue());
        modelList.add("baidu###" + BaiduModel.chatglm2_6b_32k.getValue());
        modelList.add("baidu###" + BaiduModel.aquilachat_7b.getValue());
        modelList.add("ali###" + AliConstants.QWEN_TURBO);
        modelList.add("ali###" + AliConstants.QWEN_PLUS);
        modelList.add("ali###" + AliConstants.QWEN_MAX);
        modelList.add("ali###" + AliConstants.QWEN_7B_CHAT);
        modelList.add("ali###" + AliConstants.QWEN_14B_CHAT);

        for (String text : modelList) {
            String[] array = text.split("###");
            String platform = array[0], model = array[1];
            for (int i = 0; i < prompt1.size(); i++) {
                String prompt = prompt1.get(i);
                System.out.println("--- [" + (i + 1) + "] " + prompt);

                if (platform.equals("baidu")) {
                    testOneBaiduModel(model, 0.8f, PROMPT1_PREFIX + prompt);
                    testOneBaiduModel(model, 0.1f, PROMPT1_PREFIX + prompt);
                } else {
                    // 使用相同 seed
                    testOneAliModel(model, 1f, PROMPT1_PREFIX + prompt);
                    testOneAliModel(model, 0.1f, PROMPT1_PREFIX + prompt);
                }

            }

            System.out.println("#############################################################################");
            System.out.println("#############################################################################");
            System.out.println("#############################################################################");
            System.out.println("#############################################################################");
            System.out.println("#############################################################################");
        }
    }

    public void testOneBaiduModel(String model, float temperature, String prompt) {
        BaiduApi baiduApi = new BaiduApi(temperature);
        System.out.println("百度：" + model);
        System.out.println((temperature == 0.8f ? "Default " : "") + "Temperature: " + temperature);
        for (int j = 0; j < 5; j++) {
            System.out.println("第" + (j + 1) + "次：" + baiduApi.chat(model, prompt));
        }
    }

    public void testOneAliModel(String model, Float temperature, String prompt) {
        AliApi aliApi = new AliApi(temperature);
         System.out.println("阿里：" + model);
         System.out.println((temperature == 1f ? "Default " : "") + "Temperature: " + temperature);
        for (int j = 0; j < 5; j++) {
            System.out.println("第" + (j + 1) + "次：" + aliApi.chat(model, prompt));
        }
    }

    @Test
    public void testOneWithDifferentTemperature() {
        String model = AliConstants.QWEN_PLUS;
        List<Float> temperatures = Lists.newArrayList(0.1f, 0.3f, 0.5f, 0.7f);
        for (int i = 0; i < prompt1.size(); i++) {
            String prompt = prompt1.get(i);
            System.out.println("--- [" + (i + 1) + "] " + prompt);
            prompt = PROMPT1_PREFIX + prompt;
            for (Float temperature : temperatures) {
                AliApi aliApi = new AliApi(temperature);
                System.out.println("阿里：" + model + " - Temperature: " + temperature +  ": " + aliApi.chat(model, prompt));
            }
        }
    }

    //    @Test
    public void testBaiduApi() {
        BaiduApi baiduApi = new BaiduApi();
        System.out.println(BaiduModel.ERNIE_Bot_4_0.getValue() + "：" + baiduApi.chat(BaiduModel.ERNIE_Bot_4_0.getValue(), prompt));
        System.out.println(BaiduModel.ERNIE_Bot.getValue() + "：" + baiduApi.chat(BaiduModel.ERNIE_Bot.getValue(), prompt));
        System.out.println(BaiduModel.ERNIE_Bot_turbo.getValue() + "：" + baiduApi.chat(BaiduModel.ERNIE_Bot_turbo.getValue(), prompt));
        System.out.println(BaiduModel.bloomz_7b1.getValue() + "：" + baiduApi.chat(BaiduModel.bloomz_7b1.getValue(), prompt));
        System.out.println(BaiduModel.qianfan_bloomz_7b_compressed.getValue() + "：" + baiduApi.chat(BaiduModel.qianfan_bloomz_7b_compressed.getValue(), prompt));
        System.out.println(BaiduModel.QIANFAN_CHINESE_LLAMA_2_7B.getValue() + "：" + baiduApi.chat(BaiduModel.QIANFAN_CHINESE_LLAMA_2_7B.getValue(), prompt));
        System.out.println(BaiduModel.chatglm2_6b_32k.getValue() + "：" + baiduApi.chat(BaiduModel.chatglm2_6b_32k.getValue(), prompt));
        System.out.println(BaiduModel.aquilachat_7b.getValue() + "：" + baiduApi.chat(BaiduModel.aquilachat_7b.getValue(), prompt));
    }

    //    @Test
    public void testAliApi() {
        AliApi api = new AliApi();
        for (int i = 0; i < 5; i++) {
            System.out.println(AliConstants.QWEN_TURBO + ": " + api.chat(AliConstants.QWEN_TURBO, prompt));
            System.out.println(AliConstants.QWEN_PLUS + ": " + api.chat(AliConstants.QWEN_PLUS, prompt));
            System.out.println(AliConstants.QWEN_MAX + ": " + api.chat(AliConstants.QWEN_MAX, prompt));
            System.out.println(AliConstants.QWEN_7B_CHAT + ": " + api.chat(AliConstants.QWEN_7B_CHAT, prompt));
            System.out.println(AliConstants.QWEN_14B_CHAT + ": " + api.chat(AliConstants.QWEN_14B_CHAT, prompt));
        }
    }

    @NotNull
    private void baiduList(String model) {
        BaiduApi baiduApi = new BaiduApi();
        System.out.println("百度：" + model);
        for (int i = 0; i < 5; i++) {
            System.out.println("第" + (i + 1) + "次：" + baiduApi.chat(model, prompt));
        }
    }

    @NotNull
    private void aliList(String model) {
        AliApi api = new AliApi();
        System.out.println("阿里：" + model);
        for (int i = 0; i < 5; i++) {
            System.out.println("第" + (i + 1) + "次：" + api.chat(model, prompt));
        }
    }


}
