package pub.yuntu.superbrain.ai;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import pub.yuntu.api.ai.Alibaba.AliApi;
import pub.yuntu.api.ai.Alibaba.constants.AliConstants;
import pub.yuntu.superbrain.gpt.gateway.text.CommandTextParser;

/**
 * 通过 AI 接口解析开放文本测试
 *
 * <AUTHOR>
 * @createTime 2024年08月01日 09:05:49
 */
@Slf4j
public class AiTextAnalyzeTest extends TestCase {

    String prompt = "2024 年第一季度的交付客户体验怎么样？";

//    private static String PROMPT1_PREFIX = "假设你是一个优秀的自然语言解析器，能够从一段文本中精准提取关键信息。\n" +
//            "我会提供一段文本以及需要提取的信息维度列表。\n" +
//            "你的任务是从文本中提取我指定的关键信息维度，如果某个维度没有提取到关键信息，则返回“无”。\n" +
//            "\n" +
//            "下面是指定的关键信息维度：\n" +
//            KeyInfo.DATA_PERIOD_DIMENSION_KEY + "：" + "指示时间的词汇或短语，比如“今年”、“去年”、“本年”、“这个季度”、“第三季度”、“本月”、“最近三个月”等,将时间描述转换为指定的时间格式，比如：“今年” 转为 ”2024-0112”，“本年” 转为 ”2024-0112”，“去年” 转为 ”2023-0112”，“这个季度” 转为 ”2024-0810”，“第三季度” 转为 ”2024-0810”，“本月” 转为 ”2024-08”，“最近三个月” 转为 ”2024-0608”。" + "。\n" +
//            CustomerKeyInfo.CUSTOMER_DIMENSION_KEY + "：" + CustomerKeyInfo.CUSTOMER_DIMENSION_DESC + "。\n" +
//            CustomerKeyInfo.SPLIT_DIMENSION_KEY + "：" + CustomerKeyInfo.SPLIT_DIMENSION_DESC + "。\n" +
//            IndustryKeyInfo.INDUSTRY_DIMENSION_KEY + "：" + IndustryKeyInfo.INDUSTRY_DIMENSION_DESC + "。\n" +
//            "\n" +
//            "下面是待分析的文本：\n" +
//            "%s\n" +
//            "\n" +
//            "请根据提供的文本和信息维度列表，提取对应的关键信息。返回的结果只保留维度解析结果，每个维度一行，不要返回其他信息";

    private static String PROMPT_TEMPLATE = "假设你是一个优秀的自然语言解析器，能够从一段文本中精准提取关键信息。\n" +
            "我会提供一段文本以及需要提取的信息维度列表。\n" +
            "你的任务是从文本中提取我指定的关键信息维度，并按指定规则转换。如果某个维度没有提取到关键信息，则返回“无”。\n" +
            "\n" +
            "指定的关键信息维度\n" +
            "\n" +
            "Dimension_时间维度：指示时间的词汇或短语，如“今年”、“去年”、“本年”、“这个季度”、“第三季度”、“本月”、“最近三个月”等。\n" +
            "\n" +
            "转换规则如下：\n" +
            "年、年度：使用四位数字格式 yyyy\n" +
            "季度：使用四位数字格式 yyyy-qq，第一季度为 0103，第二季度为 0406，第三季度为 0709，第四季度为 1012\n" +
            "月、月度：使用两位数字 mm，月度数字小于10时，使用 0 补齐，如 10 月显示 10, 8 月显示 08\n" +
            "转换示例：\n" +
            "“今年” 转为 \"2024-0112\"\n" +
            "“本年” 转为 \"2024-0112\"\n" +
            "“去年” 转为 \"2023-0112\"\n" +
            "“这个季度” 转为 \"2024-0810\"\n" +
            "“第三季度” 转为 \"2024-0810\"\n" +
            "“本月” 转为 \"2024-08\"\n" +
            "“最近三个月” 转为 \"2024-0608\"\n" +
            "Dimension_客户维度：指中国房地产开发商的名称或简写，如“中国金茂”、“招商”、“万科”等。\n" +
            "Dimension_切分维度：指切分单位或对象，如“集团”、“区域”、“公司”、“城市”、“项目”、“分期”，或者具体指明的“xx区域”、“xx公司”、“xx城市”、“xx项目”、“xx分期”等。\n" +
            "Dimension_行业维度：指行业的整体或特定方面，如“行业城市”、“行业标杆”、“行业总体”等。\n" +
            "\n" +
            "待分析的文本\n" +
            "\n" +
            "%s\n" +
            "\n" +
            "提取规则\n" +
            "\n" +
            "结果只保留维度解析结果，每个维度一行，不要返回其他信息。\n" +
            "\n" +
            "\n" +
            "请根据提供的文本和信息维度列表，提取对应的关键信息。";


    @Test
    public void testAll() {

        prompt = String.format(PROMPT_TEMPLATE, prompt);
        System.out.println(prompt);

//        aliList(AliConstants.QWEN_TURBO);
        aliList(AliConstants.QWEN_PLUS); // 评分最高
//        aliList(AliConstants.QWEN_MAX);
//        aliList(AliConstants.QWEN_7B_CHAT);
//        aliList(AliConstants.QWEN_14B_CHAT);
    }

    @NotNull
    private void aliList(String model) {
        AliApi api = new AliApi();
        System.out.println("#############################################################################");
        System.out.println("#############################################################################");
        System.out.println("阿里：" + model);
        for (int i = 0; i < 5; i++) {
            System.out.println("\n第" + (i + 1) + "次：\n" + api.chat(model, prompt));
        }
    }

    @Test
    public void testTextParser() {
        CommandTextParser.askAiDimensions(prompt, "maven");
    }
}
