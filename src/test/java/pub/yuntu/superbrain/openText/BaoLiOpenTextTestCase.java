package pub.yuntu.superbrain.openText;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.foundation.excel.ExcelEngine;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.*;
import pub.yuntu.superbrain.openText.dto.DataReadDTO;
import pub.yuntu.superbrain.openText.dto.TouSuReadDTO;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.KMP.OPEN_TYPE_TEXT;

/**
 * <AUTHOR>
 * @Description 保利开放文本解析
 * @createTime 2023年02月24日 22:50:57
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BaoLiOpenTextTestCase {

    // 最终保留的触点
    public static final Map<String, String> KEEP_CODE_MAP = Maps.newHashMap();

    static {
        KEEP_CODE_MAP.put("教育配套承诺", "307003");
        KEEP_CODE_MAP.put("周边道路&", "730303003001");
        KEEP_CODE_MAP.put("交通四至", "730307002007");
//        KEEP_CODE_MAP.put("维修", "841");
//        KEEP_CODE_MAP.put("维修效果", "730201005012");
//        KEEP_CODE_MAP.put("维修效率", "730201005003");
//        KEEP_CODE_MAP.put("维修进度", "606002");
//        KEEP_CODE_MAP.put("维修人员", "505");
//        KEEP_CODE_MAP.put("维修服务", "407");

        KEEP_CODE_MAP.put("诚信", "826");
        KEEP_CODE_MAP.put("车库", "203003");
        KEEP_CODE_MAP.put("出入口", "201004");
        KEEP_CODE_MAP.put("墙", "308002");
        KEEP_CODE_MAP.put("地板", "314003");
        KEEP_CODE_MAP.put("瓷地砖", "314001");
        KEEP_CODE_MAP.put("渗漏", "106001001");

        KEEP_CODE_MAP.put("园林景观", "808");
        KEEP_CODE_MAP.put("绿化率", "303006");
        KEEP_CODE_MAP.put("景观", "304002");
        KEEP_CODE_MAP.put("绿化", "304001");
    }

    @Autowired
    KMP kmp;
    @Autowired
    TextParser textParser;
    @Autowired
    TextLoader textLoader;

    @Test
    public void run() {
        Stopwatch started = Stopwatch.createStarted();
        BaoLiOpenTextParam param = zDataParam();
        log.info("开始解析保利开放文本，文件：{}", param.getSourceExcelPath());

        XSSFWorkbook sourceWb = loadSourceExcel(param);
        copySourceInfo(param, sourceWb);
        parseOpenTextInDesExcel(param, param.getDestinationWb());
        writeDestination(param);
        closeExcel(sourceWb);
        closeExcel(param.getDestinationWb());

        started.stop();

        log.info("---------------------");
        log.info("解析保利开放文本全部完成，：{}分钟 ({}秒)", started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
        log.info("---------------------");
    }

    public static BaoLiOpenTextParam zDataParam() {
        return BaoLiOpenTextParam.builder()
                .sourceExcelPath("/Users/<USER>/100_Work_工作/110 Work FG | 赛惟/116 Work FG Document | 文档/项目/保利/2023-02|保利驻场400/开放文本分析/保利发展2022年客户满意度调研原始数据-最终算分版-20230216（原始）.xlsx")
                .destinationTitles(Lists.newArrayList("encrypt", "FG唯一码", "分期", "org_one_1_unit_name", "org_one_2_unit_name", "org_one_3_unit_name", "org_one_4_unit_name", "org_one_5_unit_name", "org_one_6_unit_name", "业主类型", "业主类型(3211)", "房屋信息", "A1b"))
                .titleRowNo(1)
                .startRowNo(1)
                .openTextTitle("A1b")
                .readDto(DataReadDTO.class)
                .logType("z_data")
                .destinationWb(new XSSFWorkbook())
                .additionalTitleColNums(Lists.newArrayList())
                .additionalTitles(Lists.newArrayList())
                .sheetNameOfProjectStat("统计-按项目")
                .statByProjectTitles(Lists.newArrayList("org_one_4_unit_name", "org_one_5_unit_name"))
                .sheetNameOfInteractionStat("统计-按分类")
                .statByInteractionTitles(Lists.newArrayList("问题", "总提及", "公司提及数量", "项目提及数量"))
                .build();
    }

    public static BaoLiOpenTextParam touSuParam() {
        return BaoLiOpenTextParam.builder()
                .sourceExcelPath("/Users/<USER>/100_Work_工作/110 Work FG | 赛惟/116 Work FG Document | 文档/项目/保利/2023-02|保利驻场400/开放文本分析/投诉应关闭问题处理明细表（删除作废）.xlsx")
//                .sourceExcelPath("/Users/<USER>/100_Work_工作/110 Work FG | 赛惟/116 Work FG Document | 文档/项目/保利/2023-02|保利驻场400/开放文本分析/simple.xlsx")
                .destinationTitles(Lists.newArrayList("接待编号", "公司", "项目名称", "问题描述", "问题类型"))
                .titleRowNo(1)
                .startRowNo(2)
                .openTextTitle("问题描述")
                .readDto(TouSuReadDTO.class)
                .logType("投诉")
                .destinationWb(new XSSFWorkbook())
                .additionalTitleColNums(Lists.newArrayList())
                .additionalTitles(Lists.newArrayList())
                .sheetNameOfProjectStat("统计-按项目")
                .statByProjectTitles(Lists.newArrayList("公司", "项目名称"))
                .sheetNameOfInteractionStat("统计-按分类")
                .statByInteractionTitles(Lists.newArrayList("问题", "总提及", "公司提及数量", "项目提及数量"))
                .build();
    }

    private XSSFWorkbook loadSourceExcel(BaoLiOpenTextParam param) {
        log.info("加载数据源Excel...");
        Stopwatch started = Stopwatch.createStarted();

        Path path = Paths.get(param.getSourceExcelPath());
        XSSFWorkbook source = null;
        try {
            source = new XSSFWorkbook(Files.newInputStream(path));
        } catch (IOException e) {
            log.error("加载数据源Excel发生错误，path：{}", param.getSourceExcelPath(), e);
        }

        started.stop();
        log.info("加载数据源Excel完成，用时：{}s", started.elapsed(TimeUnit.SECONDS));
        return source;
    }

    private void copySourceInfo(BaoLiOpenTextParam param, XSSFWorkbook sourceWb) {
        Stopwatch started = Stopwatch.createStarted();

        XSSFSheet sourceSheet = sourceWb.getSheetAt(0);
        List<Integer> sourceColNos = sourceColNos(param, sourceSheet); // destinationTitles在数据源Excel表头的列号
        writeDestinationTitle(param);
        XSSFSheet destinationSheet = param.getDestinationWb().getSheetAt(0);

        int lastRowNum = sourceSheet.getLastRowNum();
        for (int rowNo = param.getStartRowNo(); rowNo <= lastRowNum; rowNo++) {
            XSSFRow sourceRow = sourceSheet.getRow(rowNo);
            XSSFRow desRow = destinationSheet.createRow(rowNo);

            for (int desColNo = 0; desColNo < sourceColNos.size(); desColNo++) {
                Integer sourceColNo = sourceColNos.get(desColNo);
                XSSFCell sourceCell = sourceRow.getCell(sourceColNo);
                XSSFCell desCell = desRow.createCell(desColNo);

                String sourceCellValue = "";
                if (null != sourceCell) {
                    sourceCellValue = sourceCell.getStringCellValue();
                }
                desCell.setCellValue(sourceCellValue);
            }

            if (rowNo % 10000 == 0) {
                log.info("拷贝数据源Excel第{}行", rowNo);
            }
        }

        started.stop();
        log.info("拷贝数据源Excel关键列完成，用时：{}s", started.elapsed(TimeUnit.SECONDS));
    }

    private List<Integer> sourceColNos(BaoLiOpenTextParam param, XSSFSheet sourceSheet) {
        List<Integer> sourceColNos = Lists.newArrayList();
        XSSFRow titleRow = sourceSheet.getRow(param.getTitleRowNo());
        short lastCellNum = titleRow.getLastCellNum();
        for (int colNo = 0; colNo < lastCellNum; colNo++) {
            XSSFCell cell = titleRow.getCell(colNo);
            String cellValue = cell.getStringCellValue();
            for (String expectTitle : param.getDestinationTitles()) {
                if (StringUtils.equals(cellValue, expectTitle)) {
                    sourceColNos.add(colNo);
                }
            }
        }
        log.info("待处理的列号：{}", sourceColNos.stream().map(i -> i + "").collect(Collectors.joining(", ")));
        return sourceColNos;
    }

    public static void writeDestinationTitle(BaoLiOpenTextParam param) {
        XSSFSheet desSheet = param.getDestinationWb().createSheet();
        XSSFRow desTitleRow = desSheet.createRow(0);
        for (int colNo = 0; colNo < param.getDestinationTitles().size(); colNo++) {
            String title = param.getDestinationTitles().get(colNo);
            XSSFCell cell = desTitleRow.createCell(colNo);
            cell.setCellValue(title);
        }
    }

    private void parseOpenTextInDesExcel(BaoLiOpenTextParam param, XSSFWorkbook workbook) {
        Stopwatch started = Stopwatch.createStarted();

        int openTextColNo = param.getDestinationTitles().indexOf(param.getOpenTextTitle());
        XSSFSheet sheet = workbook.getSheetAt(0);
        for (int rowNo = 1; rowNo < sheet.getLastRowNum(); rowNo++) {
            XSSFRow row = sheet.getRow(rowNo);
            if (null == row) continue;
            XSSFCell textCell = row.getCell(openTextColNo);
            String openText = textCell.getStringCellValue();

            List<Interaction> interactions = parseText(openText);
            writePassResult(param, sheet, row, interactions);

            // 值为Null的单元格，用空字符串替换
            replaceNullToEmpty(row);
        }

        started.stop();
        log.info("解析开放文本完成，用时：{}s", started.elapsed(TimeUnit.SECONDS));
    }

    public static void writePassResult(BaoLiOpenTextParam param, XSSFSheet sheet, XSSFRow row,
                                       List<Interaction> interactions) {
        List<String> interactionNames = interactions.stream()
                .map(BaoLiOpenTextTestCase::interactionName)
                .collect(Collectors.toList());
        interactionNames.forEach(name -> {
            if (param.getDestinationTitles().contains(name)) return;
            writeColumnTitle(param, sheet, name);
        });
        interactions.forEach(interaction -> {
            String name = interactionName(interaction);
            int colNo = param.getDestinationTitles().indexOf(name);
            XSSFCell cell = row.getCell(colNo);
            if (null == cell) {
                cell = row.createCell(colNo);
            }
            cell.setCellValue("Y");
        });
    }

    private static String interactionName(Interaction interaction) {
        return String.join("|", interaction.getName(), interaction.getCode());
    }

    private List<Interaction> parseText(String text) {
        // personInfo 按业主类型生成
        List<MatchingGroup> matchingGroups = textParser.parse(text, true, null, OPEN_TYPE_TEXT, false);
        return textParser.parseInteractions(matchingGroups, KMP.OPEN_TYPE_TEXT, KEEP_CODE_MAP);
    }

    private static void writeColumnTitle(BaoLiOpenTextParam param, XSSFSheet sheet, String title) {
        XSSFRow row = sheet.getRow(0);
        int colNo = param.getDestinationTitles().size();
        XSSFCell cell = row.createCell(colNo);
        cell.setCellValue(title);
        param.getDestinationTitles().add(title);
        param.getAdditionalTitleColNums().add(colNo);
        param.getAdditionalTitles().add(title);
    }

    private static void replaceNullToEmpty(XSSFRow row) {
        short lastCellNum = row.getLastCellNum();
        for (int colNo = 0; colNo < lastCellNum; colNo++) {
            XSSFCell cell = row.getCell(colNo);
            if (null == cell) continue;
            String cellValue = cell.getStringCellValue();
            if (StringUtils.equals(cellValue, "null")) {
                cell.setCellValue("");
            }
        }
    }

    public static void autoColWith(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        log.info("开始设置Excel自适应列宽");

        XSSFSheet sheet = param.getDestinationWb().getSheetAt(0);
//        for (int colNo = 0; colNo < param.getDestinationTitles().size(); colNo++) { // 所有列自适应列宽，非常耗时
        for (int colNo = 0; colNo < param.getAdditionalTitleColNums().size(); colNo++) { // 只有开放文本分类的列自适应列宽
            sheet.autoSizeColumn(colNo, true);
        }

        started.stop();
        log.info("设置Excel自适应列宽完成，用时：{}分钟 ({}秒)", started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

    public static void writeDestination(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        log.info("开始将Excel Workbook写入文件");
        // 创建文件
        String filePath = destinationExcelPath(param);
        try {
            Path path = Paths.get(filePath);
            Files.createFile(path);
            ExcelEngine.writeAExcel(filePath, param.getDestinationWb());
        } catch (IOException e) {
            log.info("结果Excel写入磁盘发生错误", e);
        }
        started.stop();
        log.info("Excel Workbook写入文件完成，用时：{}分钟 ({}秒)", started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

    public static String destinationExcelPath(BaoLiOpenTextParam param) {
        String dt = DateUtil.TimeStamp2Date(System.currentTimeMillis() + "", "yyyyMMdd_HHmm");
        String replacement = "_开放文本解析结果_" + dt + ".xlsx";
        return param.getSourceExcelPath().replace(".xlsx", replacement);
    }

    public static void closeExcel(XSSFWorkbook workbook) {
        if (null == workbook) return;
        try {
            workbook.close();
        } catch (IOException e) {
            log.error("关闭Excel Workbook发生错误", e);
        }
    }

}
