package pub.yuntu.superbrain.openText;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Description 纯文本解析解析，无Excel环境
 * @createTime 2023年02月25日 22:27:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PureTextTestCase {

    @Autowired
    KMP kmp;
    @Autowired
    TextParser textParser;
    @Autowired
    TextLoader textLoader;

    @Test
    public void run() {
        List<String> list;

        log.info("-------------------- 绿化|304001");
        list = Lists.newArrayList(
                "人员不负责 业主来电反馈之前投诉关于绿化修建两层高的发电站的问题，有一位姓严的工作人员进行跟进回复，业主对于工作人员解决问题的态度进行投诉，认为该工作人员对于解决问题的态度不好。还有之前报修的关于太阳能无法使用的问题（太阳能不能用一户（将近600户）也在一直没有解决，业主希望相关人员两日之内给自己一个答复，不要姓严的工作人员进行跟进处理，要求换个工作人员进行回复。；"
        );
        list.forEach(this::parseText);
        System.out.println("\n");

        /*log.info("-------------------- 道路开通&|730307002007");
        list = Lists.newArrayList(
                "维修速度慢 业主反映前面报修的‘主卧马桶堵塞 业主要求拆马桶疏通’问题来了三拨人都是看看，也没维修；报修的‘电视线路不通，只能看无线电的节目’一直无人维修，业主反映再过几天就外出不在家了，要求尽快维修。；"
        );
        list.forEach(this::parseText);
        System.out.println("\n");*/
    }

    public void parseText(String text) {
        List<MatchingGroup> matchingGroups = textParser.parse(text, true, null, KMP.OPEN_TYPE_TEXT, false);
        List<Interaction> interactions = textParser.parseInteractions(matchingGroups, KMP.OPEN_TYPE_TEXT, BaoLiOpenTextTestCase.KEEP_CODE_MAP);

        log.info("解析字符串：{}", text);
        interactions.forEach(interaction -> log.info("{}", interaction));
        log.info("--------------------");
    }

}
