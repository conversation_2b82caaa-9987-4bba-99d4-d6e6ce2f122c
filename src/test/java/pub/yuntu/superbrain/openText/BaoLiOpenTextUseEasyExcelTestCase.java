package pub.yuntu.superbrain.openText;

import com.alibaba.excel.EasyExcel;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.assertj.core.util.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.KMP;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.TextLoader;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.TextParser;
import pub.yuntu.superbrain.openText.listener.DataDtoListener;
import pub.yuntu.superbrain.openText.listener.TouSuDtoListener;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static pub.yuntu.superbrain.openText.BaoLiOpenTextTestCase.*;

/**
 * <AUTHOR>
 * @Description 使用 EasyExcel 处理保利开放文本解析
 * @createTime 2023年02月25日 14:20:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BaoLiOpenTextUseEasyExcelTestCase {

    @Autowired
    KMP kmp;
    @Autowired
    TextParser textParser;
    @Autowired
    TextLoader textLoader;

    @Test
    public void testOpenText() {
        // 处理满意度原始数据
        run(zDataParam());
        // 处理投诉数据
        run(touSuParam());
    }

    private void run(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        log.info("[{}] 开始解析保利开放文本，文件：{}", param.getLogType(), param.getSourceExcelPath());

        writeDestinationTitle(param);
        parse(param); // 解析开放文本分类
        statistics(param); // 统计
        // autoColWith(param); 耗时太久
        writeDestination(param);
        closeExcel(param.getDestinationWb());

        started.stop();
        log.info("[{}] 解析保利开放文本全部完成，用时：{}分钟 ({}秒)\n---------------------",
                param.getLogType(), started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

    private void parse(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        switch (param.getLogType()) {
            case "z_data":
                DataDtoListener ddListener = new DataDtoListener();
                ddListener.setKmp(kmp);
                ddListener.setTextParser(textParser);
                ddListener.setParam(param);
                ddListener.setStarted(started);
                EasyExcel.read(param.getSourceExcelPath(), param.getReadDto(), ddListener).sheet().doRead();
                break;
            case "投诉":
                TouSuDtoListener tsdListener = new TouSuDtoListener();
                tsdListener.setKmp(kmp);
                tsdListener.setTextParser(textParser);
                tsdListener.setParam(param);
                tsdListener.setStarted(started);
                EasyExcel.read(param.getSourceExcelPath(), param.getReadDto(), tsdListener).sheet()
                        // 标题在第二行
                        .headRowNumber(2).doRead();
                break;
            default:
                break;
        }
    }

    private void statistics(BaoLiOpenTextParam param) {
        createSheetByProject(param);
        statByProject(param);
        createSheetByInteraction(param);
        statByInteraction(param);
    }

    private void createSheetByProject(BaoLiOpenTextParam param) {
        XSSFSheet sheet = param.getDestinationWb().createSheet(param.getSheetNameOfProjectStat());
        // 创建 标题 行
        XSSFRow titleRow = sheet.createRow(0);
        // 固定列
        for (int colNo = 0; colNo < param.getStatByProjectTitles().size(); colNo++) {
            String title = param.getStatByProjectTitles().get(colNo);
            XSSFCell cell = titleRow.createCell(colNo);
            cell.setCellValue(title);
        }
        // 触点列
        for (int i = 0; i < param.getAdditionalTitleColNums().size(); i++) {
            String title = param.getAdditionalTitles().get(i);
            XSSFCell cell = titleRow.createCell(titleRow.getLastCellNum());
            cell.setCellValue(title + "(数量)");
            param.getStatByProjectTitles().add(title);
        }
        // 自适应列宽
        for (int colNo = 0; colNo < titleRow.getLastCellNum(); colNo++) {
            sheet.autoSizeColumn(colNo, true);
        }
    }

    private void statByProject(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        log.info("[{}] 开始按项目统计", param.getLogType());

        // 统计结果保留在Map中
        //             公司                   项目         触点      计数
        LinkedHashMap<String, LinkedHashMap<String, Map<String, Integer>>> statResult = Maps.newLinkedHashMap();

        // 开始统计
        int companyColNo = param.getDestinationTitles().indexOf(param.getStatByProjectTitles().get(0));
        int projectColNo = param.getDestinationTitles().indexOf(param.getStatByProjectTitles().get(1));
        List<Integer> interactionColNos = Lists.newArrayList();
        for (int i = 0; i < param.getAdditionalTitles().size(); i++) {
            String additionTitle = param.getAdditionalTitles().get(i);
            int colNo = param.getDestinationTitles().indexOf(additionTitle);
            interactionColNos.add(colNo);
        }
        XSSFSheet sourceSheet = param.getDestinationWb().getSheetAt(0);
        for (int rowNo = 1; rowNo < sourceSheet.getLastRowNum(); rowNo++) {
            XSSFRow row = sourceSheet.getRow(rowNo);

            String company = row.getCell(companyColNo).getStringCellValue();
            statResult.putIfAbsent(company, Maps.newLinkedHashMap());
            LinkedHashMap<String, Map<String, Integer>> projectMap = statResult.get(company);

            String project = row.getCell(projectColNo).getStringCellValue();
            projectMap.putIfAbsent(project, Maps.newHashMap());
            Map<String, Integer> interactionMap = projectMap.get(project);

            for (Integer colNo : interactionColNos) {
                String interactionName = param.getDestinationTitles().get(colNo);
                XSSFCell cell = row.getCell(colNo);
                if (null == cell) continue;
                String interactionValue = cell.getStringCellValue();
                interactionMap.putIfAbsent(interactionName, 0);
                Integer count = interactionMap.get(interactionName);

                if (StringUtils.equalsIgnoreCase(interactionValue, "Y")) {
                    interactionMap.put(interactionName, count + 1);
                }
            }
        }

        // 写入统计结果
        XSSFSheet statSheet = param.getDestinationWb().getSheet(param.getSheetNameOfProjectStat());
        XSSFCellStyle numStyle = param.destinationWb.createCellStyle();
        numStyle.setDataFormat(param.destinationWb.createDataFormat().getFormat("0"));
        int rowNo = 1;
        for (Map.Entry<String, LinkedHashMap<String, Map<String, Integer>>> companyEntry : statResult.entrySet()) {
            String company = companyEntry.getKey();
            LinkedHashMap<String, Map<String, Integer>> projectMap = companyEntry.getValue();
            for (Map.Entry<String, Map<String, Integer>> projectEntry : projectMap.entrySet()) {
                String project = projectEntry.getKey();
                // 每个Project一行
                XSSFRow row = statSheet.createRow(rowNo);
                XSSFCell companyCell = row.createCell(0);
                companyCell.setCellValue(company);
                XSSFCell projectCell = row.createCell(1);
                projectCell.setCellValue(project);
                Map<String, Integer> interactionMap = projectEntry.getValue();
                for (Map.Entry<String, Integer> interactionEntry : interactionMap.entrySet()) {
                    String interactionName = interactionEntry.getKey();
                    Integer count = interactionEntry.getValue();
                    int colNo = param.getStatByProjectTitles().indexOf(interactionName);
                    XSSFCell cell = row.createCell(colNo);
                    cell.setCellStyle(numStyle);
                    cell.setCellValue(count);
                }
                rowNo++;
            }
        }

        started.stop();
        log.info("[{}] 按项目统计完成，用时：{}分钟 ({}秒)",
                param.getLogType(), started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

    private void createSheetByInteraction(BaoLiOpenTextParam param) {
        XSSFSheet sheet = param.getDestinationWb().createSheet(param.getSheetNameOfInteractionStat());
        // 创建 标题 行
        XSSFRow titleRow = sheet.createRow(0);
        for (int colNo = 0; colNo < param.getStatByInteractionTitles().size(); colNo++) {
            String title = param.getStatByInteractionTitles().get(colNo);
            XSSFCell cell = titleRow.createCell(colNo);
            cell.setCellValue(title);
        }
        // 自适应列宽
        for (int colNo = 0; colNo < titleRow.getLastCellNum(); colNo++) {
            sheet.autoSizeColumn(colNo, true);
        }
    }

    private void statByInteraction(BaoLiOpenTextParam param) {
        Stopwatch started = Stopwatch.createStarted();
        log.info("[{}] 开始按分类统计", param.getLogType());

        // 统计结果保留在Map中
        //             触点     计数
        LinkedHashMap<String, Integer> totalMap = Maps.newLinkedHashMap();
        //             触点         公司
        LinkedHashMap<String, Set<String>> companyMap = Maps.newLinkedHashMap();
        //             触点         项目
        LinkedHashMap<String, Set<String>> projectMap = Maps.newLinkedHashMap();

        // 开始统计
        int companyColNo = param.getDestinationTitles().indexOf(param.getStatByProjectTitles().get(0));
        int projectColNo = param.getDestinationTitles().indexOf(param.getStatByProjectTitles().get(1));
        List<Integer> interactionColNos = Lists.newArrayList();
        for (int i = 0; i < param.getAdditionalTitles().size(); i++) {
            String additionTitle = param.getAdditionalTitles().get(i);
            int colNo = param.getDestinationTitles().indexOf(additionTitle);
            interactionColNos.add(colNo);
        }
        XSSFSheet sourceSheet = param.getDestinationWb().getSheetAt(0);
        for (int rowNo = 1; rowNo < sourceSheet.getLastRowNum(); rowNo++) {
            XSSFRow row = sourceSheet.getRow(rowNo);

            String company = row.getCell(companyColNo).getStringCellValue();
            String project = row.getCell(projectColNo).getStringCellValue();
            for (Integer colNo : interactionColNos) {
                String interactionName = param.getDestinationTitles().get(colNo);
                XSSFCell cell = row.getCell(colNo);
                if (null == cell) continue;
                String interactionValue = cell.getStringCellValue();
                if (StringUtils.isBlank(interactionValue)) continue;

                totalMap.putIfAbsent(interactionName, 0);
                totalMap.put(interactionName, totalMap.get(interactionName) + 1);

                companyMap.putIfAbsent(interactionName, Sets.newHashSet());
                companyMap.get(interactionName).add(company);

                projectMap.putIfAbsent(interactionName, Sets.newHashSet());
                projectMap.get(interactionName).add(project);
            }
        }

        // 写入统计结果
        XSSFSheet statSheet = param.getDestinationWb().getSheet(param.getSheetNameOfInteractionStat());
        XSSFCellStyle numStyle = param.destinationWb.createCellStyle();
        numStyle.setDataFormat(param.destinationWb.createDataFormat().getFormat("0"));
        int rowNo = 1;
        for (Map.Entry<String, Integer> totalEntry : totalMap.entrySet()) {
            String interactionName = totalEntry.getKey();
            int totalCount = totalEntry.getValue();

            Set<String> companySet = companyMap.get(interactionName);
            int companyCount = companySet.size();

            Set<String> projectSet = projectMap.get(interactionName);
            int projectCount = projectSet.size();

            XSSFRow row = statSheet.createRow(rowNo);
            XSSFCell interactionNameCell = row.createCell(0);
            interactionNameCell.setCellValue(interactionName);
            XSSFCell totalCountCell = row.createCell(1);
            if (totalCount == 0) {
                totalCountCell.setCellValue("");
            } else {
                totalCountCell.setCellStyle(numStyle);
                totalCountCell.setCellValue(totalCount);
            }
            XSSFCell companyCell = row.createCell(2);
            if (companyCount == 0) {
                companyCell.setCellValue("");
            } else {
                companyCell.setCellStyle(numStyle);
                companyCell.setCellValue(companyCount);
            }
            XSSFCell projectCell = row.createCell(3);
            if (projectCount == 0) {
                projectCell.setCellValue("");
            } else {
                projectCell.setCellStyle(numStyle);
                projectCell.setCellValue(projectCount);
            }
            rowNo++;
        }

        for (int colNo = 0; colNo < statSheet.getRow(0).getLastCellNum(); colNo++) {
            statSheet.autoSizeColumn(colNo, true);
        }

        started.stop();
        log.info("[{}] 按分类统计完成，用时：{}分钟 ({}秒)",
                param.getLogType(), started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

}
