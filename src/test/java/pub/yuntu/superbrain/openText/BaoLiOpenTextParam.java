package pub.yuntu.superbrain.openText;

import lombok.Builder;
import lombok.Data;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2023年02月25日 19:38:54
 */
@Data
@Builder
public class BaoLiOpenTextParam {
    String sourceExcelPath;
    List<String> destinationTitles;
    int titleRowNo;
    int startRowNo;
    String openTextTitle;
    Class<?> readDto;
    String logType;
    XSSFWorkbook destinationWb;
    List<Integer> additionalTitleColNums; // 开放文本分类的列号，用来自动设置列宽
    List<String> additionalTitles; // 开放文本分类的列号，用来自动设置列宽

    String sheetNameOfProjectStat;
    String sheetNameOfInteractionStat;
    List<String> statByProjectTitles;
    List<String> statByInteractionTitles;
}
