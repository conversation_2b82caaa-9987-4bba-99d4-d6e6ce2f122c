package pub.yuntu.superbrain.openText.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.*;
import pub.yuntu.superbrain.openText.BaoLiOpenTextParam;
import pub.yuntu.superbrain.openText.BaoLiOpenTextTestCase;
import pub.yuntu.superbrain.openText.dto.TouSuReadDTO;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static pub.yuntu.superbrain.openText.BaoLiOpenTextTestCase.writePassResult;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2023年02月25日 20:01:55
 */
@Slf4j
public class TouSuDtoListener implements ReadListener<TouSuReadDTO> {

    @Setter
    KMP kmp;
    @Setter
    TextParser textParser;
    @Setter
    BaoLiOpenTextParam param;
    @Setter
    Stopwatch started;

    int rowCount = 0;

    /**
     * 单次缓存的数据量
     */
    public static final int BATCH_COUNT = 5000;
    /**
     * 临时存储
     */
    private List<TouSuReadDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    @Override
    public void invoke(TouSuReadDTO data, AnalysisContext context) {
        log.debug("解析到一条数据:{}", JSON.toJSONString(data));
        cachedDataList.add(data);
        if (cachedDataList.size() >= BATCH_COUNT) {
            run();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要处理数据，确保最后遗留的数据也被处理
        run();
        log.info("所有数据解析完成！，用时：{}分钟 ({}秒)",
                started.elapsed(TimeUnit.MINUTES), started.elapsed(TimeUnit.SECONDS));
    }

    /**
     * 加上存储数据库
     */
    private void run() {
        log.debug("{}条数据，开始解析", cachedDataList.size());
        cachedDataList.forEach(data -> {
            List<Interaction> interactions = parseText(data);
            writeToDestination(data, interactions);
        });
        if (rowCount % 10000 == 0) {
            log.info("写入文件 {} 行", rowCount);
        }
    }

    private List<Interaction> parseText(TouSuReadDTO data) {
        String text = data.getDesc();
        if (StringUtils.isBlank(text)) return Lists.newArrayList();
        List<MatchingGroup> matchingGroups = textParser.parse(text, true, personInfo(data), KMP.OPEN_TYPE_TEXT, true);
        return textParser.parseInteractions(matchingGroups, KMP.OPEN_TYPE_TEXT, BaoLiOpenTextTestCase.KEEP_CODE_MAP);
    }

    // personInfo 按业主类型生成，投诉的表格里面没有业主信息
    private PersonInfo personInfo(TouSuReadDTO data) {
        return null;
    }

    /**
     * !! IMPORTANT!!
     * 列的取值需要与 BaoLiOpenTextParam 中的 destinationTitles 一致
     */
    private void writeToDestination(TouSuReadDTO data, List<Interaction> interactions) {
        XSSFSheet sheet = param.getDestinationWb().getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        XSSFRow row = sheet.createRow(lastRowNum + 1);
        XSSFCell cell_0 = row.createCell(0);
        cell_0.setCellValue(data.getReceptionNo()); // 接待编号
        XSSFCell cell_1 = row.createCell(1);
        cell_1.setCellValue(data.getCompany()); // 公司
        XSSFCell cell_2 = row.createCell(2);
        cell_2.setCellValue(data.getProject()); // 项目名称
        XSSFCell cell_3 = row.createCell(3);
        cell_3.setCellValue(data.getDesc()); // 问题描述
        XSSFCell cell_4 = row.createCell(4);
        cell_4.setCellValue(data.getType()); // 问题类型

        writePassResult(param, sheet, row, interactions);

        rowCount++;
    }
}
