package pub.yuntu.superbrain.openText.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description 投诉数据
 * @createTime 2023年02月25日 14:27:58
 */
@Data
@EqualsAndHashCode
public class TouSuReadDTO {
    String order; // 序号
    // String taskNo; // 任务编号
    String receptionNo; // 接待编号
    String company; // 公司
    String project; // 项目名称
    String building; // 楼栋
    // String unit; // 单元
    String room; // 房间
    // String complaintProject; // 投诉项目
    // String complaintBuilding; // 投诉楼栋
    // String complaintUnit; // 投诉单元
    // String complaintRoom; // 投诉房间
    String receptionTime; // 接待时间
    // String receptionPerson; // 接待人
    // String serviceRequester; // 服务请求人
    String mobile; // 电话
    String infoSource; // 信息来源
    // String receptionType; // 接待类型
    // String receptionDesc; // 接待说明
    String supplementaryNote; // 补充说明
    // String parts; // 部位
    String category1; // 一级问题分类
    String category2; // 二级问题分类
    String category3; // 三级问题分类
    String category4; // 四级问题分类
    String desc; // 问题描述
    String type; // 问题类型
    // String complaintCategory; // 投诉分类
    // String recipient; // 受理人
    // String responsible; // 责任人
    // String responsibleUnit; // 责任单位
    String status; // 问题状态
    String closeType; // 关闭类型
    // String closeReason; // 关闭原因
    String closeDesc; // 关闭说明
    // String closePerson; // 关闭人
    // String timeLimit; // 问题处理时限
    String finalTimeLimit; // 最终问题处理时限
    String actualCloseTime; // 实际关闭时间
    String isTimeout; // 是否超时
    String timeoutDays; // 超时天数
    // String repairTimes; // 返修次数
    String isDelay; // 是否延期
    String isTransferProperty; // 是否转物业
    // String dispatchDate; // 派单日期
    // String finishTime; // 完成时间
}
