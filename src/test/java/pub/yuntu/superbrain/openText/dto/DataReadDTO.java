package pub.yuntu.superbrain.openText.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description z_data_3019
 * @createTime 2023年02月25日 14:31:43
 */
@Data
@EqualsAndHashCode
public class DataReadDTO {
    String dataSource; // 数据来源
    String encrypt; // encrypt
    String fgUniqueNo; // FG唯一码
    String dataPeriod; // 分期
    String finished; // finished
    String excelSourceDate; // 名单来源Excel日期
    String createTime; // createtime
    String org_one_1_unit_name; // org_one_1_unit_name
    String org_one_2_unit_name; // org_one_2_unit_name
    String org_one_3_unit_name; // org_one_3_unit_name
    String org_one_4_unit_name; // org_one_4_unit_name
    String org_one_5_unit_name; // org_one_5_unit_name
    String org_one_6_unit_name; // org_one_6_unit_name
    String ownerTypeFour; // 业主类型
    String ownerTypeSeven; // 业主类型(3211)
    String houseType; // 房屋类型
    String decrationType; // 装修类型
    String building; // 楼栋
    String unit; // 单元
    String houseNo; // 房号
    String name; // 姓名
    String mobile; // 电话
    String myUniqueNo; // 明源系统房产唯一码
    String erpHouseUniqueNo; // erp系统房号唯一码
    String wechatId; // 微信id
    String singDate; // 签约时间
    String contractDeliveryDate; // 合同交付日期
    String actualDeliveryDate; // 实际交付日期
    String actualClosingDate; // 实际收楼日期
    String deliveryBatch; // 交付批次
    String isInsurance; // 是否保投顾
    String contactType; // 合同类型
    String periodGroup; // 期别组团
    String plot; // 地块
    String sales; // 销售人员
    String houseInfo; // 房屋信息
    String houseName; // 房屋名称
    String type; // 采数方式
    String open_id; // open_id
    String S0a; // S0a
    String J1s; // J1s
    String S8; // S8
    String A1; // A1
    String A2; // A2
    String A3; // A3
    String F2s; // F2s
    String L1; // L1
    String C1; // C1
    String C6; // C6
    String D1; // D1
    String G1; // G1
    String H2; // H2
    String H1; // H1
    String E1; // E1
    String F2; // F2
    String J1; // J1
    String L2; // L2
    String C4_1; // C4_1
    String C4_2; // C4_2
    String C4_3; // C4_3
    String C4_4; // C4_4
    String C4_5; // C4_5
    String C4_6; // C4_6
    String C4_7; // C4_7
    String C6_1; // C6_1
    String C6_2; // C6_2
    String D3; // D3
    String D3a_1; // D3a_1
    String D3a_2; // D3a_2
    String D3a_3; // D3a_3
    String D3a_4; // D3a_4
    String D3a_5; // D3a_5
    String D3a_6; // D3a_6
    String D3a_7; // D3a_7
    String D3a_7_text; // D3a_7_text
    String D3b_1; // D3b_1
    String D3b_2; // D3b_2
    String D3b_3; // D3b_3
    String D3b_4; // D3b_4
    String D3b_5; // D3b_5
    String D3b_6; // D3b_6
    String D4_1; // D4_1
    String D4_6; // D4_6
    String G1a_1; // G1a_1
    String G1a_2; // G1a_2
    String G1a_3; // G1a_3
    String G1a_4; // G1a_4
    String G1a_5; // G1a_5
    String G1a_6; // G1a_6
    String G1a_7; // G1a_7
    String G1a_8; // G1a_8
    String G1a_9; // G1a_9
    String G1a_10; // G1a_10
    String G1a_10_text; // G1a_10_text
    String H2a_1; // H2a_1
    String H2a_2; // H2a_2
    String H2a_3; // H2a_3
    String H2a_4; // H2a_4
    String H2a_5; // H2a_5
    String H2a_6; // H2a_6
    String H2a_7; // H2a_7
    String H2a_8; // H2a_8
    String H2a_9; // H2a_9
    String H2a_9_text; // H2a_9_text
    String H1a_1; // H1a_1
    String H1a_2; // H1a_2
    String H1a_3; // H1a_3
    String H1a_4; // H1a_4
    String H1a_5; // H1a_5
    String H1a_6; // H1a_6
    String H1a_7; // H1a_7
    String H1a_7_text; // H1a_7_text
    String E3; // E3
    String E4_1; // E4_1
    String E4_2; // E4_2
    String E4_3; // E4_3
    String E4_4; // E4_4
    String E4_5; // E4_5
    String E4_6; // E4_6
    String E4_7; // E4_7
    String E4_8; // E4_8
    String E4_9; // E4_9
    String E4_9_text; // E4_9_text
    String E4_1a_1; // E4_1a_1
    String E4_1a_2; // E4_1a_2
    String E4_1a_3; // E4_1a_3
    String E4_1a_4; // E4_1a_4
    String E4_1a_5; // E4_1a_5
    String E4_1a_6; // E4_1a_6
    String E4_1a_7; // E4_1a_7
    String E4_1a_8; // E4_1a_8
    String E4_1a_9; // E4_1a_9
    String E4_1a_10; // E4_1a_10
    String E4_1a_11; // E4_1a_11
    String E4_1a_11_text; // E4_1a_11_text
    String E4_2a_1; // E4_2a_1
    String E4_2a_2; // E4_2a_2
    String E4_2a_3; // E4_2a_3
    String E4_2a_4; // E4_2a_4
    String E4_2a_4_text; // E4_2a_4_text
    String E4_3a_1; // E4_3a_1
    String E4_3a_2; // E4_3a_2
    String E4_3a_3; // E4_3a_3
    String E4_3a_4; // E4_3a_4
    String E4_3a_5; // E4_3a_5
    String E4_3a_6; // E4_3a_6
    String E4_3a_6_text; // E4_3a_6_text
    String E4_4a_1; // E4_4a_1
    String E4_4a_2; // E4_4a_2
    String E4_4a_3; // E4_4a_3
    String E4_4a_4; // E4_4a_4
    String E4_4a_5; // E4_4a_5
    String E4_4a_6; // E4_4a_6
    String E4_4a_7; // E4_4a_7
    String E4_4a_8; // E4_4a_8
    String E4_4a_9; // E4_4a_9
    String E4_4a_10; // E4_4a_10
    String E4_4a_11; // E4_4a_11
    String E4_4a_11_text; // E4_4a_11_text
    String E4_5a_1; // E4_5a_1
    String E4_5a_2; // E4_5a_2
    String E4_5a_3; // E4_5a_3
    String E4_5a_4; // E4_5a_4
    String E4_5a_5; // E4_5a_5
    String E4_5a_5_text; // E4_5a_5_text
    String E4_6a_1; // E4_6a_1
    String E4_6a_2; // E4_6a_2
    String E4_6a_3; // E4_6a_3
    String E4_6a_4; // E4_6a_4
    String E4_6a_5; // E4_6a_5
    String E4_6a_5_text; // E4_6a_5_text
    String E4_7a_1; // E4_7a_1
    String E4_7a_2; // E4_7a_2
    String E4_7a_3; // E4_7a_3
    String E4_7a_4; // E4_7a_4
    String E4_7a_5; // E4_7a_5
    String E4_7a_5_text; // E4_7a_5_text
    String E4_8a_1; // E4_8a_1
    String E4_8a_2; // E4_8a_2
    String E4_8a_3; // E4_8a_3
    String E4_8a_4; // E4_8a_4
    String E4_8a_5; // E4_8a_5
    String E4_8a_6; // E4_8a_6
    String E4_8a_6_text; // E4_8a_6_text
    String F1a_1; // F1a_1
    String F1a_2; // F1a_2
    String F1a_3; // F1a_3
    String F1a_4; // F1a_4
    String F1a_5; // F1a_5
    String F1a_5_text; // F1a_5_text
    String F1b; // F1b
    String F3_5; // F3_5
    String F3_6; // F3_6
    String F3_7; // F3_7
    String F3_9; // F3_9
    String F4; // F4
    String J10; // J10
    String J10a_1; // J10a_1
    String J10a_2; // J10a_2
    String J10a_3; // J10a_3
    String J10a_4; // J10a_4
    String J10a_4_text; // J10a_4_text
    String J11; // J11
    String J13; // J13
    String J13a_1; // J13a_1
    String J13a_2; // J13a_2
    String J13a_3; // J13a_3
    String J13a_4; // J13a_4
    String J13a_5; // J13a_5
    String J13a_6; // J13a_6
    String J13a_7; // J13a_7
    String J13a_8; // J13a_8
    String J13a_8_text; // J13a_8_text
    String J14; // J14
    String J12; // J12
    String J9; // J9
    String J15; // J15
    String J15a_1; // J15a_1
    String J15a_2; // J15a_2
    String J15a_3; // J15a_3
    String J15a_4; // J15a_4
    String J15a_4_text; // J15a_4_text
    String J9_1; // J9_1
    String J9_2; // J9_2
    String J9_3_1; // J9_3_1
    String J9_3_2; // J9_3_2
    String J9_3_3; // J9_3_3
    String J9_3_4; // J9_3_4
    String J9_3_5; // J9_3_5
    String J9_3_6; // J9_3_6
    String J16; // J16
    String J16_1_1; // J16_1_1
    String J16_1_2; // J16_1_2
    String J16_1_3; // J16_1_3
    String J16_1_3_text; // J16_1_3_text
    String J16_2_1; // J16_2_1
    String J16_2_2; // J16_2_2
    String J16_2_3; // J16_2_3
    String J16_2_3_text; // J16_2_3_text
    String J17; // J17
    String L1a_1; // L1a_1
    String L1a_2; // L1a_2
    String L1a_3; // L1a_3
    String L1a_4; // L1a_4
    String L1a_5; // L1a_5
    String L1a_6; // L1a_6
    String L1a_7; // L1a_7
    String L1a_8; // L1a_8
    String L1a_8_text; // L1a_8_text
    String L2a_1; // L2a_1
    String L2a_2; // L2a_2
    String L2a_3; // L2a_3
    String L2a_4; // L2a_4
    String L2a_5; // L2a_5
    String L2a_6; // L2a_6
    String L2a_6_text; // L2a_6_text
    String M1; // M1
    String M1a_1; // M1a_1
    String M1a_2; // M1a_2
    String M1a_3; // M1a_3
    String M1a_4; // M1a_4
    String M1a_5; // M1a_5
    String M1a_6; // M1a_6
    String M1a_7; // M1a_7
    String M1a_7_text; // M1a_7_text
    String M1a_8; // M1a_8
    String M1a_9; // M1a_9
    String P1; // P1
    String P2; // P2
    String A1a; // A1a
    String A1b; // A1b
    String S1; // S1
    String S2_1; // S2_1
    String S2_2; // S2_2
    String S2_3; // S2_3
    String S2_4; // S2_4
    String S2_4_text; // S2_4_text
    String R1; // R1
    String T1_1; // T1_1
    String T1_2; // T1_2
    String T1_3; // T1_3
    String T1_4; // T1_4
    String T1_5; // T1_5
    String T1_6; // T1_6
    String T1_7; // T1_7
    String T1_99; // T1_99
    String T1_99_text; // T1_99_text

}
