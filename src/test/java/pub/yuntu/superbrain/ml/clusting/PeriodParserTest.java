package pub.yuntu.superbrain.ml.clusting;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import pub.yuntu.superbrain.domain.model.parser.nlp.PeriodParser;

@Slf4j
public class PeriodParserTest {

    @Test
    public void testRun() {
        PeriodParser periodParser = new PeriodParser();
        calPeriod(periodParser);
    }

    public void calPeriod(PeriodParser periodParser) {
        // 年度
        periodParser.calPeriod("2019年的销售情况");
        periodParser.calPeriod("20年的销售情况");
        periodParser.calPeriod("分析98年的销售情况");
        periodParser.calPeriod("分析大大前年的销售情况");
        periodParser.calPeriod("大前年的销售情况");
        periodParser.calPeriod("前年的销售情况");
        periodParser.calPeriod("去年的销售情况");
        periodParser.calPeriod("今年的销售情况");
        periodParser.calPeriod("明年的销售情况");
        periodParser.calPeriod("分析后年的销售情况");
        periodParser.calPeriod("销售情况");
        periodParser.calPeriod("上年的销售情况");
        periodParser.calPeriod("上上年的销售情况");
        periodParser.calPeriod("上上上年的销售情况");
        periodParser.calPeriod("2017的销售情况");
        periodParser.calPeriod("分析2018销售情况");
        periodParser.calPeriod("2021-0112物业满意度");
        log.debug("-----------------------------------------");

        // 半年度
        periodParser.calPeriod("今年上半年的销售情况");
        periodParser.calPeriod("分析去年下半年的销售情况");
        periodParser.calPeriod("分析2021年下半年的销售情况");
        periodParser.calPeriod("分析20年前半年的销售情况");
        periodParser.calPeriod("20年后半年的销售情况");
        periodParser.calPeriod("2022-0106销售情况");
        log.debug("-----------------------------------------");

        // 季度
        periodParser.calPeriod("上上上上上上上季度的销售情况");
        periodParser.calPeriod("上上上上上上季度的销售情况");
        periodParser.calPeriod("上上上上上季度的销售情况");
        periodParser.calPeriod("上上上上季度的销售情况");
        periodParser.calPeriod("上上上季度的销售情况");
        periodParser.calPeriod("上上季度的销售情况");
        periodParser.calPeriod("上季度的销售情况");
        periodParser.calPeriod("本季度的销售情况");
        periodParser.calPeriod("本季的销售情况");
        periodParser.calPeriod("分析当季的销售情况");
        periodParser.calPeriod("下季度的销售情况");
        periodParser.calPeriod("下下季度的销售情况");
        periodParser.calPeriod("下下下季度的销售情况");
        periodParser.calPeriod("分析下下下下季度的销售情况");
        periodParser.calPeriod("分析今年一季度的销售情况");
        periodParser.calPeriod("第四季度的销售情况");
        periodParser.calPeriod("去年3季度的销售情况");
        periodParser.calPeriod("分析2022-0709的销售情况");
        log.debug("-----------------------------------------");

        // 月度
        periodParser.calPeriod("分析上上上个月销售情况");
        periodParser.calPeriod("上上个月销售情况");
        periodParser.calPeriod("上个月销售情况");
        periodParser.calPeriod("这个月销售情况");
        periodParser.calPeriod("本月销售情况");
        periodParser.calPeriod("下个月销售情况");
        periodParser.calPeriod("下下个月销售情况");
        periodParser.calPeriod("下下下个月销售情况");
        periodParser.calPeriod("2022年12月销售情况");
        periodParser.calPeriod("21年10月销售情况");
        periodParser.calPeriod("2023-01销售情况");
        periodParser.calPeriod("分析2023-2销售情况");
        periodParser.calPeriod("分析2022.01销售情况");
        periodParser.calPeriod("分析202201销售情况");
        periodParser.calPeriod("2022.1销售情况");
        log.debug("-----------------------------------------");

        // 累计
        periodParser.calPeriod("今年累计的销售情况");
        periodParser.calPeriod("去年1至7月的销售情况");
        periodParser.calPeriod("今年1月至10月的销售情况");
        periodParser.calPeriod("分析今年4到6月的销售情况");
        periodParser.calPeriod("1月到11月的销售情况");
        periodParser.calPeriod("1-5月的销售情况");
        periodParser.calPeriod("前年10~12月的销售情况");
        periodParser.calPeriod("分析2019年1至3季度的销售情况");
        periodParser.calPeriod("19年3季度到4季度的销售情况");
        periodParser.calPeriod("今年前10个月的销售情况");
        periodParser.calPeriod("2019年前3季度的销售情况");
        periodParser.calPeriod("分析18年前3个季度的销售情况");
        periodParser.calPeriod("2020-0108销售情况");
        log.debug("-----------------------------------------");

        // 日
        periodParser.calPeriod("这个月六号销售情况");
        periodParser.calPeriod("上个月六号销售情况");
        periodParser.calPeriod("说说上个月今天销售情况");
        periodParser.calPeriod("8月20日销售情况");
        periodParser.calPeriod("8月二十一日销售情况");
        periodParser.calPeriod("八月二十日销售情况");
        periodParser.calPeriod("八月20日销售情况");
        periodParser.calPeriod("说说20号销售情况");
        periodParser.calPeriod("二十号销售情况");
        periodParser.calPeriod("大大前天销售情况");
        periodParser.calPeriod("大前天销售情况");
        periodParser.calPeriod("前天销售情况");
        periodParser.calPeriod("昨天销售情况");
        periodParser.calPeriod("说说今天销售情况");
        periodParser.calPeriod("明天销售情况");
        periodParser.calPeriod("后天销售情况");
        periodParser.calPeriod("大后天销售情况");
        periodParser.calPeriod("大大后天销售情况");
        periodParser.calPeriod("大大大后天销售情况");
        periodParser.calPeriod("2023-05-02销售情况");
        periodParser.calPeriod("说说2023-5-2的销售情况");
        periodParser.calPeriod("2021.05.13的销售情况");
        periodParser.calPeriod("分析2021.5.2销售情况");
        periodParser.calPeriod("20211002销售情况");
        log.debug("-----------------------------------------");

        // 混合
        periodParser.calPeriod("显示去年的物业细项和今年各区域的总体满意度");
        periodParser.calPeriod("分别展示前年、去年2季度、7月、8月、九月、三季度、6~10月累计、今年下半年、12月的物业细项得分");
        periodParser.calPeriod("分别展示2021、2022、2023年的物业细项得分");
    }
}