package pub.yuntu.superbrain.application.dsp;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.domain.dto.Conclusion2TaskLogDTO;
import pub.yuntu.superbrain.domain.mapstruct.Conclusion2TaskLogMapper;
import pub.yuntu.superbrain.domain.model.dsp.batch.Conclusion2Task;
import pub.yuntu.superbrain.domain.model.dsp.batch.Conclusion2TaskLog;
import pub.yuntu.superbrain.infrastructure.persistence.dsp.Conclusion2TaskLogRepository;
import pub.yuntu.superbrain.infrastructure.persistence.dsp.Conclusion2TaskRepository;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023年12月22日 14:20:14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class Conclusion2TaskLogApplicationTest {

    @Autowired
    Conclusion2TaskLogApplication application;
    @Autowired
    Conclusion2TaskLogRepository taskLogRepository;

    @Test
    void parse() {
        Conclusion2TaskLog taskLog = taskLogRepository.findById("EF0961BD-6D30-4AC9-9B86-485A0703883D")
                .orElseThrow(NullPointerException::new);
        Conclusion2TaskLogDTO taskLogDTO = Conclusion2TaskLogMapper.INSTANCE.toDto(taskLog);
        application.parse(taskLogDTO);
    }
}