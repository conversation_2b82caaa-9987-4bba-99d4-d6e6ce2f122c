package pub.yuntu.superbrain.gpt.frame;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @Description 流计算框架单元测试
 * @createTime 2024年03月15日 09:33:32
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class GptFrameworkTest {

    @Autowired
    GptFramework gptFramework;

    private String jsonConfig;

    @BeforeEach
    void setUp() throws IOException {
        String filePath = "/Users/<USER>/800_Code/YT-Code/YT-Superbrain/src/main/java/pub/yuntu/superbrain/gpt/frame/json/[弃用-已过时]1_图节点创建_开发商_导入Nebula.json";
        this.jsonConfig = new String(Files.readAllBytes(Paths.get(filePath)), StandardCharsets.UTF_8);
    }

    @Test
    void processing() {
        gptFramework.processing(jsonConfig, null, "maven");
    }
}