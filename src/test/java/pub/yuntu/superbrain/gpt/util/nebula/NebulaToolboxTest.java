package pub.yuntu.superbrain.gpt.util.nebula;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import pub.yuntu.superbrain.gpt.util.nebula.structure.NebulaProperty;
import pub.yuntu.superbrain.gpt.util.nebula.structure.NebulaPropertyType;
import pub.yuntu.superbrain.gpt.util.nebula.structure.NebulaTag;
import pub.yuntu.superbrain.gpt.util.nebula.toolbox.NebulaToolbox;
import pub.yuntu.superbrain.gpt.util.nebula.toolbox.VertexToolbox;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024年03月12日 09:42:28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class NebulaToolboxTest {

    @Autowired
    NebulaToolbox nebulaToolbox;
    @Autowired
    VertexToolbox vertexToolbox;

    @Test
    void testCreateTag() {
        NebulaTag nebulaTag = new NebulaTag("test");
        NebulaProperty name = new NebulaProperty("name", NebulaPropertyType.STRING);
        NebulaProperty type = new NebulaProperty("type", NebulaPropertyType.STRING);
        nebulaTag.setProperties(Lists.newArrayList(name, type));
        boolean tag = nebulaToolbox.createTag(nebulaTag, "UnitTest");

        // 简化写法
        // boolean tag = nebulaToolbox.createTag(new NebulaTag("test", Lists.newArrayList(name, type)));

        assertTrue(tag);
    }

    @Test
    void importVertex() {
        // 使用 BufferedReader 读取 CSV 文件
        String csvFile = "/Users/<USER>/Downloads/result.csv";
        List<Map<String, Object>> dataList = Lists.newArrayList();
        
        try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
            // 读取第一行作为列名
            String headerLine = br.readLine();
            String[] headers = new String[]{"vid", "nid", "id", "name"};
            
            // 读取数据行
            String line;
            while ((line = br.readLine()) != null) {
                // 将每行数据分割成数组
                String[] values = line.split(",");
                
                // 创建一个 Map 存储当前行的数据
                Map<String, Object> rowData = new HashMap<>();
                
                // 将列名和值对应存入 Map
                for (int i = 0; i < headers.length; i++) {
                    if (i < values.length) {
                        rowData.put(headers[i].trim(), values[i].trim());
                    }
                }
                
                // 将当前行数据添加到列表中
                dataList.add(rowData);
            }
        } catch (IOException e) {
            log.error("读取 CSV 文件失败", e);
        }

        for (Map<String, Object> data : dataList) {
            String vid = data.get("vid") + "";
            String nidText = data.get("nid") + "";
            String id = data.get("id") + "";
            String name = data.get("name") + "";
            vid = vid.replaceAll("\"", "");
            nidText = nidText.replaceAll("\"", "");
            id = id.replaceAll("\"", "");
            name = name.replaceAll("\"", "");
            String ngql = String.format("INSERT VERTEX customerSplit(nid, id, name) VALUES '%s':(%d, '%s', '%s')", vid, Integer.parseInt(nidText), id, name);
//            vertexToolbox.insertVertex(ngql, "meizhi");
        }
    }

}