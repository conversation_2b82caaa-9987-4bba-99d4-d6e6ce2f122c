package pub.yuntu.superbrain.gpt.util.nebula;

import com.google.common.collect.Lists;
import com.vesoft.nebula.jdbc.NebulaResultSet;
import com.vesoft.nebula.jdbc.utils.NebulaPropertyKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import pub.yuntu.superbrain.gpt.util.nebula.toolbox.NebulaToolbox;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;


/**
 * Edge 边增加属性
 *
 * <AUTHOR>
 * @createTime 2024年07月12日 13:56:07
 */
@Slf4j
public class EdgeAddPropertyTest {

    Connection connection;

    @Test
    public void edgeAddProperty() {
        // 查询所有的起点
        // industryCity → children:geo → industryCustomerCity
        executeEdgeAddProperty("industryCity", "industryCustomerCity", "children", "geo");
        // industryCustomer → children:default → industryCustomerCity
        executeEdgeAddProperty("industryCustomer", "industryCustomerCity", "children", "default");
        // industryCustomerCity →  parent:geo →  industryCity
        executeEdgeAddProperty("industryCustomerCity", "industryCity", "parent", "geo");
        // industryCustomerCity → parent:default →  industryCustomer
        executeEdgeAddProperty("industryCustomerCity", "industryCustomer", "parent", "default");
    }

    private void executeEdgeAddProperty(String srcTag, String dstTag, String edgeName, String edgeType) {
        String srcVid = "srcVid", dstVid = "dstVid";
        List<HashMap<String, Object>> dataList = loadTagAllData(srcTag, dstTag, edgeName, srcVid, dstVid);

        List<String> updateNgqlList = new ArrayList<>();
        for (HashMap<String, Object> data : dataList) {
            String srcVidValue = data.get(srcVid) + "";
            String dstVidValue = data.get(dstVid) + "";
            String updateNgql = String.format("UPDATE EDGE ON %s '%s' -> '%s'@0 SET type = '%s' YIELD type", edgeName, srcVidValue, dstVidValue, edgeType);
            updateNgqlList.add(updateNgql);
        }

        List<List<String>> partition = Lists.partition(updateNgqlList, 50);
        partition.forEach(list -> {
            String batchNgql = String.join(";", list);
            // 创建并执行SQL语句
            try (Statement statement = connection.createStatement()) {
                // 执行NGQL查询语句
                log.info("执行Update NGQL：{}", batchNgql);
                statement.execute(batchNgql);
            } catch (Throwable e) {
                log.error("执行 Nebula 查询发生错误：{}", batchNgql, e);
            }
        });
    }

    private List<HashMap<String, Object>> loadTagAllData(String srcTag, String dstTag, String edgeName, String srcVid, String dstVid) {
        // MATCH (s:industryCity)-[e:children]->(d:industryCustomerCity) RETURN id(s) as srcVid, e, id(s) as dstVid
        String ngql = String.format("MATCH (s:%s)-[e:%s]->(d:%s) RETURN id(s) as %s, e, id(d) as %s", srcTag, edgeName, dstTag, srcVid, dstVid);
        // 创建并执行SQL查询
        return loadData(ngql);
    }

    private List<HashMap<String, Object>> loadData(String ngql) {
        List<HashMap<String, Object>> dataList = new ArrayList<>();
        try (Statement statement = connection.createStatement()) {
            log.info("执行查询：{}", ngql);
            ResultSet resultSet = statement.executeQuery(ngql); // 执行查询
            // 处理查询结果并返回
            dataList = NebulaToolbox.convertResultSet((NebulaResultSet) resultSet);
        } catch (Throwable e) {
            log.error("执行 Nebula 查询发生错误：{}", ngql, e);
        }
        return dataList;
    }

    @Test
    public void deleteVertexWithEdge() {
        String tagName = "industryProvince";
        String ngql = String.format("MATCH (v:%s) RETURN id(v) as vid", tagName);
        List<HashMap<String, Object>> dataList = loadData(ngql);

        List<String> deleteNgqlList = new ArrayList<>();
        for (HashMap<String, Object> data : dataList) {
            String vid = data.get("vid") + "";
            String updateNgql = String.format("DELETE VERTEX '%s' WITH EDGE", vid);
            deleteNgqlList.add(updateNgql);
        }

        List<List<String>> partition = Lists.partition(deleteNgqlList, 50);
        partition.forEach(list -> {
            String batchNgql = String.join(";", list);
            // 创建并执行SQL语句
            try (Statement statement = connection.createStatement()) {
                // 执行NGQL查询语句
                log.info("执行删除 Vertex NGQL：{}", batchNgql);
                statement.execute(batchNgql);
            } catch (Throwable e) {
                log.error("执行 Nebula 查询发生错误：{}", batchNgql, e);
            }
        });

        /*
        删除索引和 Tag

        DROP TAG INDEX IF EXISTS idx_industryProvince_name;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_name;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_id;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_db_id;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_group_name;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_group_id;
        DROP TAG INDEX IF EXISTS idx_industryProvince_split_group_db_id;
        DROP TAG IF EXISTS industryProvince;
         */
    }

    @Test
    public void deleteEdge() {
//        // industryCustomer → children → industryProject
//        executeDeleteEdge("children", "industryCustomer", "industryProject");
//        // industryCustomerCity → children → industryProject
//        executeDeleteEdge("children", "industryCustomerCity", "industryProject");
//        // industryProject → parent → industryCustomer
//        executeDeleteEdge("parent", "industryProject", "industryCustomer");
//        // industryProject → parent → industryCustomerCity
//        executeDeleteEdge("parent", "industryProject", "industryCustomerCity");
//        // industryCustomerCity → children → industryCustomerDistrict
//        executeDeleteEdge("children", "industryCustomerCity", "industryCustomerDistrict");
//        // industryCustomer → children → industryCustomerCity
//        executeDeleteEdge("children", "industryCustomer", "industryCustomerCity");
//        // industryCustomer → parent → industryCustomerCity
//        executeDeleteEdge("parent", "industryCustomer", "industryCustomerCity");
//        // industryCustomerCity → children → industryCustomerDistrict
//        executeDeleteEdge("children", "industryCustomerCity", "industryCustomerDistrict");
//        // industryCustomerDistrict → children → industryProject
//        executeDeleteEdge("children", "industryCustomerDistrict", "industryProject");
//        // industryProject→ parent → industryCustomerDistrict
//        executeDeleteEdge("parent", "industryProject", "industryCustomerDistrict");
//        // industryCustomerDistrict → parent → industryCustomerCity
//        executeDeleteEdge("parent", "industryCustomerDistrict", "industryCustomerCity");
//        // industryCustomerCity → parent → industryCustomer
//        executeDeleteEdge("parent", "industryCustomerCity", "industryCustomer");
//        // industryCustomerCity → children → industryCustomer
//        executeDeleteEdge("children", "industryCustomerCity", "industryCustomer");
//        // industryCustomerCity → parent → industryCity
//        executeDeleteEdge("parent", "industryCustomerCity", "industryCity");
//        // industryCity → children → industryCustomerCity
//        executeDeleteEdge("children", "industryCity", "industryCustomerCity");
        executeDeleteEdge("parent", "industryCustomer", "industryCustomerCity");
        executeDeleteEdge("children", "industryCustomerCity", "industryCustomer");
    }

    private void executeDeleteEdge(String edgeName, String srcTag, String dstTag) {
        // 查询所有的起点和终点： MATCH (src:industryCustomer)-[e:children]->(dst:industryProject)  RETURN id(src) as srcVid, id(dst) as dstVid
        String ngql = String.format("MATCH (src:%s)-[e:%s]->(dst:%s) RETURN id(src) as srcVid, id(dst) as dstVid", srcTag, edgeName, dstTag);
         log.info("查询所有的起点和终点：{}", ngql);
        List<HashMap<String, Object>> srcDstVidList = loadData(ngql);
        log.info("查询所有的起点和终点，共计：{}", srcDstVidList.size());

        // 删除：DELETE EDGE children "industryCustomer_世茂物业" -> "industryCustomerCity_世茂物业_三明市"@0;
        List<String> deleteNgqlList = new ArrayList<>();
        srcDstVidList.forEach(data -> {
            String srcVid = data.get("srcVid") + "";
            String dstVid = data.get("dstVid") + "";
            String deleteNgql = String.format("DELETE EDGE %s '%s' -> '%s'@0", edgeName, srcVid, dstVid);
            deleteNgqlList.add(deleteNgql);
        });

        List<List<String>> partition = Lists.partition(deleteNgqlList, 50);
        partition.forEach(list -> {
            String batchNgql = String.join(";", list);
            // 创建并执行SQL语句
            try (Statement statement = connection.createStatement()) {
                // 执行NGQL查询语句
                log.info("执行删除 Edge NGQL：{}", batchNgql);
                statement.execute(batchNgql);
            } catch (Throwable e) {
                log.error("执行 Nebula 查询发生错误：{}", batchNgql, e);
            }
        });
    }

    @Before
    public void tearUp() {
        String url = "*************************************";
        Properties poolProperties = new Properties();
        poolProperties.put(NebulaPropertyKey.USER, "yt_service");
        poolProperties.put(NebulaPropertyKey.USER.getKeyName(), "yt_service");
        poolProperties.put(NebulaPropertyKey.PASSWORD, "R63quvAMQ8pq4VXgVvL");
        poolProperties.put(NebulaPropertyKey.PASSWORD.getKeyName(), "R63quvAMQ8pq4VXgVvL");
        poolProperties.put(NebulaPropertyKey.MINCONNSSIZE, 2);
        poolProperties.put(NebulaPropertyKey.MAXCONNSSIZE, 100);
        poolProperties.put(NebulaPropertyKey.TIMEOUT, 480000);
        poolProperties.put(NebulaPropertyKey.IDLETIME, 727);
        poolProperties.put(NebulaPropertyKey.INTERVALIDLE, 1256);
        poolProperties.put(NebulaPropertyKey.WAITTIME, 1256);

        try {
            connection = DriverManager.getConnection(url, poolProperties);
        } catch (SQLException e) {
            // 抛出运行时异常，将SQL异常包装起来
            RuntimeException re = new RuntimeException(e);
            log.error(re.getMessage());
            throw re;
        }
    }

    @After
    public void tearDown() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            log.error("关闭 Nebula 连接发生错误", e);
        }
    }

}
