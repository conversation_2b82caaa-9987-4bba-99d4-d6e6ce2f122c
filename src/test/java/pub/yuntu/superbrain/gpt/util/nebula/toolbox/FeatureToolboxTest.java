package pub.yuntu.superbrain.gpt.util.nebula.toolbox;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.gpt.domain.feature.toolbox.FeatureToolbox;

/**
 * TODO
 *
 * <AUTHOR>
 * @createTime 2024年05月21日 21:47:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class FeatureToolboxTest {

    @Autowired
    FeatureToolbox featureToolbox;

    @Test
    void batchCreateMysqlExistFeatures() {
        featureToolbox.batchCreateMysqlExistFeatures();
    }
}