package pub.yuntu.superbrain.gpt.util.nebula;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.gpt.util.nebula.dao.Nebula3Properties;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024年03月11日 11:20:31
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class NebulaPropertiesTest {

    @Autowired
    Nebula3Properties nebula3Properties;

    @Test
    public void testReadProperty() {
        log.info("host: {}", nebula3Properties.getHost());
        log.info("user: {}", nebula3Properties.getUser());
        log.info("password: {}", nebula3Properties.getPassword());
        log.info("minConnsSize: {}", nebula3Properties.getMinConnsSize());
        log.info("maxConnsSize: {}", nebula3Properties.getMaxConnsSize());
        log.info("timeout: {}", nebula3Properties.getTimeout());
        log.info("idleTime: {}", nebula3Properties.getIdleTime());
        log.info("Meta: {}", nebula3Properties.getMeta().getPort());
        log.info("Graph: {}", nebula3Properties.getGraph().getPort());
        log.info("Storage: {}", nebula3Properties.getStorage().getPort());
    }

}
