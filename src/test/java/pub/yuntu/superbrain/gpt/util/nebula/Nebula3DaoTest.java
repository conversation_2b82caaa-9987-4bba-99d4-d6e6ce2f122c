package pub.yuntu.superbrain.gpt.util.nebula;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.gpt.util.nebula.dao.Nebula3Dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024年03月11日 14:59:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class Nebula3DaoTest {
    @Autowired
    Nebula3Dao nebula3Dao;

    public static final String USER = "UnitTest";

    @Test
    void executeCreateTag() throws SQLException {
        boolean execute = nebula3Dao.execute("CREATE TAG IF NOT EXISTS person(name string, age int);" +
                "CREATE EDGE IF NOT EXISTS like(likeness double)", USER);
        assertTrue(execute);
    }

    @Test
    void executeInsertVertex() throws SQLException {
        String insertVertexes = "INSERT VERTEX person(name, age) VALUES "
                + "'Bob':('Bob', 10), "
                + "'Lily':('Lily', 9), "
                + "'Tom':('Tom', 10), "
                + "'Jerry':('Jerry', 13), "
                + "'John':('John', 11);";
        boolean execute = nebula3Dao.execute(insertVertexes, USER);
        assertTrue(execute);

        String insertEdges = "INSERT EDGE like(likeness) VALUES "
                + "'Bob'->'Lily':(80.0), "
                + "'Bob'->'Tom':(70.0), "
                + "'Lily'->'Jerry':(84.0), "
                + "'Tom'->'Jerry':(68.3), "
                + "'Bob'->'John':(97.2);";
        execute = nebula3Dao.execute(insertEdges, USER);
        assertTrue(execute);
    }

    @Test
    void query() throws SQLException {
        String ngql = "GO FROM \"Bob\" OVER like "
                + "YIELD $^.person.name as name, $^.person.age as age, like.likeness as likeness";
        nebula3Dao.query(ngql, (ResultSet resultSet) -> {
            while (resultSet.next()) {
                String name = resultSet.getString("name");
                int age = resultSet.getInt("age");
                double likeness = resultSet.getDouble("likeness");
                log.info("name: {}, age: {}, likeness: {}", name, age, likeness);
            }
            return null;
        }, USER);
    }

}