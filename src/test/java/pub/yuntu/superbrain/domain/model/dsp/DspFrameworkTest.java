package pub.yuntu.superbrain.domain.model.dsp;

import com.google.common.collect.Lists;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pub.yuntu.superbrain.domain.model.dsp.param.DspParam;
import pub.yuntu.superbrain.infrastructure.util.JsonUtil;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 结论逻辑 2.0 单元测试
 * @createTime 2023年08月17日 15:36:18
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DspFrameworkTest extends TestCase {

    private final String FILE_PATH = "/Users/<USER>/800_Code/YT-Code/YT-Superbrain/src/main/java/pub/yuntu/superbrain/domain/model/dsp/json/";

    @Autowired
    DspFramework dspFramework;

    @Test
    public void testProcessing() {
        DspParam dspParam = initDspParam("sample.json");
        dspFramework.processing(dspParam);
    }

    /**
     * 批量测试
     */
    @Test
    public void testBatchProcessing() {
        List<String> fileList = Lists.newArrayList(
                "01_根据切分总数动态输出前n名.json",
                "02_切分对基准影响度.json",
                "10_切分动态对比基准.json",
                "26_切分组均衡性.json"
        );
        for (String fileName : fileList) {
            DspParam dspParam = initDspParam(fileName);
            dspFramework.processing(dspParam);
        }
    }

    private DspParam initDspParam(String fileName) {
        String configText = this.loadJsonConfig(fileName);
        DspParam dspParam = JsonUtil.jsonMapper.fromJson(configText, DspParam.class);
        if (null == dspParam) {
            throw new NullPointerException("解析DSP 参数失败，结果为 Null");
        }
        return dspParam;
    }

    /**
     * 加载配置 json
     */
    private String loadJsonConfig(String fileName) {
        String filePath = FILE_PATH + fileName;
        try {
            return new String(Files.readAllBytes(Paths.get(filePath)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}