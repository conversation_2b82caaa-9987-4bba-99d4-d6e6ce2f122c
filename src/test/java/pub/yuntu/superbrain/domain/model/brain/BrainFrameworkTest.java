package pub.yuntu.superbrain.domain.model.brain;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023年05月04日 18:10:43
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BrainFrameworkTest extends TestCase {

    @Autowired
    BrainFramework brainFramework;

    @Test
    public void testBuildByShot() throws InterruptedException {
        for (int i = 0; i < 10000; i++) {
            System.out.println("--- Test - " + i);
            brainFramework.build("KBPQ20E4VZQI5S1Z9UE", "mm");
            Thread.sleep(1000L);
        }
    }
}