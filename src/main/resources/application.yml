spring:
  application:
    name: YT-Superbrain
  profiles:
    active: dev
  server:
    port: 10109
  devtools:
    restart:
      enabled: true
    livereload:
      enable: true
  jackson:
    date-format: "yyyy-MM-dd HH:mm:ss"
    time-zone: GMT+8
    default-property-inclusion: non_null
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    database: mysql
    properties:
      hibernate:
        show_sql: false
        dialect: org.hibernate.dialect.MySQLDialect
        use_query_cache: false
        use_second_level_cache: false
        generate_statistics: false
  datasource:
    primary:
      driver-class-name: com.mysql.jdbc.Driver
#      jdbc-url: *************************************************************************************************************************************************************************************************************
      jdbc-url: jdbc:mysql://*************:3306/wl?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull&autoReconnect=true&autoReconnectForPools=true&useServerPrepStmts=false&rewriteBatchedStatements=true
      username: root
      password: 'ABQqfF*Xm7EUyn)D7Vjq7MiubsU8r8'
#      password: 'rootroot'
      type: com.zaxxer.hikari.HikariDataSource
      pool-name: YT-Superbrain-DB-Pool
      ## 最小空闲连接数量
      minimum-idle: 10
      ## 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 60000
      ## 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 1800000
      ## 连接池最大连接数，默认是10
      maximum-pool-size: 100
      ## 此属性控制从池返回的连接的默认自动提交行为,默认值：true
      auto-commit: true
      ## 数据库连接超时时间,默认30秒，即30000
      connection-timeout: 60000
      connection-test-query: SELECT 1
    secondary:
      driver-class-name: com.mysql.jdbc.Driver
      #      jdbc-url: *************************************************************************************************************************************************************************************************************
      jdbc-url: jdbc:mysql://*************:3306/wl?useUnicode=true&characterEncoding=UTF8&zeroDateTimeBehavior=convertToNull&autoReconnect=true&autoReconnectForPools=true&useServerPrepStmts=false&rewriteBatchedStatements=true
      username: root
      password: 'ABQqfF*Xm7EUyn)D7Vjq7MiubsU8r8'
      type: com.zaxxer.hikari.HikariDataSource
      pool-name: YT-Superbrain-YTDB-Pool
      minimum-idle: 2
      idle-timeout: 0
      max-lifetime: 1800000
      maximum-pool-size: 10
      auto-commit: true
      connection-timeout: 30000
      connection-test-query: SELECT 1
  data:
    rest:
      base-path: '/api'
#  redis:
#    # Redis数据库索引（默认为0）
#    database: 0
#    # Redis服务器地址
#    host: 127.0.0.1
#    # Redis服务器连接端口
#    port: 6379
#    # Redis服务器连接密码（默认为空）
#    password: WEBtriNRijxjRG8C4ob8izfNcraRekt
#    # 连接池最大连接数（使用负值表示没有限制） 默认 8
#    max-active: 400
#    # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
#    max-wait: -1
#    # 连接池中的最大空闲连接 默认 8
#    max-idle: 8
#    # 连接池中的最小空闲连接 默认 0
#    min-idle: 8
#    # 连接超时时间（毫秒）
#    timeout: 5000

server:
  port: 10109
  tomcat:
    max-threads: 2000
    uri-encoding: UTF-8
  compression:
    enabled: true
    mime-types: application/json

security:
  user:
    name: yt-service
    password: "p6QqyH2^LYZrHwGLDc2#(ZUfv"

auth-props:
  username: ${security.user.name}
  password: ${security.user.password}

nebula3:
  host: *************
  meta:
    port: 8559
  graph:
    port: 8669
  storage:
    port: 8779
  user: "yt_service"
  password: "R63quvAMQ8pq4VXgVvL"
  spaceName: dips
  minConnsSize: 2
  maxConnsSize: 100
  # 1000 * 60 * 8
  timeout: 480000
  idleTime: 727
  intervalIdle: 1256
  waitTime: 1256

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 600000

ribbon:
  ReadTimeout: 600000
  ConnectTimeout: 600000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1
  eureka:
    enabled: false

---
# 本地开发 Profile
spring:
  profiles: dev

zuul:
  max:
    host:
      connections: 500
  host:
    socket-timeout-millis: 600000
    connect-timeout-millis: 600000
  sensitiveHeaders:
  routes:
    customers:
      path: /api/cus/**
      url: http://**************:10081/api/
    exams:
      path: /api/exam/**
      url: http://**************:10083/api/
    auth:
      path: /api/auth/**
      url: http://**************:10082/api/
    ml:
      path: /api/ml/**
      url: http://*************:10095/api/
    agent:
      path: /api/agent/**
      url: http://**************:10086/api/

yuntu.rest-api:
  basic:
    name: ${security.user.name}
    password: ${security.user.password}
  uri:
    local: "http://127.0.0.1:10109"
    sms: "http://**************:10085/api/"
    itgVk: "http://localhost:10101/api/"
  file:
    #    base: "/Users/<USER>/IdeaProjects/YT/YT-Fileroot/"
    base: "/Users/<USER>/100_Work_工作/110 Work FG | 赛惟/113 Work FG YT | 云途/113.7 Work FG YT Run Temp"
  elastic: *************
  esswitch: false
  esuser: elastic
  espass: 1qaz2wsx

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} %yellow([%-5level]) %cyan([%X{AppName}][%thread] %logger{36}[%M][%L]) - %msg%n"
  level:
    org:
      springframework: info
      hibernate: info
    net:
      sf: warn
    pub:
      yuntu: info
