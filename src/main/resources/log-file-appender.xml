<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <contextName>${appName}</contextName>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss} %yellow([%-5level]) %cyan([%X{AppName}][%thread] %logger{36}[%M][%L]) - %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/logs/${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/logs/${appName}.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5level] [%X{AppName}][%thread] %logger{36}[%M][%L] - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.springframework" level="info">
        <appender-ref ref="rollingFile"/>
    </logger>
    <logger name="org.hibernate" level="info">
        <appender-ref ref="rollingFile"/>
    </logger>
    <logger name="net.sf" level="warn">
        <appender-ref ref="rollingFile"/>
    </logger>
    <logger name="com.vesoft" level="warn">
        <appender-ref ref="rollingFile"/>
    </logger>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="rollingFile"/>
    </root>
</configuration>