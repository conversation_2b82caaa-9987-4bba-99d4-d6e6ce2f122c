#æ¬éç½®æä»¶ä¸­çè·¯å¾çæ ¹ç®å½ï¼æ ¹ç®å½+å¶ä»è·¯å¾=å®æ´è·¯å¾ï¼æ¯æç¸å¯¹è·¯å¾ï¼è¯·åèï¼https://github.com/hankcs/HanLP/pull/254ï¼
#Windowsç¨æ·è¯·æ³¨æï¼è·¯å¾åéç¬¦ç»ä¸ä½¿ç¨/
root=/mnt/yt-files/hanlp/

#æ ¸å¿è¯å¸è·¯å¾
#CoreDictionaryPath=data/dictionary/CoreNatureDictionary.txt
#2åè¯­æ³è¯å¸è·¯å¾
#BiGramDictionaryPath=data/dictionary/CoreNatureDictionary.ngram.txt
#èªå®ä¹è¯å¸è·¯å¾ï¼ç¨;éå¼å¤ä¸ªèªå®ä¹è¯å¸ï¼ç©ºæ ¼å¼å¤´è¡¨ç¤ºå¨åä¸ä¸ªç®å½ï¼ä½¿ç¨âæä»¶å è¯æ§âå½¢å¼åè¡¨ç¤ºè¿ä¸ªè¯å¸çè¯æ§é»è®¤æ¯è¯¥è¯æ§ãä¼åçº§éåã
#ææè¯å¸ç»ä¸ä½¿ç¨UTF-8ç¼ç ï¼æ¯ä¸è¡ä»£è¡¨ä¸ä¸ªåè¯ï¼æ ¼å¼éµä»[åè¯] [è¯æ§A] [Açé¢æ¬¡] [è¯æ§B] [Bçé¢æ¬¡] ... å¦æä¸å¡«è¯æ§åè¡¨ç¤ºéç¨è¯å¸çé»è®¤è¯æ§ã
#CustomDictionaryPath=data/dictionary/custom/CustomDictionary.txt; ç°ä»£æ±è¯­è¡¥åè¯åº.txt; å¨å½å°åå¤§å¨.txt ns; äººåè¯å¸.txt; æºæåè¯å¸.txt; ä¸æµ·å°å.txt ns;data/dictionary/person/nrf.txt nrf;
CustomDictionaryPath=data/dictionary/custom/æ¶é´.txt; è§¦ç¹.txt;
#åç¨è¯è¯å¸è·¯å¾
CoreStopWordDictionaryPath=data/dictionary/stopwords.txt
#åä¹è¯è¯å¸è·¯å¾
#CoreSynonymDictionaryDictionaryPath=data/dictionary/synonym/CoreSynonym.txt
#äººåè¯å¸è·¯å¾
#PersonDictionaryPath=data/dictionary/person/nr.txt
#äººåè¯å¸è½¬ç§»ç©éµè·¯å¾
#PersonDictionaryTrPath=data/dictionary/person/nr.tr.txt
#ç¹ç®è¯å¸æ ¹ç®å½
#tcDictionaryRoot=data/dictionary/tc
#HMMåè¯æ¨¡å
#HMMSegmentModelPath=data/model/segment/HMMSegmentModel.bin
#åè¯ç»ææ¯å¦å±ç¤ºè¯æ§
#ShowTermNature=true
#IOééå¨ï¼å®ç°com.hankcs.hanlp.corpus.io.IIOAdapteræ¥å£ä»¥å¨ä¸åçå¹³å°ï¼HadoopãRedisç­ï¼ä¸è¿è¡HanLP
#é»è®¤çIOééå¨å¦ä¸ï¼è¯¥ééå¨æ¯åºäºæ®éæä»¶ç³»ç»çã
#IOAdapter=com.hankcs.hanlp.corpus.io.FileIOAdapter
#æç¥æºè¯æ³åæå¨
#PerceptronCWSModelPath=data/model/perceptron/pku1998/cws.bin
#PerceptronPOSModelPath=data/model/perceptron/pku1998/pos.bin
#PerceptronNERModelPath=data/model/perceptron/pku1998/ner.bin
#CRFè¯æ³åæå¨
#CRFCWSModelPath=data/model/crf/pku199801/cws.txt
#CRFPOSModelPath=data/model/crf/pku199801/pos.txt
#CRFNERModelPath=data/model/crf/pku199801/ner.txt
#æ´å¤éç½®é¡¹è¯·åè https://github.com/hankcs/HanLP/blob/master/src/main/java/com/hankcs/hanlp/HanLP.java#L59 èªè¡æ·»å 