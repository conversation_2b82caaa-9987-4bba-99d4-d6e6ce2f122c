package pub.yuntu.superbrain.gpt.frame.action.business;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.gpt.frame.action.business.meta.GptActionMeta;
import pub.yuntu.superbrain.gpt.frame.action.business.meta.GptIndex;
import pub.yuntu.superbrain.gpt.frame.action.business.meta.GptSplit;
import pub.yuntu.superbrain.gpt.frame.treaty.ExpressionEvaluator;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 差值计算动作
 * @createTime 2024/3/27 11:05 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "action", defaultImpl = DiffAction.class, visible = true)
public class DiffAction extends BusinessAction {

    String left;
    String right;

    @Override
    protected void doProcess(GptActionMeta meta) {
        // checkCondition 检查meta单元是否符合配置的condition
        boolean enter = super.checkCondition(meta);
        if (!enter)
            return;
        // 获取计算meta
        List<GptActionMeta> computeMetaList = meta.getComputeMetaList();
        for (GptActionMeta rightMeta : computeMetaList) {
            Object compute = compute(meta, rightMeta);
            // 生成变量名
            String variableName = produceVariableName(rightMeta);
            // 保存
            super.saveResult(meta, variableName, compute);
        }
    }

    private Object compute(GptActionMeta leftMeta, GptActionMeta rightMeta) {
        Object leftParse = variableParser.parse(context, variable, leftMeta);
        Object rightParse = variableParser.parse(context, variable, rightMeta);
        if (leftParse == null || rightParse == null)
            return null;
        // 通过表达式计算
        String expression = leftParse + " - " + rightParse;
        Object result = ExpressionEvaluator.evaluateArithmetic(expression);
        return result;
    }

    protected List<GptActionMeta> diffLoop(List<GptSplit> gptSplitList,
                                           List<String> attributeList,
                                           List<GptIndex> indexList,
                                           List<String> periodList) {
        switch (loop) {
            case "split":
                return splitLoop(gptSplitList, attributeList, indexList, periodList);
            case "attribute":
                return attributeLoop(gptSplitList, attributeList, indexList, periodList);
            case "period":
                return periodLoop(gptSplitList, attributeList, indexList, periodList);
            case "index":
                return indexLoop(gptSplitList, attributeList, indexList, periodList);
        }
        return null;
    }

    private List<GptActionMeta> splitLoop(List<GptSplit> gptSplitList,
                                          List<String> attributeList,
                                          List<GptIndex> indexList,
                                          List<String> periodList) {
        // 当前属性集合需作为切分转化的条件
        List<GptSplit> allAttributeList = super.transformToAttributeSplit(gptSplitList, attributeList);
        List<GptActionMeta> resultMetaList = Lists.newArrayList();
        if (StringUtils.isBlank(left))
            left = "$split_self";
        if (StringUtils.isBlank(right))
            right = "$split_self";
        String[] leftSplits = left.split(",");
        String[] rightSplits = right.split(",");
        // 开始循环
        for (String period : periodList) {
            for (GptIndex gptIndex : indexList) {
                for (GptSplit gptSplit : allAttributeList) {
                    // 需要先建一个meta再去解析split参数
                    GptActionMeta processMeta = new GptActionMeta(period, gptIndex, gptSplit);
                    // 解析左右
                    List<GptSplit> parsedLeftSplitList = Lists.newArrayList();
                    List<GptSplit> parsedRightSplitList = Lists.newArrayList();
                    for (String leftSplit : leftSplits) {
                        List<GptSplit> leftGptSplits = super.processSplitVariable(leftSplit, processMeta);
                        parsedLeftSplitList.addAll(leftGptSplits);
                    }
                    for (String rightSplit : rightSplits) {
                        List<GptSplit> rightGptSplits = super.processSplitVariable(rightSplit, processMeta);
                        parsedRightSplitList.addAll(rightGptSplits);
                    }
                    // 开始生成真正的ActionMeta
                    for (GptSplit parsedLeftSplit : parsedLeftSplitList) {
                        GptActionMeta mountedMeta = new GptActionMeta(period, gptIndex, parsedLeftSplit);
                        mountedMeta.setComputeMetaList(Lists.newArrayList());
                        if (parsedRightSplitList.isEmpty())
                            continue;
                        for (GptSplit parsedRightSplit : parsedRightSplitList) {
                            GptActionMeta parsedRightMeta = new GptActionMeta(period, gptIndex, parsedRightSplit);
                            mountedMeta.getComputeMetaList().add(parsedRightMeta);
                        }
                        resultMetaList.add(mountedMeta);
                    }
                }
            }
        }
        return resultMetaList;
    }

    private List<GptActionMeta> attributeLoop(List<GptSplit> gptSplitList,
                                              List<String> attributeList,
                                              List<GptIndex> indexList,
                                              List<String> periodList) {
        List<GptActionMeta> resultMetaList = Lists.newArrayList();
        // 获取left,right的属性集合
        String[] leftAttributes = left.split(",");
        String[] rightAttributes = right.split(",");
        List<String> parsedLeftAttributeList = Lists.newArrayList();
        List<String> parsedRightAttributeList = Lists.newArrayList();
        // 因为填入的属性可能包含表达式，因此需先解析表达式
        for (String leftAttribute : leftAttributes) {
            if (leftAttribute.startsWith("$attr_")) {
                List<String> parseALeftVariableList = (List<String>) variableParser.parse(context, leftAttribute);
                parsedLeftAttributeList.addAll(parseALeftVariableList);
            } else
                parsedLeftAttributeList.add(leftAttribute);
        }
        for (String rightAttribute : rightAttributes) {
            if (rightAttribute.startsWith("$attr_")) {
                List<String> parseARightVariableList = (List<String>) variableParser.parse(context, rightAttribute);
                parsedLeftAttributeList.addAll(parseARightVariableList);
            } else
                parsedRightAttributeList.add(rightAttribute);
        }
        // 开始循环
        for (String period : periodList) {
            for (GptIndex gptIndex : indexList) {
                for (GptSplit gptSplit : gptSplitList) {
                    for (String leftAttribute : parsedLeftAttributeList) {
                        // 转换属性的gptSplit
                        GptSplit leftAttributeGptSplit = gptSplit.copyAndChangeAttribute(leftAttribute, gptSplit);
                        GptActionMeta mountedMeta = new GptActionMeta(period, gptIndex, leftAttributeGptSplit);
                        mountedMeta.setComputeMetaList(Lists.newArrayList());
                        // 开始解析right
                        for (String rightAttribute : parsedRightAttributeList) {
                            GptSplit rightAttributeGptSplit = gptSplit.copyAndChangeAttribute(rightAttribute, gptSplit);
                            GptActionMeta rightAttributeMeta = mountedMeta.copyAndChangeSplit(mountedMeta, rightAttributeGptSplit);
                            mountedMeta.getComputeMetaList().add(rightAttributeMeta);
                        }
                        resultMetaList.add(mountedMeta);
                    }
                }
            }
        }
        return resultMetaList;
    }

    private List<GptActionMeta> periodLoop(List<GptSplit> gptSplitList,
                                           List<String> attributeList,
                                           List<GptIndex> indexList,
                                           List<String> periodList) {
        // 当前属性集合需作为切分转化的条件
        List<GptSplit> allAttributeList = super.transformToAttributeSplit(gptSplitList, attributeList);
        List<GptActionMeta> resultMetaList = Lists.newArrayList();
        // 获取left,right的分期集合
        String[] leftPeriods = left.split(",");
        String[] rightPeriods = right.split(",");
        // 开始循环
        for (GptIndex gptIndex : indexList) {
            for (GptSplit gptSplit : allAttributeList) {
                for (String leftPeriod : leftPeriods) {
                    GptActionMeta mountedMeta = new GptActionMeta(leftPeriod, gptIndex, gptSplit);
                    mountedMeta.setComputeMetaList(Lists.newArrayList());
                    for (String rightPeriod : rightPeriods) {
                        GptActionMeta rightPeriodMeta = mountedMeta.copyAndChangePeriod(mountedMeta, rightPeriod);
                        mountedMeta.getComputeMetaList().add(rightPeriodMeta);
                    }
                    resultMetaList.add(mountedMeta);
                }
            }
        }
        return resultMetaList;
    }

    private List<GptActionMeta> indexLoop(List<GptSplit> gptSplitList,
                                          List<String> attributeList,
                                          List<GptIndex> indexList,
                                          List<String> periodList) {
        // 当前属性集合需作为切分转化的条件
        List<GptSplit> allAttributeList = super.transformToAttributeSplit(gptSplitList, attributeList);
        List<GptActionMeta> resultMetaList = Lists.newArrayList();
        // 获取left,right的指标集合
        String[] leftIndices = left.split(",");
        if (StringUtils.equalsIgnoreCase(left, "all"))
            leftIndices = indexList.stream().map(GptIndex::getIndexId).toArray(String[]::new);
        String[] rightIndices = right.split(",");
        if (StringUtils.equalsIgnoreCase(right, "all"))
            rightIndices = indexList.stream().map(GptIndex::getIndexId).toArray(String[]::new);
        // 开始循环
        for (String period : periodList) {
            for (GptSplit gptSplit : allAttributeList) {
                for (String leftIndex : leftIndices) {
                    // 获取GptIndex
                    String leftExpression = "$indexId[" + leftIndex + "]";
                    GptIndex parsedLeftIndex = (GptIndex) variableParser.parse(context, leftExpression);
                    GptActionMeta mountedMeta = new GptActionMeta(period, parsedLeftIndex, gptSplit);
                    mountedMeta.setComputeMetaList(Lists.newArrayList());
                    for (String rightIndex : rightIndices) {
                        String rightExpression = "$indexId[" + rightIndex + "]";
                        GptIndex parsedRightIndex = (GptIndex) variableParser.parse(context, rightExpression);
                        GptActionMeta rightIndexMeta = mountedMeta.copyAndChangeIndex(mountedMeta, parsedRightIndex);
                        mountedMeta.getComputeMetaList().add(rightIndexMeta);
                    }
                    resultMetaList.add(mountedMeta);
                }
            }
        }
        return resultMetaList;
    }


    /**
     * @Description 根据不同的循环会有不同的变量名
     */
    @Override
    protected String produceVariableName(GptActionMeta computeMeta) {
        if (StringUtils.isNotBlank(returnValue))
            return "$diff_" + returnValue;
        switch (loop) {
            case "split":
                return "$diff_split_对比" + computeMeta.getGptsplit().getOrgUnitName();
            case "attribute":
                return "$diff_attribute_对比" + computeMeta.getGptsplit().getAttribute();
            case "period":
                return "$diff_period_对比" + computeMeta.getPeriod();
            case "index":
                return "$diff_index_对比" + computeMeta.getGptIndex().getIndexId();
        }
        IllegalArgumentException e = new IllegalArgumentException("diff动作暂不支持的loop命名: " + loop);
        log.error("", e);
        throw e;
    }
}
