package pub.yuntu.superbrain.gpt.frame.action.visual;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.string.WgStringUtil;
import pub.yuntu.superbrain.gpt.frame.action.visual.parser.AbstractGptSegment;
import pub.yuntu.superbrain.gpt.frame.action.visual.parser.GptSegmentChain;
import pub.yuntu.superbrain.gpt.frame.action.visual.point.AbstractGptDataPoint;
import pub.yuntu.superbrain.gpt.frame.context.GptContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by Qianyi on 2024/5/8 11:56.
 */
@Data
public class GptCondition {
    String data;  // 取数表达式
    String text;  // 文本表达式
    String expression;  // 条件表达式，中括号之内是“且”的关系，中括号与中括号之间是“或”的关系

    public boolean isMatch(GptContext context, VisualParam visualParam) {
        List<AbstractGptDataPoint> dataPointList = new ArrayList<>();
        if (StringUtils.isNotBlank(this.data)) {
            GptSegmentChain chain = context.getComponents().getGptTextParser().parse(this.data);
            dataPointList = chain.asDataPointList(context, visualParam);
        }

        String text = "";
        if (StringUtils.isNotBlank(this.text)) {
            GptSegmentChain chain = context.getComponents().getGptTextParser().parse(this.text);
            text = chain.asText(context, visualParam);
        }

        // [xx,xx:xx:xx][xx]
        List<String> exps = WgStringUtil.matchText(AbstractGptSegment.p_squareBrackets, expression, 0);
        List<Boolean> expMatchList = new ArrayList<>();

        for (String exp : exps) {
            exp = exp.replaceAll("[\\[\\]]", "");
            String[] expParts = exp.split(",");
            boolean expMatch = true;

            for (String expPart : expParts) {
                boolean partMatch = true;
                String[] expSections = expPart.split(":");
                switch (expSections[0]) {
                    case "exist":
                        if (dataPointList == null || dataPointList.isEmpty())
                            partMatch = false;
                        break;
                    case "notExist":
                        if (dataPointList != null && !dataPointList.isEmpty())
                            partMatch = false;
                        break;
                    case "equal":
                        // [equal:361/250]
                        String[] shouldEqualTexts = expSections[1].split("/");
                        if (!Arrays.asList(shouldEqualTexts).contains(text))
                            partMatch = false;
                        break;
                    case "notEqual":
                        // [notEqual:361/250]
                        String[] shouldNotEqualTexts = expSections[1].split("/");
                        if (Arrays.asList(shouldNotEqualTexts).contains(text))
                            partMatch = false;
                        break;
                }
                expMatch = expMatch && partMatch;
            }

            expMatchList.add(expMatch);
        }

        // 只要有一个条件块为true，则 true
        return expMatchList.contains(true);
    }
}
