package pub.yuntu.superbrain.gpt.frame.action.visual.point.producer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.string.WgStringUtil;
import pub.yuntu.superbrain.gpt.frame.action.visual.VisualParam;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptTableBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.parser.GptDataExpSegment;
import pub.yuntu.superbrain.gpt.frame.action.visual.point.AbstractGptDataPoint;
import pub.yuntu.superbrain.gpt.frame.context.GptContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by Qianyi on 2024/5/13 18:25.
 */
@Slf4j
@Data
public abstract class AbstractGptPointProducer {
    public static List<String> ignoreTexts = Arrays.asList("没有", "没", "无", "无。", "暂无", "", "null", "NULL");
    public static String[] cityKeyWords = {"市", "自治州", "地区", "县", "盟"};

    public abstract List<AbstractGptDataPoint> produce(GptContext context,
                                                       VisualParam visualParam,
                                                       GptDataExpSegment gptDataExpSegment);

//    public MatchedCapturePeriod mostMatchedCapturePeriod(CreativityParam param,
//                                                         GptDataExpSegment gptDataExpSegment,
//                                                         String dataPeriod) {
//        List<DataCapturePeriodDTO> dataCapturePeriodList = param.getCommercialParam().getDataCapturePeriods()
//                .stream()
//                .filter(x -> x.getExam() != null)
//                .filter(x -> (x.getExam().getOrgCategoryID() != null && StringUtils.equalsIgnoreCase(x.getExam().getOrgCategoryID().getValue(), gptDataExpSegment.getOrgCategoryId()))
//                        || (x.getExam().getOrgCategoryTwoID() != null && StringUtils.equalsIgnoreCase(x.getExam().getOrgCategoryTwoID().getValue(), gptDataExpSegment.getOrgCategoryId())))
//                .collect(Collectors.toList());
//
//        List<MatchedCapturePeriod> list = new ArrayList<>();
//        dataCapturePeriodList.forEach(x -> list.add(new MatchedCapturePeriod(dataPeriod, x)));
//        list.sort((x1, x2) -> x2.getOverlapRatio().compareTo(x1.getOverlapRatio()));
//        if (list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    public MatchedCapturePeriod mostMatchedMsdCapturePeriod(CreativityParam param,
//                                                            GptDataExpSegment gptDataExpSegment,
//                                                            String dataPeriod) {
//        List<MsdDataCapturePeriodDTO> dataCapturePeriodList = param.getCommercialParam().getMsdDataCapturePeriods()
//                .stream()
//                .filter(x -> x.getExam() != null)
//                .filter(x -> (x.getExam().getOrgCategoryID() != null && StringUtils.equalsIgnoreCase(x.getExam().getOrgCategoryID().getValue(), gptDataExpSegment.getOrgCategoryId()))
//                        || (x.getExam().getOrgCategoryTwoID() != null && StringUtils.equalsIgnoreCase(x.getExam().getOrgCategoryTwoID().getValue(), gptDataExpSegment.getOrgCategoryId())))
//                .collect(Collectors.toList());
//
//        List<MatchedCapturePeriod> list = new ArrayList<>();
//        dataCapturePeriodList.forEach(x -> list.add(new MatchedCapturePeriod(dataPeriod, x)));
//        list.sort((x1, x2) -> x2.getOverlapRatio().compareTo(x1.getOverlapRatio()));
//        if (list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }

    public void setPointName(GptContext context,
                             GptDataExpSegment gptDataExpSegment,
                             AbstractGptDataPoint point) {
        point.setNameByExp(context, gptDataExpSegment.getNameType());
        point.setXNameByExp(context, gptDataExpSegment.getXNameType());
        point.setYNameByExp(context, gptDataExpSegment.getYNameType());
    }

    public void addPointWithoutValue(VisualParam visualParam,
                                     GptDataExpSegment gptDataExpSegment,
                                     AbstractGptDataPoint dataPoint) {
        if (dataPoint.getValue() != null || dataPoint.getStrValue() != null)
            return;

        if (visualParam.getCurrentBlock() instanceof GptTableBlock) {
            List<AbstractGptDataPoint> list = visualParam.getCurrentBlock().getDataPointsWithoutValueMap()
                    .computeIfAbsent("{" + gptDataExpSegment.getExpression() + "}", k -> new ArrayList<>());
            list.add(dataPoint);
        }
    }

//    public static SplitUnit findSplitUnit(CreativityParam param, Map<Long, SplitUnit> idAndSplitUnitMap, long splitUnitDbId) {
//        // 在调用此方法之前判断 idAndSplitUnitMap 是否为null，不要在这里 new HashMap<>()，否则后续的回填会因为地址改变而失效
//        SplitUnit splitUnit = idAndSplitUnitMap.get(splitUnitDbId);
//        if (splitUnit != null)
//            return splitUnit;
//
//        List<SplitUnit> units = param.getCreativityFramework().getSplitUnitRepository().findBy_id(splitUnitDbId);
//        if (units == null || units.isEmpty())
//            return null;
//
//        splitUnit = units.get(0);
//        if (splitUnit != null) {
//            String splitGroupId = splitUnit.getGroupID();
//            List<SplitUnit> splitUnits = param.getCreativityFramework().getSplitUnitRepository().findByGroupID(splitGroupId);
//            for (SplitUnit unit : splitUnits) {
//                idAndSplitUnitMap.put(unit.get_id(), unit);
//            }
//        }
//        return splitUnit;
//    }
//
//    public static AllianceSplitUnit findAllianceSplitUnit(CreativityParam param, Map<Long, AllianceSplitUnit> idAndAllianceSplitUnitMap, long splitUnitDbId) {
//        // 在调用此方法之前判断 idAndAllianceSplitUnitMap 是否为null，不要在这里 new HashMap<>()，否则后续的回填会因为地址改变而失效
//        AllianceSplitUnit splitUnit = idAndAllianceSplitUnitMap.get(splitUnitDbId);
//        if (splitUnit != null)
//            return splitUnit;
//
//        List<AllianceSplitUnit> allianceSplitUnits = param.getCreativityFramework().getAllianceSplitUnitRepository().findBy_id(splitUnitDbId);
//        if (allianceSplitUnits == null || allianceSplitUnits.isEmpty())
//            return null;
//
//        splitUnit = allianceSplitUnits.get(0);
//        if (splitUnit != null) {
//            String splitGroupId = splitUnit.getGroupID();
//            List<AllianceSplitUnit> splitUnits = param.getCreativityFramework().getAllianceSplitUnitRepository().findByGroupID(splitGroupId);
//            for (AllianceSplitUnit unit : splitUnits) {
//                idAndAllianceSplitUnitMap.put(unit.get_id(), unit);
//            }
//        }
//        return splitUnit;
//    }

    public boolean matchDataConditions(List<String[]> conditions, Map<String, Object> data) {
        if (conditions == null || conditions.isEmpty())
            return true;

        for (String[] condition : conditions) {
            if (condition.length < 3)
                continue;
            // [planarCode;completeEqual;001]
            String fieldName = condition[0];
            String operator = condition[1];
            String compareValue = condition[2];
            String value = getDataValue(data, fieldName);

            switch (operator) {
                case "completeEqual":
                    if (!StringUtils.equals(value, compareValue))
                        return false;
                    break;
                case "partEqual":
                    String[] valueParts = value.split("[(),;_-]");
                    if (!Arrays.asList(valueParts).contains(compareValue))
                        return false;
                    break;
                case "contain":
                    if (!StringUtils.contains(value, compareValue))
                        return false;
                    break;
                case "notContain":
                    if (StringUtils.contains(value, compareValue))
                        return false;
                    break;
                case "notEqual":
                    if (StringUtils.equalsIgnoreCase(value, compareValue))
                        return false;
                    break;
                case "greaterThan":
                case "greaterThanOrEqual":
                case "lessThan":
                case "lessThanOrEqual":
                    if (!isNumMatch(operator, value, compareValue))
                        return false;
                    break;
            }
        }
        return true;
    }

    private boolean isNumMatch(String operator, String toBeChecked, String compareValue) {
        if (StringUtils.isBlank(toBeChecked) || !WgStringUtil.isNum(toBeChecked) || !WgStringUtil.isNum(compareValue))
            return false;

        switch (operator) {
            case "greaterThan":
                return Double.parseDouble(toBeChecked) > Double.parseDouble(compareValue);
            case "greaterThanOrEqual":
                return Double.parseDouble(toBeChecked) >= Double.parseDouble(compareValue);
            case "lessThan":
                return Double.parseDouble(toBeChecked) < Double.parseDouble(compareValue);
            case "lessThanOrEqual":
                return Double.parseDouble(toBeChecked) <= Double.parseDouble(compareValue);
            case "equal":
                return Double.parseDouble(toBeChecked) == Double.parseDouble(compareValue);
            case "notEqual":
                return Double.parseDouble(toBeChecked) != Double.parseDouble(compareValue);
        }
        return false;
    }

    public List<String> getSubPeriods(String dataPeriod) {
        List<String> dataPeriods = new ArrayList<>();
        if (dataPeriod.length() < 9) {
            dataPeriods.add(dataPeriod);
            return dataPeriods;
        }

        String[] parts = dataPeriod.split("-");
        String year = parts[0], simpleMonths = parts[1];
        String startMonth = simpleMonths.substring(0, 2), endMonth = simpleMonths.substring(2, 4);
        int startInt = Integer.parseInt(startMonth), endInt = Integer.parseInt(endMonth);

        for (int i = startInt; i <= endInt; i++) {
            String month = i < 10 ? "0" + i : "" + i;
            dataPeriods.add(year + "-" + month);
        }
        return dataPeriods;
    }

//    public int lastYear(String dataPeriod) {
//        int year = thisYear(dataPeriod);
//        return year - 1;
//    }
//
//    public int thisYear(String dataPeriod) {
//        String yearStr = dataPeriod.split("-")[0];
//        return Integer.parseInt(yearStr);
//    }
//
//    protected List<String> filterList(List<String> list, String filterStr) {
//        return list.stream()
//                .filter(x -> StringUtils.containsIgnoreCase(x, filterStr))
//                .collect(Collectors.toList());
//    }
//
//    protected void codesOfCombinationInteraction(CreativityParam param,
//                                                 String childCodes,
//                                                 List<String> finalChildCodes) {
//        String[] childCodeArray = childCodes.split(",");
//        List<GraphInteraction> graphInteractions = param.getCreativityFramework().getGraphInteractionRepository()
//                .findByOpenTypeAndCodes("openText", Arrays.asList(childCodeArray));
//
//        for (GraphInteraction graphInteraction : graphInteractions) {
//            if (StringUtils.isNotBlank(graphInteraction.getCombinationType()) && StringUtils.isNotBlank(graphInteraction.getChildCodes())) {
//                codesOfCombinationInteraction(param, graphInteraction.getChildCodes(), finalChildCodes);
//            } else {
//                finalChildCodes.add(graphInteraction.getCode());
//            }
//        }
//    }

    public static String getDataValue(Map<String, Object> data, String key) {
        if (StringUtils.isBlank(key))
            return "";
        return data.get(key.toUpperCase()) + "";
    }

    public static boolean notBlank(String s) {
        return StringUtils.isNotBlank(s) && !StringUtils.equalsIgnoreCase(s, "null");
    }

//    public static void addData(HashMap mapData, List<AbstractGptDataPoint> points) {
//        for (AbstractGptDataPoint point : points) {
//            point.addData((HashMap<String, Object>) mapData);
//        }
//    }
//
//    public String transformCity(String splitName) {
//        for (String s : cityKeyWords) {
//            splitName = splitName.replaceAll(s, "");
//        }
//        return splitName;
//    }
//
//    public void setValueByFieldName(DataPoint dataPoint, HashMap mapData) {
//        if (StringUtils.contains(dataPoint.getDataType(), ".")) {
//            valueFromMap(mapper, mapData, dataPoint);
//        } else {
//            fieldValue(mapData, dataPoint);
//        }
//    }
//
//    public void valueFromMap(JsonMapper mapper,
//                             HashMap mapData,
//                             DataPoint dataPoint) {
//        // statResult.proportion
//        String dataType = dataPoint.getDataType();
//        if (!StringUtils.contains(dataType, "."))
//            return;
//        String[] keys = dataType.split("\\.");
//        HashMap statMap = new HashMap<>();
//
//        for (int i = 0; i < keys.length; i++) {
//            String key = keys[i];
//            if (i == 0) {
//                String statMapStr = getDataValue(mapData, key);
//                if (!notBlank(statMapStr))
//                    return;
//                statMapStr = statMapStr.replaceAll("\\\\\"", "\"");
//                statMap = mapper.fromJson(statMapStr, HashMap.class);
//
//            } else if (i < keys.length - 1) {
//                statMap = (HashMap) statMap.get(key);
//
//            } else {
//                String strValue = statMap.get(key) + "";
//                if (!notBlank(strValue))
//                    return;
//                dataPoint.setStrValue(strValue);
//
//                if (!WgStringUtil.isNum(strValue))
//                    return;
//                double value = Double.parseDouble(strValue);
//                if (StringUtils.containsIgnoreCase(key, "proportion") || StringUtils.containsIgnoreCase(key, "percent"))
//                    value = value * 100D;
//                dataPoint.setValue(value);
//            }
//        }
//    }
//
//    public void fieldValue(HashMap mapData, DataPoint dataPoint) {
//        String strValue = getDataValue(mapData, dataPoint.getDataType());
//        if (!notBlank(strValue))
//            return;
//        dataPoint.setStrValue(strValue);
//
//        if (!WgStringUtil.isNum(strValue))
//            return;
//        double value = Double.parseDouble(strValue);
//        dataPoint.setValue(value);
//    }
//
//    public List<AbstractGptDataPoint> setPeriod(GptDataExpSegment gptDataExpSegment, List<AbstractGptDataPoint> originalPoints) {
//        List<String> periods = gptDataExpSegment.getPeriods();
//        if (periods == null || periods.isEmpty())
//            return originalPoints;
//
//        List<AbstractGptDataPoint> temp = new ArrayList<>();
//
//        for (String period : periods) {
//            for (AbstractGptDataPoint originalPoint : originalPoints) {
//                DataPoint dataPoint = ((DataPoint) originalPoint).deepClone(mapper);
//                dataPoint.setDataPeriod(period);
//                temp.add(dataPoint);
//            }
//        }
//
//        return temp;
//    }
//
//    public List<AbstractGptDataPoint> setSplit(GptDataExpSegment gptDataExpSegment,
//                                            List<AbstractGptDataPoint> originalPoints,
//                                            CommercialParam commercialParam) {
//        List<Long> splitUnitDbIdList = gptDataExpSegment.getSplitUnitDbIdList();
//        if (splitUnitDbIdList == null || splitUnitDbIdList.isEmpty())
//            return originalPoints;
//
//        List<AbstractGptDataPoint> temp = new ArrayList<>();
//
//        for (Long splitUnitDbId : splitUnitDbIdList) {
//            FeatureSplitUnitDTO splitUnit = commercialParam.getFullSplitMap().get(splitUnitDbId + "");
//            if (splitUnit == null)
//                splitUnit = commercialParam.getFullAllianceSplitMap().get(splitUnitDbId + "");
//            if (splitUnit == null)
//                continue;
//
//            String orgUnitCode = null;
//            if (splitUnit instanceof SplitUnitDTO)
//                orgUnitCode = ((SplitUnitDTO) splitUnit).getOrgUnitCode();
//
//            Long splitGroupDbId = null;
//            SplitGroupDTO splitGroup = commercialParam.findSplitGroup(splitUnit.splitGroupId());
//            if (splitGroup != null) {
//                splitGroupDbId = splitGroup.get_id();
//            }
//            if (splitGroupDbId == null) {
//                AllianceSplitGroupDTO allianceSplitGroup = commercialParam.findAllianceSplitGroup(splitUnit.splitGroupId());
//                if (allianceSplitGroup != null) {
//                    splitGroupDbId = allianceSplitGroup.get_id();
//                }
//            }
//
//            for (AbstractGptDataPoint originalPoint : originalPoints) {
//                DataPoint dataPoint = ((DataPoint) originalPoint).deepClone(mapper);
//                dataPoint.setSplitName(splitUnit.name());
//                dataPoint.setSplitUnitDbId(splitUnitDbId);
//                dataPoint.setSplitUnitId(splitUnit.getId());
//                dataPoint.setOrgUnitCode(orgUnitCode);
//                dataPoint.setSplitGroupDbId(splitGroupDbId);
//                temp.add(dataPoint);
//            }
//        }
//
//        return temp;
//    }
//
//    public List<AbstractGptDataPoint> setAttribute(GptDataExpSegment gptDataExpSegment, List<AbstractGptDataPoint> originalPoints) {
//        List<String[]> attributeList = gptDataExpSegment.getAttributeList();
//        if (attributeList == null || attributeList.isEmpty())
//            return originalPoints;
//
//        List<AbstractGptDataPoint> temp = new ArrayList<>();
//
//        for (String[] attributeArray : attributeList) {
//            String attribute = StringUtils.join(attributeArray, "-");
//
//            for (AbstractGptDataPoint originalPoint : originalPoints) {
//                DataPoint dataPoint = ((DataPoint) originalPoint).deepClone(mapper);
//                dataPoint.setAttribute(attribute);
//                temp.add(dataPoint);
//            }
//        }
//
//        return temp;
//    }
//
//    public List<AbstractGptDataPoint> setIndexCode(GptDataExpSegment gptDataExpSegment,
//                                                List<AbstractGptDataPoint> originalPoints,
//                                                CommercialParam commercialParam) {
//        List<String> indexCodeList = gptDataExpSegment.getIndexCodeList();
//        if (indexCodeList == null || indexCodeList.isEmpty())
//            return originalPoints;
//
//        List<AbstractGptDataPoint> temp = new ArrayList<>();
//
//        for (String indexCode : indexCodeList) {
//            IndexGridDTO indexGrid = commercialParam.getIndexCodeGridMap().get(indexCode);
//            if (indexGrid == null)
//                continue;
//
//            for (AbstractGptDataPoint originalPoint : originalPoints) {
//                DataPoint dataPoint = ((DataPoint) originalPoint).deepClone(mapper);
//                dataPoint.setIndexName(indexGrid.getIndexGridName(commercialParam.getCustomer().get_id()));
//                dataPoint.setHigherIsBetter(StringUtils.equalsIgnoreCase(indexGrid.getHigherIsBetter(), "Y"));
//                dataPoint.setPlanarCode(indexGrid.getPlanarIndexCode());
//                dataPoint.setIndexLevel(indexGrid.getIndexLevel());
//                dataPoint.setIndexCode(indexCode);
//                dataPoint.setIndexId(indexGrid.getLegacyIndexId());
//                temp.add(dataPoint);
//            }
//        }
//
//        return temp;
//    }
//
//    public List<AbstractGptDataPoint> setPlanarCode(GptDataExpSegment gptDataExpSegment,
//                                                 List<AbstractGptDataPoint> originalPoints,
//                                                 CommercialParam commercialParam) {
//        Map<String, GraphInteractionDTO> planarCodeAndGraphInteractionMap = gptDataExpSegment.getPlanarCodeAndGraphInteractionMap();
//        if (planarCodeAndGraphInteractionMap == null || planarCodeAndGraphInteractionMap.isEmpty())
//            return originalPoints;
//
//        List<AbstractGptDataPoint> temp = new ArrayList<>();
//
//        for (String planarCode : planarCodeAndGraphInteractionMap.keySet()) {
//            GraphInteractionDTO interaction = commercialParam.getPlanarCodeAndGraphInteractionMap().get(planarCode);
//            if (interaction == null)
//                continue;
//
//            for (AbstractGptDataPoint originalPoint : originalPoints) {
//                DataPoint dataPoint = ((DataPoint) originalPoint).deepClone(mapper);
//                dataPoint.setIndexName(interaction.getName());
//                dataPoint.setPlanarCode(planarCode);
//                dataPoint.setInteractionId(interaction.getId());
//                temp.add(dataPoint);
//            }
//        }
//
//        return temp;
//    }
//
//    public void setPeriodByData(Signal signal, DataPoint dataPoint) {
//        DataFinder dataPeriod = signal.dataPeriod();
//        if (StringUtils.isBlank(dataPoint.getDataPeriod()) && dataPeriod != null && dataPeriod.isFound()) {
//            dataPoint.setDataPeriod(dataPeriod.getValue());
//        }
//    }
//
//    public void setSplitByData(Signal signal, DataPoint dataPoint) {
//        DataFinder splitName = signal.splitName();
//        DataFinder splitGroupId = signal.splitGroupId();
//        DataFinder splitUnitId = signal.splitUnitId();
//
//        if (StringUtils.isBlank(dataPoint.getSplitName())) {
//            if (splitName != null && splitName.isFound()) {
//                dataPoint.setSplitName(splitName.getValue());
//            }
//            if (splitGroupId != null && splitGroupId.isFound()) {
//                dataPoint.setSplitGroupDbId(Long.parseLong(splitGroupId.getValue()));
//            }
//            if (splitUnitId != null && splitUnitId.isFound()) {
//                dataPoint.setSplitUnitDbId(Long.parseLong(splitUnitId.getValue()));
//            }
//        }
//    }
//
//    public void setIndexByData(CommercialParam commercialParam, Signal signal, DataPoint dataPoint) {
//        DataFinder indexCode = signal.indexCode();
//        DataFinder indexDbId = signal.indexDbId();
//        DataFinder planarCode = signal.planarCode();
//
//        if (StringUtils.isBlank(dataPoint.getIndexName())) {
//            if (indexCode != null && indexCode.isFound() && StringUtils.isNotBlank(indexCode.getValue())) {
//                IndexGridDTO indexGrid = commercialParam.getIndexCodeGridMap().get(indexCode.getValue());
//                setIndexInfo(commercialParam, dataPoint, indexGrid);
//
//            } else if (indexDbId != null && indexDbId.isFound() && StringUtils.isNotBlank(indexDbId.getValue()) && WgStringUtil.isNum(indexDbId.getValue())) {
//                String indexStrId = commercialParam.getIndexLongAndStrIdMap().get(Long.parseLong(indexDbId.getValue()));
//                IndexGridDTO indexGrid = commercialParam.getIndexIdGridMap().get(indexStrId);
//                setIndexInfo(commercialParam, dataPoint, indexGrid);
//
//            } else if (planarCode != null && planarCode.isFound() && StringUtils.isNotBlank(planarCode.getValue())) {
//                // text
//                GraphInteractionDTO interaction = commercialParam.getPlanarCodeAndGraphInteractionMap().get(planarCode.getValue());
//                if (interaction != null) {
//                    dataPoint.setIndexName(interaction.getName());
//                    dataPoint.setInteractionId(interaction.getId());
//                    dataPoint.setPlanarCode(planarCode.getValue());
//                }
//            }
//        }
//    }
//
//    public void setIndexInfo(CommercialParam commercialParam, DataPoint dataPoint, IndexGridDTO indexGrid) {
//        if (indexGrid == null)
//            return;
//        dataPoint.setIndexName(indexGrid.getIndexGridName(commercialParam.getCustomer().get_id()));
//        dataPoint.setHigherIsBetter(StringUtils.equalsIgnoreCase(indexGrid.getHigherIsBetter(), "Y"));
//        dataPoint.setPlanarCode(indexGrid.getPlanarIndexCode());
//        dataPoint.setIndexLevel(indexGrid.getIndexLevel());
//        dataPoint.setIndexCode(indexGrid.getLegacyIndexCode());
//    }
//
//    public void setAttributeName(boolean useAttributeCode,
//                                 Map<String, String> attributeCodeAndNameMap,
//                                 String attribute,
//                                 DataPoint dataPoint) {
//        if (!useAttributeCode)
//            return;
//
//        String[] attrCodes = attribute.split("-");
//        String attrName = Arrays.stream(attrCodes)
//                .map(attributeCodeAndNameMap::get)
//                .collect(Collectors.joining("-"));
//        dataPoint.setAttributeName(attrName);
//    }
}
