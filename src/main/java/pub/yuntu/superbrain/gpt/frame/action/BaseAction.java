package pub.yuntu.superbrain.gpt.frame.action;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.gpt.domain.signal.BaseSignal;
import pub.yuntu.superbrain.gpt.frame.action.business.*;
import pub.yuntu.superbrain.gpt.frame.action.convert.ConvertToNgqlAction;
import pub.yuntu.superbrain.gpt.frame.action.data.KeepDataAction;
import pub.yuntu.superbrain.gpt.frame.action.dict.DictDefinition;
import pub.yuntu.superbrain.gpt.frame.action.mysql.ExecuteSqlAction;
import pub.yuntu.superbrain.gpt.frame.action.nebula.*;
import pub.yuntu.superbrain.gpt.frame.action.ppt.PptAction;
import pub.yuntu.superbrain.gpt.frame.action.save.definition.ReadDefinition;
import pub.yuntu.superbrain.gpt.frame.action.save.definition.SaveDefinition;
import pub.yuntu.superbrain.gpt.frame.action.stat.openStat.OpenTextStatAction;
import pub.yuntu.superbrain.gpt.frame.action.stat.OpenTextSaveAction;
import pub.yuntu.superbrain.gpt.frame.action.stat.StatAction;
import pub.yuntu.superbrain.gpt.frame.action.visual.ReportAction;
import pub.yuntu.superbrain.gpt.frame.action.visual.SummaryAction;
import pub.yuntu.superbrain.gpt.frame.action.visual.VisualAction;
import pub.yuntu.superbrain.gpt.frame.context.GptContext;
import pub.yuntu.superbrain.gpt.frame.enhancer.GptEnhancer;
import pub.yuntu.superbrain.gpt.frame.variable.VariableParser;
import pub.yuntu.superbrain.gpt.graph.Vertex;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description 流计算框架动作
 * @createTime 2024年03月18日 17:28:48
 */
@Slf4j
@Data
@NoArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "action", defaultImpl = DefaultAction.class, visible = true)
@JsonSubTypes({
        // Mysql 相关动作
        @JsonSubTypes.Type(value = ExecuteSqlAction.class, name = "executeSql"),
        // Nebula 相关动作
        @JsonSubTypes.Type(value = CreateTagAction.class, name = "createTag"),
        @JsonSubTypes.Type(value = DropTagAction.class, name = "dropTag"),
        @JsonSubTypes.Type(value = InsertVertexAction.class, name = "insertVertex"),
        @JsonSubTypes.Type(value = CreateIndexAction.class, name = "createIndex"),
        @JsonSubTypes.Type(value = RebuildIndexAction.class, name = "rebuildIndex"),
        @JsonSubTypes.Type(value = ExecuteNgqlAction.class, name = "executeNgql"),
        // Convert 转换相关动作
        @JsonSubTypes.Type(value = ConvertToNgqlAction.class, name = "convertToNgql"),
        // 数据操作相关动作
        @JsonSubTypes.Type(value = KeepDataAction.class, name = "keepData"),
        // 原始数据统计动作
        @JsonSubTypes.Type(value = StatAction.class, name = "stat"),
        // 开放题专属保存中间表动作
        @JsonSubTypes.Type(value = OpenTextSaveAction.class, name = "openTextSave"),
        // 业务相关动作
        @JsonSubTypes.Type(value = DiffAction.class, name = "diff"),
        @JsonSubTypes.Type(value = SortAction.class, name = "sort"),
        @JsonSubTypes.Type(value = ActualDeliveryAction.class, name = "actualDelivery"),
        @JsonSubTypes.Type(value = PlanDeliveryAction.class, name = "planDelivery"),
        @JsonSubTypes.Type(value = VisualAction.class, name = "visual"),
        @JsonSubTypes.Type(value = SummaryAction.class, name = "summary"),
        @JsonSubTypes.Type(value = ReportAction.class, name = "report"),
        @JsonSubTypes.Type(value = CountAction.class, name = "count"),
        @JsonSubTypes.Type(value = SumAction.class, name = "sum"),
        @JsonSubTypes.Type(value = AvgAction.class, name = "avg"),
        @JsonSubTypes.Type(value = ArithmeticAction.class, name = "arithmetic"),
        @JsonSubTypes.Type(value = AppendAction.class, name = "append"),
        @JsonSubTypes.Type(value = EffectAction.class, name = "effect"),
        @JsonSubTypes.Type(value = MaxAction.class, name = "max"),
        @JsonSubTypes.Type(value = MinAction.class, name = "min"),
        @JsonSubTypes.Type(value = PeriodWaveAction.class, name = "periodWave"),
        @JsonSubTypes.Type(value = DynamicSqlAction.class, name = "dynamicSql"),
        @JsonSubTypes.Type(value = FeatureAction.class, name = "feature"),
        @JsonSubTypes.Type(value = ConclusionAction.class, name = "conclusion"),
        @JsonSubTypes.Type(value = CalWeightedAction.class, name = "calWeighted"),
        @JsonSubTypes.Type(value = ExtendAction.class, name = "extend"),
        @JsonSubTypes.Type(value = OpenTextStatAction.class, name = "openTextStat"),
        @JsonSubTypes.Type(value = CalKnowLedgePointAction.class, name = "calKnowLedgePoint"),
        @JsonSubTypes.Type(value = KnowLedgePointAction.class, name = "knowLedgePoint"),
        // 生成 PPT 文件的动作
        @JsonSubTypes.Type(value = PptAction.class, name = "ppt"),
})
public abstract class BaseAction {

    // 动作名称，用了执行具体的动作子类
    String action;
    // 动作名称，整个流计算框架中唯一
    String name;
    // 动作描述
    String description;
    // 绑定子图的节点名称，支持表达式配置
    List<String> bindingWith;
    // 是否跳过动作不执行，无配置默认不跳过
    boolean skip = false;
    // 接收信号的类型，未指定类型的话，处理所有信号
    String signalType;
    // 动作处理数据保存定义
    SaveDefinition saveTo;
    // 读取 saveTo 保存的数据
    ReadDefinition readFrom;
    // 字典配置
    DictDefinition dict;
    // 增强器
    List<GptEnhancer> signalEnhance;
    boolean deliverSignal = false; // 接收信号的动作配置是否传递信号

    static final Pattern DATE_PATTERN = Pattern.compile("(\\d{4})[-/.年]?(\\d{1,2})[-/.月]?(\\d{1,2})[日]?");

    transient final Set<String> bindingWithVertexes = new CopyOnWriteArraySet<>(); // 绑定的节点

    /**
     * 执行动作，用来记录动作执行时间
     * 不能使用 Aspect 切面实现，action 不是通过 Spring 容器管理的类
     *
     * @param context 流计算框架上下文
     */
    public final void execute(GptContext context) {
        // BusinessAction需要组装分期attribute循环，bindingWith不在这里解析
        if (null == getBindingWith() || this instanceof BusinessAction) {
            this.execute(context, null, null);
            return;
        }

        // 绑定节点
        VariableParser variableParser = context.getComponents().getVariableParser();
        String bindingWithStr = StringUtils.join(getBindingWith(), ",");
        List<Vertex> parsedVertices = (List<Vertex>) variableParser.parse(context, bindingWithStr);
        parsedVertices.forEach(vertex -> this.execute(context, vertex, null));
    }

    public final void execute(GptContext context, Vertex vertex, BaseSignal signal) {
        ActionLogHelper actionLogHelper = context.getActionLogHelper();
        actionLogHelper.log(context, this, "start");
        if (skip) {
            actionLogHelper.log(context, this, "skip");
            return;
        }

        // 过滤匹配的信号类型
        if (signal != null) {
            boolean match = actionAcceptSignal(signal);
            if (!match) {
                actionLogHelper.log(context, this, "noMatchSignal");
                return;
            }
        }

        actionLogHelper.log(context, this, "startProcess");

        // 子类实现的具体操作
        process(context, vertex, signal);
        // 如果需要传递信号
        if (!isDeliverSignal())
            return;
        // TODO: 2024/7/3 目前仅实现next返回一个vertex，由下往上
        // next的配置来源于信号的MatchVertexDefinition
        if (signal == null)
            return;
        Vertex next = vertex.next(signal);
        if (next == null)
            return;
        // 用找出来的新的点的锁
        next.getLock().lock();
        try {
            // 传递信号，子类实现需覆盖
            BaseSignal newSignal = produceNewSignal(context, vertex, signal);
            // next再次执行
            execute(context, next, newSignal);
        } finally {
            next.getLock().unlock();
        }
    }

    public abstract void process(GptContext context, Vertex vertex, BaseSignal signal);

    /**
     * 过滤并返回与动作接收信号类型匹配的信号列表。
     * 如果输入的信号列表为空或不匹配任何信号，则返回空列表。
     *
     * @param signals 输入的信号列表，可能为null。
     * @return 返回过滤后的信号列表，如果不匹配任何信号，则返回空列表。
     */
    protected List<BaseSignal> typeMatchedSignals(List<BaseSignal> signals) {
        // 如果输入的信号列表为null，直接返回空列表
        if (signals == null) return Collections.emptyList();

        // 使用Stream API过滤出与动作接收信号类型匹配的信号，并收集到新的列表中
        // 直接在流的操作中返回结果，而不是使用中间变量
        return signals.stream()
                .filter(this::actionAcceptSignal)
                .collect(Collectors.toList());
    }

    /**
     * 判断传入的信号类型是否为动作所接受的信号类型。
     *
     * @param signal 待检查的信号对象，不应为null。
     * @return 如果信号类型匹配，则返回true；否则返回false。
     */
    protected boolean actionAcceptSignal(BaseSignal signal) {
        if (null == signal) return false;

        // 获取当前动作接受的信号类型
        String acceptType = this.getSignalType();
        // 如果接受的信号类型为空，表示接受所有信号，直接返回true
        if (StringUtils.isBlank(acceptType)) return true;

        // 获取信号的类型
        String signalType = signal.getDefinition().getType();
        if (StringUtils.isBlank(signalType)) return true;

        signalType = StringUtils.trim(signalType);
        // 判断信号类型是否包含在动作接受的信号类型中
        return acceptType.contains(signalType);
    }

    /**
     * 动作配置了deliverSignal，触发产生新信号。
     * 子类动作有需实现produceNewSignal
     */
    protected BaseSignal produceNewSignal(GptContext context, Vertex vertex, BaseSignal signal) {
        return signal;
    }

    public List<String[]> getDate(String str) {
        List<String[]> res = new ArrayList<>();
        if (StringUtils.isBlank(str))
            return res;

        Matcher m = DATE_PATTERN.matcher(str);
        while (m.find()) {
            String year = m.group(1);
            String month = String.format("%02d", Integer.parseInt(m.group(2)));
            String day = String.format("%02d", Integer.parseInt(m.group(3)));
            String[] array = new String[]{year, month, day};
            res.add(array);
        }
        return res;
    }

    public String[] getStartAndEndPeriod(String dataPeriod) {
        if (StringUtils.isBlank(dataPeriod))
            return null;

        if (dataPeriod.length() < 9) {
            return new String[]{dataPeriod, dataPeriod};
        }

        String[] parts = dataPeriod.split("-");
        String year = parts[0], simpleMonths = parts[1];
        String startMonth = simpleMonths.substring(0, 2), endMonth = simpleMonths.substring(2, 4);
        return new String[]{year + "-" + startMonth, year + "-" + endMonth};
    }

    public String getDescription() {
        return StringUtils.isBlank(description) ? "[没有配置动作描述]" : description;
    }

    public String getName() {
        return StringUtils.isBlank(name) ? "动作没有配置name" : name;
    }

    public boolean matchVertex(GptContext context, Vertex vertex) {
        return bindingWithVertexes.contains(vertex.getId());
    }

    /**
     * 动作配置了bindingWith，初始化解析一次
     */
    public void initBindingWith(GptContext context) {
        if (bindingWithVertexes.isEmpty()) {
            VariableParser variableParser = context.getComponents().getVariableParser();
            String bindingWithStr = StringUtils.join(this.getBindingWith(), ",");
            List<Vertex> parsedVertices = (List<Vertex>) variableParser.parse(context, bindingWithStr);

            for (Vertex parsedVertex : parsedVertices) {
                bindingWithVertexes.add(parsedVertex.getId());
            }
        }
    }

    public void destroy() {
        setAction(null);
        setName(null);
        setDescription(null);
        Optional.ofNullable(bindingWith).ifPresent(List::clear);
        setBindingWith(null);
        setSignalType(null);
        Optional.ofNullable(saveTo).ifPresent(SaveDefinition::destroy);
        setSaveTo(null);
        Optional.ofNullable(dict).ifPresent(DictDefinition::destroy);
        setDict(null);
        Optional.ofNullable(signalEnhance).ifPresent(signalEnhance -> signalEnhance.forEach(GptEnhancer::destroy));
        setSignalEnhance(null);
        Optional.ofNullable(bindingWithVertexes).ifPresent(Set::clear);
    }

}
