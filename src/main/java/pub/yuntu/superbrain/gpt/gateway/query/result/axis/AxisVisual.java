package pub.yuntu.superbrain.gpt.gateway.query.result.axis;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import pub.yuntu.superbrain.gpt.frame.stat.GptAxis;
import pub.yuntu.superbrain.gpt.graph.Vertex;

import java.util.List;

/**
 * 生成坐标轴可视化
 *
 * <AUTHOR>
 * @createTime 2024年09月12日 09:01:13
 */
@Slf4j
public class AxisVisual {

    public static void draw(String questionText, List<GptAxis> axisList, Vertex vertex, Float featureDistance) {
        AxisVisualResult axisVisualResult = new AxisVisualResult().setQuestionText(questionText).setQuestionAxes(axisList);
        generateAxisVisual(vertex, axisVisualResult, featureDistance);
        vertex.setAxisVisualResult(axisVisualResult);
    }

    /**
     * 生成坐标轴可视化
     * 按照组织结构节点匹配的特征对应的坐标集合生成可视化结果图
     * 图包括：
     * 1.组织结构节点的特征
     * 2.特征对应的坐标和刻度
     * 3.坐标刻度的距离
     *
     * @param vertex           组织结构节点
     * @param axisVisualResult 坐标轴可视化结果对象
     * @param distanceThreshold  特征距离阈值
     */
    private static void generateAxisVisual(Vertex vertex, AxisVisualResult axisVisualResult, Float distanceThreshold) {
        if (null == vertex.getMatchedFeatureList() || vertex.getMatchedFeatureList().isEmpty()) return;

        vertex.getMatchedFeatureList().forEach(feature -> {
            if (null == feature.getAxisList() || feature.getAxisList().isEmpty()) return;

            FeatureAxes featureAxes = new FeatureAxes().setFeatureName(feature.getFeatureName()).setFeatureDistance(feature.getFeatureDistance());
            feature.getAxisList().forEach(axis -> {
                if (null == axis || null == axis.getNormalization()) return;

                Float distance = axis.getNormalization().getValue();
                String sequence = axis.getSequence();
                String scaleName = axis.getScaleName();

                if (null == axisVisualResult.getFeatureAxesList()) {
                    axisVisualResult.setFeatureAxesList(Lists.newArrayList());
                }

                ScaleDistance scaleDistance = new ScaleDistance(distance, scaleName);
                scaleDistance.setNetAxisId(axis.getNetAxisId());
                scaleDistance.setSequence(sequence);
//                scaleDistance.setMatch(distance <= distanceThreshold);
                featureAxes.getAxes().add(scaleDistance);
            });
            axisVisualResult.getFeatureAxesList().add(featureAxes);
        });

    }
}
