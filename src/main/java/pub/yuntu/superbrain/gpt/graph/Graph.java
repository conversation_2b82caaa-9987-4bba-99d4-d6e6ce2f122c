package pub.yuntu.superbrain.gpt.graph;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.gpt.domain.signal.BaseSignal;
import pub.yuntu.superbrain.gpt.domain.signal.definition.MatchVertexDefinition;
import pub.yuntu.superbrain.gpt.frame.GptComponents;
import pub.yuntu.superbrain.gpt.frame.action.business.meta.GptSplit;
import pub.yuntu.superbrain.gpt.frame.businessDefinition.vertex.VertexDefinition;
import pub.yuntu.superbrain.gpt.frame.context.GptContext;
import pub.yuntu.superbrain.gpt.frame.param.GraphDefinition;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 图结构/子图/神经网络实例
 * @createTime 2024年03月25日 11:39:06
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@Builder
public class Graph {

    GraphDefinition definition;
    List<Vertex> vertices;
    List<Edge> edges;
    ConcurrentMap<String, Vertex> idAndVertexMap = new ConcurrentHashMap<>(); // key：vertex vid
    ConcurrentMap<String, GptSplit> splitMap = new ConcurrentHashMap<>(); // anaSplit转换为GptSplit，需要加上组织结构的前缀，区分神客和地产
    ConcurrentMap<String, Vertex> vertexNameMap = new ConcurrentHashMap<>(); // key：vertex vid
    ConcurrentMap<String, Vertex> splitIdMap = new ConcurrentHashMap<>(); // key：开发商切分单元数字 ID
    ConcurrentMap<String, Vertex> splitStrIdMap = new ConcurrentHashMap<>(); // key：开发商切分单元字符串ID
    ConcurrentMap<String, Vertex> orgCodeMap = new ConcurrentHashMap<>(); // key：组织结构的 orgCode
    ConcurrentMap<String, List<Vertex>> levelOfVertexMap = new ConcurrentHashMap<>(); // 按层级 grouping vertex
    ConcurrentMap<String, Map<String, Vertex>> levelCodeOfVertexMap = new ConcurrentHashMap<>(); // 按层级+orgCode grouping vertex
    ConcurrentMap<String, List<Edge>> srcAndEdgeMap = new ConcurrentHashMap<>();
    ConcurrentMap<String, Map<String, List<Vertex>>> srcAndRelationAndDstMap = new ConcurrentHashMap<>();
    int curBreakpointIndex;
    boolean isVirtual = false;
    // 交互网关使用：中心组织结构
    Vertex centerVertex;
    // 交互网关使用：中心组织结构向上向下扩展层级
    ConcurrentMap<Integer, List<Vertex>> tierOfVertexMap = new ConcurrentHashMap<>(); // 按层级 grouping vertex

    public void accept(GptContext context, List<BaseSignal> signals) {
        if (null == signals || signals.isEmpty()) return;

        // 定位信号进入的节点
        // 理论上同一批信号的 MatchVertexDefinition 相同
        MatchVertexDefinition matchVertex = signals.get(0).getDefinition().getMatchVertex();
        if (matchVertex == null) {
            log.error("信号没有配置MatchVertex，无法在子图中定位节点");
            return;
        }

        // 获取vertexDefinition
        VertexDefinition vertexDefinition = matchVertex.getVertexDefinition();
        if (null == vertexDefinition) {
            log.error("信号配置MatchVertex中没有指定 vertexDefinition，无法在子图中定位节点");
            return;
        }

        // 信号分发
        CountDownLatch countDownLatch = new CountDownLatch(signals.size());
        for (BaseSignal signal : signals) {
            SignalDispatcher signalDispatcher = new SignalDispatcher();
            signalDispatcher.setCountDownLatch(countDownLatch);
            signalDispatcher.setSignal(signal);
            signalDispatcher.setContext(context);
            signalDispatcher.setVertexDefinition(vertexDefinition);
            GptComponents.actionPool.execute(signalDispatcher);
        }

        while (true) {
            if (countDownLatch.getCount() == 0) {
                log.debug("signalDispatcher多线程执行完成");
                break;
            }
        }
    }

    public List<Vertex> findNodesByLevel(@NotBlank String level) {
        return this.getLevelOfVertexMap().get(level);
    }

    public Vertex findNode(@NotBlank String bindingWith) {
        if (null == vertices || vertices.isEmpty()) return null;

        final String vid = StringUtils.isBlank(bindingWith) ? "root" : bindingWith;
        return findNodeByVid(vid);
    }

    public Vertex findNodeByVid(@NotBlank String vid) {
        if (null == vertices || vertices.isEmpty()) return null;

        return idAndVertexMap.get(vid);
    }

    public List<Vertex> findNodeByName(@NotBlank String vertexName) {
        if (null == vertices || vertices.isEmpty()) return null;

        return vertices.stream().filter(vertex -> vertex.getName().equalsIgnoreCase(vertexName)).collect(Collectors.toList());
    }

    public void addNode(Vertex vertex) {
        if (null == vertices) {
            vertices = Lists.newArrayList();
        }
        vertices.add(vertex);
    }

    public void addRelationship(Edge edge) {
        if (null == edges) {
            edges = Lists.newArrayList();
        }
        edges.add(edge);
    }

    public void destroy() {
        Optional.ofNullable(definition).ifPresent(GraphDefinition::destroy);
        setDefinition(null);

        Optional.ofNullable(vertices).ifPresent(list -> {
            list.forEach(Vertex::destroy);
            list.clear();
        });
        setVertices(null);

        Optional.ofNullable(edges).ifPresent(list -> {
            list.forEach(Edge::destroy);
            list.clear();
        });
        setEdges(null);

        Optional.ofNullable(idAndVertexMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(Vertex::destroy));
            map.clear();
        });
        setIdAndVertexMap(null);

        Optional.ofNullable(splitMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(GptSplit::destroy));
            map.clear();
        });
        setSplitMap(null);

        Optional.ofNullable(vertexNameMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(Vertex::destroy));
            map.clear();
        });
        setVertexNameMap(null);

        Optional.ofNullable(splitIdMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(Vertex::destroy));
            map.clear();
        });
        setSplitIdMap(null);

        Optional.ofNullable(splitStrIdMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(Vertex::destroy));
            map.clear();
        });
        setSplitStrIdMap(null);

        Optional.ofNullable(orgCodeMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(Vertex::destroy));
            map.clear();
        });
        setOrgCodeMap(null);

        Optional.ofNullable(levelOfVertexMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(list -> {
                list.forEach(Vertex::destroy);
                list.clear();
            }));
            map.clear();
        });
        setLevelOfVertexMap(null);

        Optional.ofNullable(levelCodeOfVertexMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(_map -> {
                _map.forEach((_k, _v) -> _v.destroy());
                _map.clear();
            }));
            map.clear();
        });
        setLevelCodeOfVertexMap(null);

        Optional.ofNullable(srcAndEdgeMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(list -> {
                list.forEach(Edge::destroy);
                list.clear();
            }));
            map.clear();
        });
        setSrcAndEdgeMap(null);

        Optional.ofNullable(srcAndRelationAndDstMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(_map -> {
                _map.forEach((_k, _v) -> {
                    _v.forEach(Vertex::destroy);
                    _v.clear();
                });
                _map.clear();
            }));
            map.clear();
        });
        setSrcAndRelationAndDstMap(null);

        Optional.ofNullable(centerVertex).ifPresent(Vertex::destroy);
        setCenterVertex(null);

        Optional.ofNullable(tierOfVertexMap).ifPresent(map -> {
            map.forEach((k, v) -> Optional.ofNullable(v).ifPresent(list -> {
                list.forEach(Vertex::destroy);
                list.clear();
            }));
            map.clear();
        });
        setTierOfVertexMap(null);

    }

}
