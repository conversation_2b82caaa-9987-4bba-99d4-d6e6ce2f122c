package pub.yuntu.superbrain.gpt.domain.ppt.layout;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by Maven on 2020/6/3 09:19
 */
@AllArgsConstructor
@Getter
public enum MarkType {

    DEFINITION("definition"),
    LAYOUT("layout"),
    REGION("region");

    private final String value;

    public static MarkType getByValue(String value) {
        for (MarkType type : values()) {
            if (StringUtils.equalsIgnoreCase(type.getValue(), StringUtils.trim(value))) {
                return type;
            }
        }
        return null;
    }
}
