package pub.yuntu.superbrain.gpt.domain.ppt.component;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class StandardComponentTypeNames implements Cloneable {

    public String[] Names;
    public ComponentType componentType;

    @Override
    public StandardComponentTypeNames clone() {
        StandardComponentTypeNames clone = null;
        try {
            clone = (StandardComponentTypeNames) super.clone();
        } catch (CloneNotSupportedException e) {
            log.error("", e);
        }
        return clone;
    }
}
