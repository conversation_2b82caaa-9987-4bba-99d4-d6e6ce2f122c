package pub.yuntu.superbrain.gpt.domain.ppt.component.chart;

import com.aspose.slides.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.gpt.domain.ppt.component.AxesV6;
import pub.yuntu.superbrain.gpt.domain.ppt.component.SeriesListV6;
import pub.yuntu.superbrain.gpt.domain.ppt.component.series.SeriesV6;
import pub.yuntu.superbrain.gpt.domain.ppt.param.PptParam;
import pub.yuntu.superbrain.gpt.frame.action.visual.point.GptDataPointWithValueList;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptChartBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.point.AbstractGptDataPoint;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 气泡图+effectScatter（2个气泡序列，多个effectScatter序列）
 * @createTime 2022年04月18日 15:23:54
 */
@Slf4j
public class Bubble2WithEffectScattersChart extends ChartV6 {

    public Bubble2WithEffectScattersChart(PptParam param, ISlide slide, GptChartBlock chartBlock) {
        super(param, slide, chartBlock);
    }

    @Override
    public void draw() {
        List<Chart> charts = getCharts(this);
        if (charts == null || charts.isEmpty()) return;

        // 模板中只有一个chart
        Chart asposeChart = charts.get(0);
        chartDataTableClearToMaxLine(asposeChart);

        drawTitle(asposeChart); // 图表标题
        drawLegend(asposeChart, chartConfig.getSeriesList()); // 图例
        drawPlotArea(asposeChart); // 绘图区域尺寸
        drawAxis(asposeChart); // 轴范围

        // 从EChart转换后的序列
        SeriesListV6 seriesList = chartConfig.getSeriesList(); // EChart转换后的配置
        List<SeriesV6> scatterSeriesList = seriesList.getSeriesList().stream()
                .filter(seriesV6 -> StringUtils.equalsIgnoreCase(seriesV6.getType(), "scatter"))
                .collect(Collectors.toList());
        List<SeriesV6> effectScatterSeriesList = seriesList.getSeriesList().stream()
                .filter(seriesV6 -> StringUtils.equalsIgnoreCase(seriesV6.getType(), "effectScatter"))
                .collect(Collectors.toList());

        fillData(asposeChart, scatterSeriesList, effectScatterSeriesList);
    }

    private void fillData(Chart asposeChart, List<SeriesV6> scatterSeriesList, List<SeriesV6> effectScatterSeriesList) {
        IChartData asposeChartData = asposeChart.getChartData();
        IChartDataWorkbook fact = asposeChartData.getChartDataWorkbook();

        // 填充表头
        int titleRowNo = 0;

        // 倒序输出序列，顺序会覆盖点，导致看不见，示例：
        // http://localhost:10101/api/rh/ppt/downloadByPages?pptId=33A4E872-7F09-4E8C-999F-F136297103AA&shotId=original&customerId=09D9FAC3-3C40-41A7-ACA0-62DB0A986825&splitGroupId=undefined&splitUnitId=0579DDCB-5E99-429A-AFD3-6B7598B4CA70&pageNIdList=1847&user=zhaofeiyan
        scatterSeriesList = Lists.reverse(scatterSeriesList);
        effectScatterSeriesList = Lists.reverse(effectScatterSeriesList);

        int scatterSeriesSize = scatterSeriesList.size();
        Map<String, Integer> columnNoMapping = Maps.newHashMap();
        String nameSuffix = "_name";

        // Excel第一行，表头
        for (int i = 0; i < scatterSeriesSize; i++) {
            SeriesV6 scatterSeries = scatterSeriesList.get(i);
            int columnNo = 2 + i * 2; // 第一列x，第二列大小，从第3列开始，一个序列有Y值和名称两列所以乘2
            String scatterSeriesName = scatterSeries.getName();
            columnNoMapping.put(scatterSeriesName, columnNo);
            addChartStaticData(fact, titleRowNo, columnNo, scatterSeriesName);
            String sName = scatterSeriesName + nameSuffix;
            columnNoMapping.put(sName, columnNo + 1); // 名称列
            addChartStaticData(fact, titleRowNo, columnNo + 1, sName);
        }
        for (int i = 0; i < effectScatterSeriesList.size(); i++) {
            SeriesV6 effectScatterSeries = effectScatterSeriesList.get(i);
            int columnNo = 2 + scatterSeriesSize * 2 + i * 2; // 第一列x，第二列大小，一个序列有Y值和名称两列所以乘2
            String effectScatterSeriesName = effectScatterSeries.getName();
            if (StringUtils.isEmpty(effectScatterSeriesName)) {
                effectScatterSeriesName = "effectScatter_" + i;
                effectScatterSeries.setName(effectScatterSeriesName); // 设置Style时，按名称匹配
            }
            columnNoMapping.put(effectScatterSeriesName, columnNo);
            addChartStaticData(fact, titleRowNo, columnNo, effectScatterSeriesName);
            // 第x列列名使用effectScatter序列名称
            String eName = effectScatterSeriesName + nameSuffix;
            columnNoMapping.put(eName, columnNo + 1);
            addChartStaticData(fact, titleRowNo, columnNo + 1, eName);
        }

        // 填充气泡数据
        int rowNo = 1, xColumnNo = 0, sizeColumnNo = 1;
        double maxX = 0, minX = 100, maxY = 0, minY = 100;
        for (int scatterSeriesIndex = 0; scatterSeriesIndex < scatterSeriesList.size(); scatterSeriesIndex++) {
            SeriesV6 scatterSeries = scatterSeriesList.get(scatterSeriesIndex);
            String scatterSeriesName = scatterSeries.getName();
            String sName = scatterSeriesName + nameSuffix;

            List<? extends AbstractGptDataPoint> scatterDataList = scatterSeries.getDataList();
            if (null == scatterDataList) {
                log.error("气泡序列没有数据，序列名：{}", scatterSeries.getName());
                continue;
            }
            for (int i = 0; i < scatterDataList.size(); i++) {
                GptDataPointWithValueList dataPoint = (GptDataPointWithValueList) scatterDataList.get(i);

                Integer yColumnNo = columnNoMapping.get(scatterSeriesName);
                Integer nameColumnNo = columnNoMapping.get(sName);
                if (null == dataPoint) {
                    clearCell(fact, rowNo, xColumnNo);
                    clearCell(fact, rowNo, yColumnNo);
                    clearCell(fact, rowNo, nameColumnNo);
                    clearCell(fact, rowNo, sizeColumnNo);
                } else {
                    List<Double> valueList = (List<Double>) dataPoint.getValue();
                    Double x = valueList.get(0);
                    Double y = valueList.get(1);
                    // 有些取数没有取到点的大小，给默认值 http://120.26.8.63:10103/browse/D7-155
                    Double size = valueList.size() == 2 ? 15d : valueList.get(2);
                    String name = dataPoint.getName();

                    if (null == x) {
                        clearCell(fact, rowNo, xColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, xColumnNo, x);
                        maxX = x > maxX ? x : maxX;
                        minX = x < minX ? x : minX;
                    }

                    if (null == y) {
                        clearCell(fact, rowNo, yColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, yColumnNo, y);
                        maxY = y > maxY ? y : maxY;
                        minY = y < minY ? y : minY;
                    }

                    if (StringUtils.isEmpty(name)) {
                        clearCell(fact, rowNo, nameColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, nameColumnNo, name);
                    }

                    if (null == size) {
                        clearCell(fact, rowNo, sizeColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, sizeColumnNo, size);
                    }
                }

                rowNo++;
            }
        }

        // 填充effectScatter数据
        for (int i = 0; i < effectScatterSeriesList.size(); i++) {
            SeriesV6 effectScatterSeries = effectScatterSeriesList.get(i);
            String effectScatterSeriesName = effectScatterSeries.getName();
            if (StringUtils.isEmpty(effectScatterSeriesName)) {
                effectScatterSeriesName = "effectScatter_" + i;
            }

            IChartSeries asposeSeries = matchAsposeSeries(asposeChartData, effectScatterSeriesName);
            asposeSeries.getLabels().getDefaultDataLabelFormat().setShowLabelValueFromCell(true);

            List<? extends AbstractGptDataPoint> dataList = effectScatterSeries.getDataList();
            if (null == dataList) continue;
            for (int j = 0; j < dataList.size(); j++) {
                AbstractGptDataPoint AbstractGptDataPoint = dataList.get(j);
                if (!(AbstractGptDataPoint instanceof GptDataPointWithValueList)) {
                    log.error("错误的数据类型，数据点：{}", AbstractGptDataPoint.getName());
                    continue;
                }
                GptDataPointWithValueList dataPoint = (GptDataPointWithValueList) AbstractGptDataPoint;

                Integer yColumnNo = columnNoMapping.get(effectScatterSeriesName);
                Integer nameColumnNo = columnNoMapping.get(effectScatterSeriesName + nameSuffix);
                if (null == dataPoint) {
                    clearCell(fact, rowNo, xColumnNo);
                    clearCell(fact, rowNo, yColumnNo);
                    clearCell(fact, rowNo, sizeColumnNo);
                    clearCell(fact, rowNo, nameColumnNo);
                } else {
                    List<Double> valueList = (List<Double>) dataPoint.getValue();
                    Double x = valueList.get(0);
                    Double y = valueList.get(1);
                    Double size = valueList.get(2);
                    String name = dataPoint.getName();

                    IDataLabel label = null;
                    IChartDataCell nameCell = null;
                    if (null != asposeSeries.getLabels()) {
                        int index = rowNo - 1;
                        if (index < asposeSeries.getLabels().getCount()) {
                            label = asposeSeries.getLabels().get_Item(index);
                        }
                    }

                    if (StringUtils.isEmpty(name)) {
                        clearCell(fact, rowNo, nameColumnNo);
                    } else {
                        nameCell = addChartStaticData(fact, rowNo, nameColumnNo, name);
                        labelValueCell(label, nameCell);
                    }

                    if (null == x) {
                        clearCell(fact, rowNo, xColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, xColumnNo, x);
                        labelValueCell(label, nameCell);
                        maxX = x > maxX ? x : maxX;
                        minX = x < minX ? x : minX;
                    }

                    if (null == y) {
                        clearCell(fact, rowNo, yColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, yColumnNo, y);
                        labelValueCell(label, nameCell);
                        maxY = y > maxY ? y : maxY;
                        minY = y < minY ? y : minY;
                    }

                    if (null == size) {
                        clearCell(fact, rowNo, sizeColumnNo);
                    } else {
                        addChartStaticData(fact, rowNo, sizeColumnNo, size);
                        labelValueCell(label, nameCell);
                    }
                }

                rowNo++;
            }
        }

        removeUselessSeriesData(asposeChartData, rowNo - 1);

        maxX = Math.ceil(maxX);
        minX = Math.floor(minX);
        maxY = Math.ceil(maxY);
        minY = Math.floor(minY);

        // x轴范围
        IAxis horizontalAxis = asposeChart.getAxes().getHorizontalAxis();
        horizontalAxis.setAutomaticMinValue(false);
        if (horizontalAxis.getMinValue() < 0) {
            horizontalAxis.setMinValue(0);
        }

        AxesV6 axis = this.getChartConfig().getAxis();
        Object configMax = axis.getHorizontalAxis().getMax();
        if (null != configMax && configMax instanceof String) {
            String configMaxStr = configMax.toString();
            if (StringUtils.equals(configMaxStr, "dataMax")) {
                horizontalAxis.setAutomaticMaxValue(false);
                horizontalAxis.setMaxValue(maxX);
            }
        }
        Object configMin = axis.getHorizontalAxis().getMin();
        if (null != configMin && configMin instanceof String) {
            String configMinStr = configMin.toString();
            if (StringUtils.equals(configMinStr, "dataMin")) {
                horizontalAxis.setAutomaticMinValue(false);
                horizontalAxis.setMinValue(minX);
            }
        }

        // y轴范围
        IAxis verticalAxis = asposeChart.getAxes().getVerticalAxis();
        verticalAxis.setAutomaticMinValue(false);
        if (verticalAxis.getMinValue() < 0) {
            verticalAxis.setMinValue(0);
        }

        configMax = axis.getVerticalAxis().getMax();
        if (null != configMax && configMax instanceof String) {
            String configMaxStr = configMax.toString();
            if (StringUtils.equals(configMaxStr, "dataMax")) {
                verticalAxis.setAutomaticMaxValue(false);
                verticalAxis.setMaxValue(maxY);
            }
        }
        configMin = axis.getVerticalAxis().getMin();
        if (null != configMin && configMin instanceof String) {
            String configMinStr = configMin.toString();
            if (StringUtils.equals(configMinStr, "dataMin")) {
                verticalAxis.setAutomaticMinValue(false);
                verticalAxis.setMinValue(minY);
            }
        }


    }

    private void labelValueCell(IDataLabel label, IChartDataCell nameCell) {
        if (null != label) {
            label.setValueFromCell(nameCell);
        }
    }

    private IChartSeries matchAsposeSeries(IChartData asposeChartData, String sName) {
        IChartSeries matchAsposeSeries = null;
        for (int i = 0; i < asposeChartData.getSeries().size(); i++) {
            IChartSeries asposeSeries = asposeChartData.getSeries().get_Item(i);
            String name = asposeSeries.getName().toString();
            if (StringUtils.equals(name, sName)) {
                matchAsposeSeries = asposeSeries;
                break;
            }
        }
        return matchAsposeSeries;
    }

    @Override
    public void drawStyle() {
        // 从EChart转换后的序列
        SeriesListV6 seriesList = chartConfig.getSeriesList(); // EChart转换后的配置

        List<Chart> charts = getCharts(this);
        Chart asposeChart = charts.get(0);
        IChartData asposeChartData = asposeChart.getChartData();
        for (int i = 0; i < asposeChartData.getSeries().size(); i++) {
            IChartSeries asposeSeries = asposeChartData.getSeries().get_Item(i);
            String name = asposeSeries.getName().toString();
            log.debug("asposeSeries name={}, type={}", asposeSeries.getName(), asposeSeries.getType());

            seriesList.getSeriesList().stream()
                    .filter(seriesV6 -> StringUtils.equalsIgnoreCase(name, seriesV6.getName()))
                    .findFirst()
                    .ifPresent(seriesV6 -> {
                        log.info("start to draw style: {}", seriesV6.getName());
                        drawScatterStyle(asposeSeries, seriesV6);
                    });
        }
    }
}
