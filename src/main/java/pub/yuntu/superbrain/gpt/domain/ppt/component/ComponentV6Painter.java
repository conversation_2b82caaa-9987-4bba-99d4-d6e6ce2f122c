package pub.yuntu.superbrain.gpt.domain.ppt.component;

import cn.hutool.core.net.URLEncodeUtil;
import com.aspose.slides.*;
import com.ruiyun.jvppeteer.core.browser.Browser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.foundation.file.FileUtils;
import pub.yuntu.foundation.filestore.Qiniu;
import pub.yuntu.foundation.filestore.QiniuAccount;
import pub.yuntu.foundation.filestore.QiniuMs;
import pub.yuntu.foundation.image.ImageUtil;
import pub.yuntu.foundation.video.VideoInfo;
import pub.yuntu.superbrain.domain.component.SlidesEngine;
import pub.yuntu.superbrain.domain.model.creativity.config.PptConfig;
import pub.yuntu.superbrain.domain.model.creativity.media.MediaItem;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.CssStyle;
import pub.yuntu.superbrain.domain.model.legacy.knowledge.constants.KnowledgeConstants;
import pub.yuntu.superbrain.gpt.domain.ppt.component.chart.*;
import pub.yuntu.superbrain.gpt.domain.ppt.component.table.TableV6;
import pub.yuntu.superbrain.gpt.domain.ppt.constants.PptConstants;
import pub.yuntu.superbrain.gpt.domain.ppt.layout.LayoutV6;
import pub.yuntu.superbrain.gpt.domain.ppt.page.GptPptPage;
import pub.yuntu.superbrain.gpt.domain.ppt.param.PptParam;
import pub.yuntu.superbrain.gpt.domain.ppt.region.Position;
import pub.yuntu.superbrain.gpt.domain.ppt.region.Size;
import pub.yuntu.superbrain.gpt.domain.ppt.template.Template;
import pub.yuntu.superbrain.gpt.domain.ppt.util.PresentationUtil;
import pub.yuntu.superbrain.gpt.domain.ppt.web.GptPptRpcAgent;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptChartBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptMediaBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptTableBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.block.GptTextBlock;
import pub.yuntu.superbrain.gpt.frame.action.visual.layout.GptColumn;
import pub.yuntu.superbrain.gpt.frame.action.visual.layout.GptLayout;
import pub.yuntu.superbrain.util.VideoUtils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLConnection;

/**
 * <AUTHOR>
 * @Description 6.0PPT组件绘制器
 * @createTime 2022年04月18日 09:58:21
 */
@Slf4j
public class ComponentV6Painter {

    private static final QiniuAccount qiNiuAccount = new QiniuMs();
    // 默认每个超链接高度
    final float HYPERLINK_DEFAULT_HEIGHT = 20f;
    Browser browser;

    public ComponentV6Painter() {
        browser = GptPptRpcAgent.webUIMandaloLogin();
    }

//    public void drawColumn(ISlide slide, GptColumn column) {
//        Position position = column.getLayoutV6().getPosition();
//        Size size = column.getLayoutV6().getSize();
//        IAutoShape shape = slide.getShapes().addAutoShape(ShapeType.Rectangle, position.getX(), position.getY(), size.getWidth(), size.getHeight());
//        shape.getFillFormat().setFillType(FillType.NoFill);
//        log.info("Column Width:{}, Height:{}", size.getWidth(), size.getHeight());
//    }

    /**
     * 绘制页面标题
     */
    public void drawPageTitle(PptParam param, ISlide slide, GptLayout dynamicLayout,
                              GptPptPage page) {
        LayoutV6 layout = dynamicLayout.getLayoutV6();
        final float pageWidth = layout.getSize().getWidth();
//        String title = page.getName().replaceAll("\b", "");
//        String appendTitle = page.getNodeName().replaceAll("\b", "") + "_" + page.getVisualName().replaceAll("\b", "");
//        title = title + "   " + appendTitle;
        String title = page.getOfficialName().replaceAll("\b", "");
        if (StringUtils.containsIgnoreCase(title, "{splitName}")) {
            title = title.replace("{splitName}", param.getSplitUnitName());
        }

        final IAutoShape titleShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, 20, 10
                , pageWidth - 40, PptConstants.TITLE_HEIGHT - 25);
        PresentationUtil.handleAutoShapeFonts(titleShape, param.getPptFont()); // 设置字体
        titleShape.getFillFormat().setFillType(FillType.NoFill);
        ILineFormat lineFormat = titleShape.getLineFormat();
        lineFormat.getFillFormat().setFillType(FillType.NoFill);
        lineFormat.getFillFormat().getSolidFillColor().setColor(Color.WHITE);

        // Access the TextFrame associated with the AutoShape
        ITextFrame textFrame = titleShape.getTextFrame();
        textFrame.setText(title);

        // Access the Portion associated with the TextFrame
        IParagraph paragraph = textFrame.getParagraphs().get_Item(0);
        IPortion port = paragraph.getPortions().get_Item(0);
        // 粗体 Set Bold property of the Font
        port.getPortionFormat().setFontBold(NullableBool.True);
        Color fontColor = param.getCustomerConfig().getTitle().getFontColor();
        if (null != fontColor) {
            port.getPortionFormat().getFillFormat().getSolidFillColor().setColor(fontColor);
        }

        // 文本左对齐
        // Justify the paragraph
        paragraph.getParagraphFormat().setAlignment(TextAlignment.Left);
        port.getPortionFormat().getFillFormat().setFillType(FillType.Solid);

        /*
         * 标准模板，苹方简
         * 26号字，一行可以放置28字
         * 20号字，一行可以放置37
         * 16号字，一行可以放置46
         *
         * 宽屏模板，苹方简
         * 26号字，一行可以放置35字
         * 20号字，一行可以放置45字
         * 16号字，一行可以放置56字
         */
        final int wordCount = title.length();
        if (param.isWide()) {
            if (wordCount < 35) {
                port.getPortionFormat().setFontHeight(26);
            } else if (wordCount < 45) {
                port.getPortionFormat().setFontHeight(20);
            } else if (wordCount < 56) {
                port.getPortionFormat().setFontHeight(16);
            } else {
                port.getPortionFormat().setFontHeight(12);
            }
        } else {
            if (wordCount < 26) {
                port.getPortionFormat().setFontHeight(26);
            } else if (wordCount < 37) {
                port.getPortionFormat().setFontHeight(20);
            } else if (wordCount < 46) {
                port.getPortionFormat().setFontHeight(16);
            } else {
                port.getPortionFormat().setFontHeight(12);
            }
        }
    }

    /**
     * 绘制图表
     */
    public void drawChart(PptParam param, ISlide slide, GptColumn column,
                          Template template, GptPptPage page) {
        long start = System.currentTimeMillis();
        GptChartBlock chartBlock = column.getChart();
        if (null == chartBlock) return;

        // 设置布局，默认一个DynamicColumn中只有一个组件，即组件使用DynamicColumn的布局、完全覆盖
        chartBlock.setLayoutV6(column.getLayoutV6());

        PptConfig pptConfig = chartBlock.getPptConfig();
        if (null == pptConfig) return;

        // EChart截图
        if (pptConfig.isSnapshot()) {
            snapshotBlock(param, slide, column, page);
            log.info("[{}] Gpt-PPT 场景[{}]截图完成，用时：{}",
                    param.getUser(), page.getName(), DateUtil.calPassedTime(start));
            return;
        }

        // 设置标准组件
        StandardComponent component = ComponentV6Util.getStandardComponent(param, template, pptConfig);
        if (null == component) return;
        chartBlock.setPptComponent(component);

        // 绘制图表
        log.info("[{}] Gpt-PPT >>> 开始绘制Chart[{}]组件", param.getUser(), chartBlock.getPptComponent().getName());
        drawAsposeChart(param, slide, chartBlock, template);
        log.info("[{}] Gpt-PPT >>> Chart[{}]组件绘制完成，用时：{}",
                param.getUser(), chartBlock.getPptComponent().getName(), DateUtil.calPassedTime(start));
    }

    /**
     * 打开浏览器绘制可视化后截图，图片复制到PPT页面对应的位置
     */
    private void snapshotBlock(PptParam param, ISlide slide, GptColumn column, GptPptPage page) {
        long start = System.currentTimeMillis();
        log.info("GptChartBlock Prepare to Snapshot");
        BufferedImage bufferedImage = GptPptRpcAgent.webUIMandaloDomSnapshot(browser, param, page); // 6.0 PPT TODO

        // 计算前端Web页面中需要截取的column的位置
        LayoutV6 layoutV6ForSnapshot = column.getLayoutV6ForSnapshot();
        float x = layoutV6ForSnapshot.getPosition().getX();
        float y = layoutV6ForSnapshot.getPosition().getY();
        float width = layoutV6ForSnapshot.getSize().getWidth();
        float height = layoutV6ForSnapshot.getSize().getHeight();
        log.info("Gpt-PPT 前端页面截图位置：x={}, y={}, width={}, height={}", x, y, width, height);

        BufferedImage cutImage = bufferedImage.getSubimage((int) x, (int) y, (int) width, (int) height);
        BufferedImage cutImageCopy = new BufferedImage(cutImage.getWidth(), cutImage.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D cutImageCopyGraphics = cutImageCopy.createGraphics();
        cutImageCopyGraphics.drawImage(cutImage, 0, 0, Color.white, null);
        cutImageCopy.flush();
        cutImageCopy.getGraphics().dispose();
        IPPImage regionImage = slide.getPresentation().getImages().addImage(cutImageCopy);
        cutImage.getGraphics().dispose();
        LayoutV6 layoutForPpt = column.getLayoutV6();
        slide.getShapes().addPictureFrame(ShapeType.Rectangle,
                layoutForPpt.getPosition().getX(), layoutForPpt.getPosition().getY(),
                layoutForPpt.getSize().getWidth(), layoutForPpt.getSize().getHeight(), regionImage);

        log.info("[{}] Gpt-PPT 浏览器截图完成，用时：{}",
                param.getUser(), DateUtil.calPassedTime(start));
    }

    private void drawAsposeChart(PptParam param, ISlide slide, GptChartBlock chartBlock, Template template) {
        ChartV6 chart = null;
        StandardComponent horizontalAxisLabelTable = template.getComponent("横轴标签");
        String componentName = chartBlock.getPptComponent().getName();

        if (StringUtils.isNotBlank(chartBlock.getPptConfig().getPptAndEChartSeriesIndex())) {
            // chart = new DefaultChart(param, slide, chartBlock); // 6.0 PPT TODO 不使用默认Chart
        } else {
            switch (componentName) {
                // 多个序列要同时绘制，带有分类Category
                case "单柱":
                case "单柱-正负值":
                case "单柱-正负值-无横轴标签":
                case "单柱+图例":
                case "横向条形图":
                case "横向条形图-百分比":
                    chart = new SingleBarChart(param, slide, chartBlock);
                    break;
                case "双柱":
                case "3柱":
                case "4柱":
                case "9柱":
                case "横向条形图-双柱":
                    chart = new MultipleBarsChart(param, slide, chartBlock);
                    break;
                case "堆叠柱图":
                case "堆叠柱图-百分比":
                case "横向堆积图":
                case "横向堆积图-百分比":
                case "13堆叠柱图":
                    chart = new StackBarChart(param, slide, chartBlock);
                    break;
                case "堆叠柱图-2柱":
                    chart = new Stack2BarChart(param, slide, chartBlock);
                    break;
                case "折线":
                case "折线-无横轴标签":
                case "有图例折线":
                case "折线-横轴无刻度标签":
                    chart = new SingleLineChart(param, slide, chartBlock);
                    break;
                case "双折线":
                case "双折线-横轴无刻度标签":
                case "双折线-横轴无刻度标签无图例":
                case "3折线":
                case "3折线-无横轴标签":
                case "5折线":
                case "5折线_无数据标签":
                    chart = new MultipleLinesChart(param, slide, chartBlock);
                    break;
                case "柱+折线":
                case "柱+折线-无图例":
                case "柱+折线-负数":
                case "柱+折线+纵轴刻度":
                case "柱+折线+双纵轴":
                case "柱+折线+双纵轴+无纵轴标题":
                case "柱+折线-2":
                case "柱+折线-横轴无刻度标签":
                    chart = new BarWithLineChart(param, slide, chartBlock);
                    break;
                case "柱+2折线":
                case "柱+3折线":
                case "单柱正负值+3折线":
                    chart = new BarWithLinesChart(param, slide, chartBlock);
                    break;
                case "2柱+折线":
                case "2柱+折线+双纵轴":
                case "2柱+折线-负数":
                case "2柱+2折线":
                case "2柱+2折线+双纵轴":
                case "2柱+3折线":
                case "3柱+折线":
                case "3柱+折线-无标签":
                case "3柱+2折线":
                case "4柱+折线":
                case "4柱+折线+双纵轴":
                    chart = new BarsWithLinesChart(param, slide, chartBlock);
                    break;
                case "折线+MarkLine":
                    chart = new LineWithMarkLinesChart(param, slide, chartBlock);
                    break;
                case "柱+MarkLine":
                case "柱+2MarkLine":
                case "柱+2MarkLine-横轴无刻度标签":
                case "2柱+2MarkLine":
                case "柱+3MarkLine":
                    chart = new BarsWithMarkLinesChart(param, slide, chartBlock);
                    break;
                case "散点_单序列":
                    chart = new ScatterChart(param, slide, chartBlock);
                    break;
                case "散点_2序列":
                    chart = new Scatters2Chart(param, slide, chartBlock);
                    break;
                case "散点2+effectScatters":
                case "散点5+effectScatters":
                    chart = new Scatters5WithEffectScatterChart(param, slide, chartBlock);
                    break;
                case "散点2+effectScatters+1比1辅助线":
                case "散点2+effectScatters+1比1辅助线+无图例":
                case "散点2带边框+effectScatters+1比1辅助线+无图例":
                    chart = new Scatters2WithEffectScattersWithOneToOneAuxiliaryLineChart(param, slide, chartBlock);
                    break;
                case "散点+趋势线":
                    chart = new ScatterWithTrendLineChart(param, slide, chartBlock);
                    break;
                case "气泡":
                    chart = new BubbleChart(param, slide, chartBlock);
                    break;
                case "气泡4序列":
                    chart = new BubblesChart(param, slide, chartBlock);
                    break;
                case "气泡+effectScatter":
                    chart = new BubbleWithEffectScatterChart(param, slide, chartBlock);
                    break;
                case "气泡(带label)+effectScatter":
                    chart = new BubbleWithLabelWithEffectScatterChart(param, slide, chartBlock);
                    break;
                case "气泡2+effectScatters":
                    chart = new Bubble2WithEffectScattersChart(param, slide, chartBlock);
                    break;
                case "气泡2+effectScatters+1比1辅助线":
                case "气泡2+2effectScatters+1比1辅助线":
                case "气泡2+3effectScatters+1比1辅助线":
                    chart = new Bubble2WithEffectScattersWithOneToOneAuxiliaryLineChart(param, slide, chartBlock);
                    break;
                case "气泡+1比1辅助线":
                    chart = new BubbleWithOneToOneAuxiliaryLineChart(param, slide, chartBlock);
                    break;
                case "气泡2序列+1比1辅助线":
                    chart = new Bubble2WithOneToOneAuxiliaryLineChart(param, slide, chartBlock);
                    break;
                case "簇泡+折线":
                    chart = new ScatterWithLineChart(param, slide, chartBlock);
                    chart.setHorizontalAxisLabelTable(horizontalAxisLabelTable);
                    break;
                case "簇泡+3折线":
                    chart = new ScatterWithLinesChart(param, slide, chartBlock);
                    chart.setHorizontalAxisLabelTable(horizontalAxisLabelTable);
                    break;
                case "2簇泡": // 不是散点图，有气泡大小
                    chart = new Bubble2Chart(param, slide, chartBlock);
                    break;
                case "2簇泡+3折线":
                    chart = new ScattersWithLinesChart(param, slide, chartBlock);
                    chart.setHorizontalAxisLabelTable(horizontalAxisLabelTable);
                    break;
                case "饼图":
                case "饼图-百分比":
                    chart = new PieChart(param, slide, chartBlock);
                    break;
                case "忠诚度":
                    chart = new LoyalChart(param, slide, chartBlock);
                    break;
                case "忠诚度+2折线":
                    chart = new LoyalWithLinesChart(param, slide, chartBlock);
                    break;
                default:
                    log.error("暂不支持的组件类型：{}", componentName);
                    break;
            }
        }

        if (null == chart) return;

        chart.draw();
        chart.drawStyle(); // 匹配EChart序列和aspose序列，应用EChart Option配置
        chart.clear();
    }

    /**
     * 绘制表格
     */
    public void drawTable(PptParam param, ISlide slide, GptColumn column,
                          Template template, GptPptPage page) {
        long start = System.currentTimeMillis();
        GptTableBlock tableBlock = column.getTable();
        if (null == tableBlock) return;

        PptConfig pptConfig = tableBlock.getPptConfig();

        tableBlock.setLayoutV6(column.getLayoutV6());
        TableV6 table = new TableV6(param, slide, tableBlock);

        if (null != pptConfig && pptConfig.isSnapshot()) {
            snapshotBlock(param, slide, column, page);
            log.info("[{}] Gpt-PPT 场景[{}]截图完成，用时：{}",
                    param.getUser(), page.getName(), DateUtil.calPassedTime(start));
            return;
        }

        if (null != pptConfig && pptConfig.isExportExcel()) {
            // 绘制简化表格，不溢出布局
            ITable asposeTable = table.drawSimpleTable();
            // 生成Excel文件
            if (null != asposeTable) {
                table.exportExcel(param, asposeTable);
            }
        } else {
            // 绘制完整表格
            table.draw();
        }
        table.clear();
        log.info("[{}] Gpt-PPT >>> Table组件绘制完成，用时：{}", param.getUser(), DateUtil.calPassedTime(start));
    }

    /**
     * 绘制分页表格
     */
    public void drawPagedTable(PptParam param, ISlide slide, GptTableBlock pageTableBlock) {
        TableV6 table = new TableV6(param, slide, pageTableBlock);
        table.draw();
        table.clear();
    }

//    /**
//     * 绘制结论
//     */
//    public void drawThesis(PptParam param, ISlide slide, GptColumn column,
//                           Template template, GptPptPage page) {
//        ThesisBlock thesis = column.getThesis();
//        if (null == thesis) return;
//
//        String content = thesis.getContent();
//        if (StringUtils.isBlank(content)) return;
//
//        Position position = column.getLayoutV6().getPosition();
//        Size size = column.getLayoutV6().getSize();
//        IAutoShape textShape = textShape(slide, position, size);
//
//        drawText(textShape, content, null);
//    }

//    /**
//     * 绘制结论
//     */
//    public void drawAssert(PptParam param, ISlide slide, GptColumn column, Template template, GptPptPage page) {
//        List<RhAssert> asserts = column.getAsserts();
//        if (null == asserts || asserts.size() == 0) return;
//
//        Position position = column.getLayoutV6().getPosition();
//        Size size = column.getLayoutV6().getSize();
//        IAutoShape textShape = textShape(slide, position, size);
//
//        // 处理结论文本
//        String content = asserts.stream()
//                .map(RhAssert::getRichText)
//                .collect(Collectors.joining("\n"));
//        // TODO: 处理结论附件
//
//        drawText(textShape, content, null);
//    }

    /**
     * 绘制文本
     */
    public void drawText(ISlide slide, GptColumn column) {
        GptTextBlock textBlock = column.getText();
        if (null == textBlock) return;

        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        IAutoShape textShape = textShape(slide, position, size);

        CssStyle cssStyle = CssStyle.parse(textBlock.getStyle());

        String text = textBlock.getContent();

        if (StringUtils.isNotBlank(text)) {
            text = text.replaceAll("\n", "").trim();
        }
        if (StringUtils.isNotBlank(text) && text.contains("|||")) {
            text = text.replaceAll("\\|\\|\\|", "\n");
        }
        // 绘制文本内容
        drawText(textShape, text, cssStyle);
    }

    private IAutoShape textShape(ISlide slide, Position position, Size size) {
        IAutoShape textShape = slide.getShapes().addAutoShape(ShapeType.Rectangle
                , position.getX(), position.getY(), size.getWidth(), size.getHeight());
        textShape.getFillFormat().setFillType(FillType.NoFill);
        ILineFormat lineFormat = textShape.getLineFormat();
        lineFormat.getFillFormat().setFillType(FillType.NoFill);
        lineFormat.getFillFormat().getSolidFillColor().setColor(Color.WHITE);
        return textShape;
    }

    private void drawText(IAutoShape textShape, String text, CssStyle cssStyle) {
        ITextFrame textFrame = textShape.getTextFrame();
        text = StringUtils.isBlank(text) ? "" : text;
        textFrame.setText(text);

        IParagraph paragraph = textFrame.getParagraphs().get_Item(0);
        IPortion port = paragraph.getPortions().get_Item(0);
        IPortionFormat portionFormat = port.getPortionFormat();
        portionFormat.getFillFormat().setFillType(FillType.Solid);

        if (null == cssStyle) {
            // 默认样式
            portionFormat.setFontHeight(10f);
            portionFormat.getFillFormat().getSolidFillColor().setColor(Color.BLACK); // 没有设置颜色，默认黑色
            paragraph.getParagraphFormat().setAlignment(TextAlignment.Left);
            return;
        }

        // 字号
        if (null != cssStyle.getFontSize()) {
            portionFormat.setFontHeight(cssStyle.getFontSize());
        } else {
            portionFormat.setFontHeight(10f);
        }
        // 粗体
        String fontWeight = cssStyle.getFontWeight();
        if (StringUtils.isNotBlank(fontWeight) && StringUtils.equalsIgnoreCase(fontWeight, "bold")) {
            portionFormat.setFontBold(NullableBool.True);
        }
        // 颜色
        if (null == cssStyle.getFontColor() && null == cssStyle.getColor()) {
            portionFormat.getFillFormat().getSolidFillColor().setColor(Color.BLACK); // 没有设置颜色，默认黑色
        } else {
            // 优先使用font-color
            if (null != cssStyle.getFontColor()) {
                portionFormat.getFillFormat().getSolidFillColor().setColor(cssStyle.getFontColor());
            } else {
                portionFormat.getFillFormat().getSolidFillColor().setColor(cssStyle.getColor());
            }
        }
        // 文本对齐
        if (StringUtils.equalsIgnoreCase(cssStyle.getTextAlign(), "center")) {
            paragraph.getParagraphFormat().setAlignment(TextAlignment.Center);
        } else if (StringUtils.equalsIgnoreCase(cssStyle.getTextAlign(), "right")) {
            paragraph.getParagraphFormat().setAlignment(TextAlignment.Right);
        } else {
            paragraph.getParagraphFormat().setAlignment(TextAlignment.Left);
        }
    }

    /**
     * 绘制多媒体
     */
    public void drawMedia(Presentation ppt, PptParam param, ISlide slide, GptColumn column,
                          Template template, GptPptPage page) {
        GptMediaBlock media = column.getMedia();
        if (null == media) return;

        MediaItem[] mediaItems = media.getMediaItems();
        if (mediaItems == null || mediaItems.length == 0) return;

        if (StringUtils.isBlank(page.getConfigurationExpression())) {
            switch (media.getMediaType()) {
                case "video":
                    drawVideos(slide, column, mediaItems, param);
                    break;
                case "audio":
                    drawAudios(slide, column, mediaItems);
                    break;
                case "image":
                    drawImages(slide, column, mediaItems);
                    break;
            }
        } else if (StringUtils.equalsIgnoreCase(page.getConfigurationExpression(), "onePageOneEvidence")) {
            // 有多个证据，一个页面一个证据
            switch (media.getMediaType()) {
                case "video":
                    drawVideosOnePageOneEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
                case "audio":
                    drawAudiosOnePageOneEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
                case "image":
                    drawImagesOnePageOneEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
            }
        } else if (StringUtils.equalsIgnoreCase(page.getConfigurationExpression(), "onePageThreeEvidence")) {
            // 有多个证据，一个页面三个证据
            switch (media.getMediaType()) {
                case "video":
                    drawVideosOnePageThreeEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
                case "audio":
                    drawAudiosOnePageThreeEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
                case "image":
                    drawImagesOnePageThreeEvidence(ppt, slide, column, mediaItems, param, page);
                    break;
            }
        } else {
            throw new RuntimeException("PPT Page ConfigurationExpression Error，暂不支持的表达式类型");
        }
    }

    private void drawVideos(ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();

        // 第一个视频使用视频控件展示，其他视频显示链接，通过链接的数量反推视频的尺寸，计算高度
        int hyperlinkAmount = mediaItems.length - 1;
        if (hyperlinkAmount > 3) hyperlinkAmount = 3;
        float videoHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT * (hyperlinkAmount + 1); // +1因为第一个视频有自己的标题

        // 绘制第一个视频
        MediaItem firstVideo = mediaItems[0];
        drawVideo(slide, position, size, videoHeight, firstVideo, param);

        if (mediaItems.length == 1) return;

        for (int i = 1; i < mediaItems.length; i++) {
            MediaItem hyperlink = mediaItems[i];

            /*
            http://120.26.8.63:10103/browse/D7-130
            附件太多，导致无位置放置第一张图片
            修改方案：如果附件数量大于3个，只展示前三个，其余的不绘制
             */
            if (i > 3) continue;

            drawHyperlink(slide, position.getX(),
                    position.getY() + videoHeight + (HYPERLINK_DEFAULT_HEIGHT * i),
                    size.getWidth(), HYPERLINK_DEFAULT_HEIGHT, hyperlink);
        }
    }

    private void drawVideosOnePageOneEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float videoHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        for (int i = 0; i < mediaItems.length; i++) {
            // 第一个视频绘制在当前页面，其余视频创建新页面
            ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
            // 绘制页面标题
            drawPageTitle(param, newSlide, dynamicLayout, page);
            MediaItem video = mediaItems[i];
            drawVideo(newSlide, position, size, videoHeight, video, param);
        }
    }

    private void drawVideosOnePageThreeEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float videoHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        // 多媒体数量
        int mediaItemsAmount = mediaItems.length;
        int pageSize = 1;
        int mediaIndex = 0, gapWidth = 20;

//        // 只有一个多媒体，居中布局
//        if (mediaItemsAmount == 1) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            float columnWidth = size.getWidth();
//            // 计算每列的位置和大小
//            Size columnSize = new Size(columnWidth, videoHeight);
//            MediaItem video = mediaItems[mediaIndex];
//            drawVideo(slide, position, columnSize, videoHeight, video, param);
//        }
//
//        // 只有两个多媒体，左右布局
//        else if (mediaItemsAmount == 2) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            for (int columnIndex = 0; true; columnIndex++) {
//                float columnWidth = size.getWidth() / 2 - gapWidth;
//                // 计算每列的位置和大小
//                Size columnSize = new Size(columnWidth, videoHeight);
//                Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
//                if (columnIndex > 0) {
//                    newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
//                }
//
//                MediaItem video = mediaItems[mediaIndex];
//                drawVideo(slide, newPosition, columnSize, videoHeight, video, param);
//
//                if (mediaIndex == mediaItemsAmount - 1) break;
//                mediaIndex++;
//            }
//        }
//
//        else {
            // 按一页放三个视频，一个视频一列计算页面数量
            pageSize = mediaItems.length % 3 == 0 ? mediaItems.length / 3 : mediaItems.length / 3 + 1;
            for (int i = 0; i < pageSize; i++) {
                // 第一个视频绘制在当前页面，其余视频创建新页面
                ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
                // 绘制页面标题
                drawPageTitle(param, newSlide, dynamicLayout, page);
                for (int columnIndex = 0; columnIndex < 3; columnIndex++) {
                    float columnWidth = size.getWidth() / 3 - gapWidth * 2;
                    // 计算每列的位置和大小
                    Size columnSize = new Size(columnWidth, videoHeight);
                    Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
                    if (columnIndex > 0) {
                        newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
                    }

                    MediaItem video = mediaItems[mediaIndex];
                    drawVideo(newSlide, newPosition, columnSize, videoHeight, video, param);

                    if (mediaIndex == mediaItems.length - 1) break;
                    mediaIndex++;
                }
            }
//        }
    }

    private void drawVideo(ISlide slide, Position position, Size size, float videoHeight, MediaItem video, PptParam param) {
//        drawOnlineVideo(slide, position, size, videoHeight, video);
        drawVideoFromLocalFile(slide, position, size, videoHeight, video, param);

        // 绘制第一个视频的标题
        Position titlePosition = Position.builder().x(position.getX()).y(position.getY() + videoHeight).build();
        Size titleSize = Size.builder().width(size.getWidth()).height(HYPERLINK_DEFAULT_HEIGHT).build();
        drawHyperlink(slide, titlePosition.getX(), titlePosition.getY(), titleSize.getWidth(), HYPERLINK_DEFAULT_HEIGHT, video);
//        IAutoShape firstVideoTitleShape = textShape(slide, titlePosition, titleSize);
//        drawText(firstVideoTitleShape, video.getName(), null);
    }

    /**
     * 插入Video，使用在线的资源，Video控件使用URL访问资源
     * 使用此方法插入视频，PPT打开报错，并且不能在PPT中播放视频，只能在网页中打开
     */
    private void drawOnlineVideo(ISlide slide, Position position, Size size, float videoHeight, MediaItem video) {
        //add videoFrame
        IVideoFrame videoFrame = slide.getShapes().addVideoFrame(position.getX(), position.getY(), size.getWidth(), videoHeight, video.getUrl());
        videoFrame.setPlayMode(VideoPlayModePreset.Auto);

        //load thumbnail 视频封面
        String coverUrl = video.getCoverUrl();
        if (StringUtils.isBlank(coverUrl)) {
            video.setVideoCoverUrl(qiNiuAccount.getAuth(), KnowledgeConstants.QINIU_EXPIRES);
            coverUrl = video.getCoverUrl();
        }
        URLConnection connection = null;
        try {
            URL url = new URL(coverUrl);
            connection = url.openConnection();
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
        } catch (IOException e) {
            log.error("加载视频封面视频发生错误，封面图片URL：{}", coverUrl, e);
        }

        if (null == connection) return;

        try (InputStream input = connection.getInputStream();
             ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[8192];
            for (int count; (count = input.read(buffer)) > 0; ) {
                output.write(buffer, 0, count);
            }
            output.toByteArray();
            videoFrame.getPictureFormat().getPicture().setImage(slide.getPresentation().getImages().addImage(output.toByteArray()));
        } catch (IOException e) {
            log.error("PPT添加视频封面发生错误", e);
        }
    }

    /**
     * 先将网络视频下载到本地，作为文件添加到PPT控件
     */
    private void drawVideoFromLocalFile(ISlide slide, Position position, Size size, float videoHeight, MediaItem video, PptParam param) {
        // 下载视频到本地磁盘
        String url = video.getUrl();
        String urlWithoutToken = StringUtils.substring(url, 0, url.indexOf("?"));
        String keyOfQiniu = urlWithoutToken.replace(qiNiuAccount.getURL(), "");
        // 重新获取下载URL，之前保存的token可能已经过期
        String downloadUrl = Qiniu.download(keyOfQiniu, qiNiuAccount);

        String[] array = urlWithoutToken.split("/");
        String fullName = array[array.length - 1];
        // bug fixed：文件名中包含英文句号，导致拆分错误，如：F9&F12~F14&F19-招商蛇口（佛山）-物业-佛山汀兰轩（2023.12.17）-未见有监督热线（0755-********）公示&客服人员服务不足.mp4
        // String[] nameAndSuffix = fullName.split("\\.");
        int lastDotIndex = fullName.lastIndexOf(".");
        String fileName = fullName.substring(0, lastDotIndex);
        String suffixName = fullName.substring(lastDotIndex + 1);

        String downloadPath = param.getComponents().getYmlConfig().presentationEvidenceRootPath() + File.separator;
        try {
            VideoUtils.downloadVideo(downloadUrl, downloadPath, fileName, suffixName);
        } catch (Exception e) {
            log.error("下载视频发生错误，下载地址：{}", downloadUrl, e);
            return;
        }

        // 视频本地磁盘全路径
        String videoFullPath = downloadPath + fullName;
        VideoInfo videoInfo = VideoUtils.getVideoInfo(videoFullPath);
//        float videoRealWidth = videoInfo.getWidth(), videoRealHeight = videoInfo.getHeight();
//        float videoRatio = videoRealWidth / videoRealHeight; // 视频宽高比
//        float[] scaleSize = scaleSize((int)videoRealWidth, (int)videoRealHeight, size, true);
//
//        // 计算视频的大小（保证宽高比例），首先保证视频的高度
//        float videoWidth = videoRatio * videoHeight;
//        float centerPosX = centerPosition(scaleSize[0], size.getWidth());

        // Embedd vide inside presentation
        IVideo vid;
        try {
            vid = slide.getPresentation().getVideos().addVideo(new FileInputStream(videoFullPath));
        } catch (FileNotFoundException e) {
            log.error("读取下载的PPT文件发生错误，文件不存在，文件路径：{}", videoFullPath, e);
            return;
        }

        // Add Video Frame
        IVideoFrame videoFrame = slide.getShapes().addVideoFrame(position.getX(), position.getY(), size.getWidth(), videoHeight, vid);

        // Set video to Video Frame
        videoFrame.setEmbeddedVideo(vid);

        // Set Play Mode and Volume of the Video
        videoFrame.setPlayMode(VideoPlayModePreset.Auto);
        videoFrame.setVolume(AudioVolumeMode.Loud);

        // 设置视频封面
        try (InputStream input = new FileInputStream(videoInfo.getSnapshotPath());
             ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[8192];
            for (int count; (count = input.read(buffer)) > 0; ) {
                output.write(buffer, 0, count);
            }
            output.toByteArray();
            videoFrame.getPictureFormat().getPicture().setImage(slide.getPresentation().getImages().addImage(output.toByteArray()));
        } catch (IOException e) {
            log.error("PPT添加视频封面发生错误", e);
        }

        // 删除下载的临时文件，防止占满磁盘空间
        try {
            FileUtils.deleteFile(videoFullPath);
        } catch (IOException e) {
            log.error("删除视频临时文件发生异常，路径：{}", videoFullPath, e);
        }
        try {
            FileUtils.deleteFile(videoInfo.getSnapshotPath());
        } catch (IOException e) {
            log.error("删除视频封面文件发生异常，路径：{}", videoFullPath, e);
        }
    }

    private void drawAudios(ISlide slide, GptColumn column, MediaItem[] mediaItems) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();

        float audioHeight = 50;

        // 绘制第一个音频
        MediaItem firstAudio = mediaItems[0];
        drawAudio(slide, position, size, audioHeight, firstAudio);

        if (mediaItems.length == 1) return;

        for (int i = 1; i < mediaItems.length; i++) {
            MediaItem hyperlink = mediaItems[i];

            /*
            http://120.26.8.63:10103/browse/D7-130
            附件太多，导致无位置放置第一张图片
            修改方案：如果附件数量大于3个，只展示前三个，其余的不绘制
             */
            if (i > 3) continue;

            drawHyperlink(slide, position.getX(),
                    position.getY() + audioHeight + (HYPERLINK_DEFAULT_HEIGHT * i),
                    size.getWidth(), HYPERLINK_DEFAULT_HEIGHT, hyperlink);
        }
    }

    private void drawAudiosOnePageOneEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float audioHeight = 50;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        for (int i = 0; i < mediaItems.length; i++) {
            // 第一个视频绘制在当前页面，其余视频创建新页面
            ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
            // 绘制页面标题
            drawPageTitle(param, newSlide, dynamicLayout, page);
            MediaItem audio = mediaItems[i];
            drawAudio(newSlide, position, size, audioHeight, audio);
        }
    }

    private void drawAudiosOnePageThreeEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float audioHeight = 50;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        // 多媒体数量
        int mediaItemsAmount = mediaItems.length;
        int pageSize = 1;
        int mediaIndex = 0, gapWidth = 20;

//        // 只有一个多媒体，居中布局
//        if (mediaItemsAmount == 1) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            float columnWidth = size.getWidth();
//            // 计算每列的位置和大小
//            Size columnSize = new Size(columnWidth, audioHeight);
//            MediaItem audio = mediaItems[mediaIndex];
//            drawAudio(slide, position, columnSize, audioHeight, audio);
//        }
//
//        // 只有两个多媒体，左右布局
//        else if (mediaItemsAmount == 2) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            for (int columnIndex = 0; true; columnIndex++) {
//                float columnWidth = size.getWidth() / 2 - gapWidth;
//                // 计算每列的位置和大小
//                Size columnSize = new Size(columnWidth, audioHeight);
//                Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
//                if (columnIndex > 0) {
//                    newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
//                }
//
//                MediaItem audio = mediaItems[mediaIndex];
//                drawAudio(slide, newPosition, columnSize, audioHeight, audio);
//
//                if (mediaIndex == mediaItemsAmount - 1) break;
//                mediaIndex++;
//            }
//        }
//
//        // 按一页放三个音频，一个音频一列计算页面数量
//        else {
            pageSize = mediaItems.length % 3 == 0 ? mediaItems.length / 3 : mediaItems.length / 3 + 1;
            for (int i = 0; i < pageSize; i++) {
                // 第一个视频绘制在当前页面，其余视频创建新页面
                ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
                // 绘制页面标题
                drawPageTitle(param, newSlide, dynamicLayout, page);
                for (int columnIndex = 0; columnIndex < 3; columnIndex++) {
                    float columnWidth = size.getWidth() / 3 - gapWidth * 2;
                    // 计算每列的位置和大小
                    Size columnSize = new Size(columnWidth, audioHeight);
                    Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
                    if (columnIndex > 0) {
                        newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
                    }

                    MediaItem audio = mediaItems[mediaIndex];
                    drawAudio(newSlide, newPosition, columnSize, audioHeight, audio);

                    if (mediaIndex == mediaItems.length - 1) break;
                    mediaIndex++;
                }
            }
//        }
    }

    private void drawAudio(ISlide slide, Position position, Size size, float audioHeight, MediaItem audio) {
        String audioUrl = audio.getUrl();
        String urlWithoutToken = StringUtils.substring(audioUrl, 0, audioUrl.indexOf("?"));
        String keyOfQiniu = urlWithoutToken.replace(qiNiuAccount.getURL(), "");
        // 重新获取下载URL，之前保存的token可能已经过期
        String downloadUrl = Qiniu.download(keyOfQiniu, qiNiuAccount);

        InputStream stream = null;
        try {
            URL url = new URL(downloadUrl);
            stream = url.openStream();
        } catch (IOException e) {
            log.error("加载音频资源发生错误, URL: {}", downloadUrl, e);
        }

        // Add Audio Frame
        IAudioFrame af = slide.getShapes().addAudioFrameEmbedded(position.getX(), position.getY(), audioHeight, audioHeight, stream);

        // Set Play Mode and Volume of the Audio
        af.setPlayMode(AudioPlayModePreset.Auto);
        af.setVolume(AudioVolumeMode.Loud);
        // 绘制第一个音频的标题
        IAutoShape firstVideoTitleShape = textShape(slide,
                Position.builder().x(position.getX()).y(position.getY() + audioHeight).build(),
                Size.builder().width(size.getWidth()).height(HYPERLINK_DEFAULT_HEIGHT).build());
//        drawText(firstVideoTitleShape, audio.getName(), null);
        drawHyperlink(slide, firstVideoTitleShape.getX(), firstVideoTitleShape.getY(), size.getWidth(), audioHeight, audio);
    }

    private void drawImages(ISlide slide, GptColumn column, MediaItem[] mediaItems) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();

        // 第一个图片使用图片控件展示，其他图片显示链接，通过链接的数量反推图片的尺寸，计算高度
        int hyperlinkAmount = mediaItems.length - 1;
        if (hyperlinkAmount > 3) hyperlinkAmount = 3;
        float imageHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT * (hyperlinkAmount + 1); // +1因为第一个图片有自己的标题

        // 绘制第一个图片
        MediaItem firstVideo = mediaItems[0];
        drawImage(slide, position, size, imageHeight, firstVideo, true);

        if (mediaItems.length == 1) return;

        for (int i = 1; i < mediaItems.length; i++) {
            MediaItem hyperlink = mediaItems[i];

            /*
            http://120.26.8.63:10103/browse/D7-130
            附件太多，导致无位置放置第一张图片
            修改方案：如果附件数量大于3个，只展示前三个，其余的不绘制
             */
            if (i > 3) continue;

            drawHyperlink(slide, position.getX(),
                    position.getY() + imageHeight + (HYPERLINK_DEFAULT_HEIGHT * i),
                    size.getWidth(), HYPERLINK_DEFAULT_HEIGHT, hyperlink);
        }
    }

    private void drawImagesOnePageOneEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float imageHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        for (int i = 0; i < mediaItems.length; i++) {
            // 第一个视频绘制在当前页面，其余视频创建新页面
            ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
            // 绘制页面标题
            drawPageTitle(param, newSlide, dynamicLayout, page);
            MediaItem image = mediaItems[i];
            drawImage(newSlide, position, size, imageHeight, image, true);
        }
    }

    private void drawImagesOnePageThreeEvidence(Presentation ppt, ISlide slide, GptColumn column, MediaItem[] mediaItems, PptParam param, GptPptPage page) {
        Position position = column.getLayoutV6().getPosition();
        Size size = column.getLayoutV6().getSize();
        float imageHeight = size.getHeight() - HYPERLINK_DEFAULT_HEIGHT;

        GptLayout dynamicLayout = new GptLayout();
        dynamicLayout.setLayoutV6(column.getLayoutV6());

        // 多媒体数量
        int mediaItemsAmount = mediaItems.length;
        int pageSize = 1;
        int mediaIndex = 0, gapWidth = 20;

//        // 只有一个多媒体，居中布局
//        if (mediaItemsAmount == 1) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            float columnWidth = size.getWidth();
//            // 计算每列的位置和大小
//            Size columnSize = new Size(columnWidth, imageHeight);
//            MediaItem image = mediaItems[mediaIndex];
//            drawImage(slide, position, columnSize, imageHeight, image, true);
//        }
//
//        // 只有两个多媒体，左右布局
//        else if (mediaItemsAmount == 2) {
//            drawPageTitle(param, slide, dynamicLayout, page);
//            for (int columnIndex = 0; true; columnIndex++) {
//                float columnWidth = size.getWidth() / 2 - gapWidth;
//                // 计算每列的位置和大小
//                Size columnSize = new Size(columnWidth, imageHeight);
//                Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
//                if (columnIndex > 0) {
//                    newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
//                }
//
//                MediaItem image = mediaItems[mediaIndex];
//                drawImage(slide, newPosition, columnSize, imageHeight, image, false);
//
//                if (mediaIndex == mediaItemsAmount - 1) break;
//                mediaIndex++;
//            }
//        }
//
//        // 三个以上分页
//        // 按一页放三个图片，一个图片一列计算页面数量
//        else {
            pageSize = mediaItemsAmount % 3 == 0 ? mediaItemsAmount / 3 : mediaItemsAmount / 3 + 1;
            for (int i = 0; i < pageSize; i++) {
                // 第一个图片绘制在当前页面，其余图片创建新页面
                ISlide newSlide = i == 0 ? slide : SlidesEngine.createBlankSlide(ppt);
                // 绘制页面标题
                drawPageTitle(param, newSlide, dynamicLayout, page);
                for (int columnIndex = 0; columnIndex < 3; columnIndex++) {
                    float columnWidth = size.getWidth() / 3 - gapWidth * 2;
                    // 计算每列的位置和大小
                    Size columnSize = new Size(columnWidth, imageHeight);
                    Position newPosition = new Position(position.getX() + columnIndex * columnWidth + gapWidth * columnIndex, position.getY());
                    if (columnIndex > 0) {
                        newPosition.setX(newPosition.getX() + gapWidth * columnIndex);
                    }

                    MediaItem image = mediaItems[mediaIndex];
                    drawImage(newSlide, newPosition, columnSize, imageHeight, image, false);

                    if (mediaIndex == mediaItemsAmount - 1) break;
                    mediaIndex++;
                }
            }
//        }
    }

    private void drawImage(ISlide slide, Position position, Size size, float imageHeight, MediaItem image, boolean reCalPosition) {
        String url = image.getUrl();
        String downloadUrl = imageUrl(url);

        // Instantiate the ImageEx class
        BufferedImage img = ImageUtil.read(downloadUrl);
        int retryTimes = 0, retryThreshold = 3;
        if (null == img) {
            log.warn("下载图片失败：{}", downloadUrl);
            while (retryTimes < retryThreshold) {
                log.info("第[{}]次重试下载图片：{}", retryTimes, downloadUrl);
                img = ImageUtil.read(downloadUrl);
                if (null != img) break;

                log.warn("第[{}]次重试下载图片失败：{}", retryTimes, downloadUrl);
                retryTimes++;
            }
            return;
        }
        IPPImage imgx = slide.getPresentation().getImages().addImage(img);

        // 计算图片的尺寸
        int imgWidth = imgx.getWidth(), imgHeight = imgx.getHeight();
        float[] scaleSize = scaleSize(imgWidth, imgHeight, size, reCalPosition);
        // 是否重新计算图片的中心位置
        float centerPosX = position.getX();
        if (reCalPosition) {
            centerPosX = centerPosition(scaleSize[0], size.getWidth());
        }

        // Add Picture Frame with height and width equivalent of Picture
        slide.getShapes().addPictureFrame(ShapeType.Rectangle, centerPosX, position.getY(), scaleSize[0], scaleSize[1], imgx);

        // 绘制第一个视频的标题
        IAutoShape firstVideoTitleShape = textShape(slide,
                Position.builder().x(position.getX()).y(position.getY() + imageHeight).build(),
                Size.builder().width(size.getWidth()).height(HYPERLINK_DEFAULT_HEIGHT).build());
//        if (!reCalPosition) { // 目前只有一页三个证据的时候，文本加粗
//            // 不起作用
//            firstVideoTitleShape.getTextFrame().getTextFrameFormat().getTextStyle().getDefaultParagraphFormat().getDefaultPortionFormat().setFontBold((byte) 1);
//        }
//        drawText(firstVideoTitleShape, image.getName(), CssStyle.parse("text-align:center"));
        drawText(firstVideoTitleShape, image.getName(), null);
    }

    private float[] scaleSize(int width, int height, Size size, boolean reCalPosition) {
        float ratio = new BigDecimal(width).divide(new BigDecimal(height), 8, RoundingMode.HALF_UP).floatValue();
        // ratio > 1 是横向图片（首先保证宽度），ratio < 1 是纵向图片（首先保证高度）
        float actualWidth = 0f, actualHeight = 0f;
        if (ratio >= 1) {
            actualWidth = size.getWidth();
            actualHeight = size.getHeight() / ratio;
        }
        if (ratio < 1) {
            actualHeight = size.getHeight();
            actualWidth = actualHeight * ratio;
        }
        log.info("原始宽高【{}, {}】，缩放宽高【{}, {}】", width, height, actualWidth, actualHeight);
        return new float[]{actualWidth, actualHeight};
    }

    private float centerPosition(float itemWidth, float pageWidth) {
        return (pageWidth - itemWidth) / 2;
    }

    private String imageUrl(String url) {
        // [需求]实现图片截图生成ppt http://120.26.8.63:10103/browse/D7-202
        if (StringUtils.startsWith(url, "http://knowledge.fg-china.cn/")) {
            return url;
        }

        String downloadUrl;
        if (StringUtils.startsWith(url, "http://yt-img.fg-china.cn")) {
            url = url.replace("http://yt-img.fg-china.cn", "https://yt-img.fg-china.cn");
        }

        // 链家图片直接下载
        if (StringUtils.contains(url, "ljcdn.com")) {
            downloadUrl = url;
        }
        // 链家转存七牛图片直接下载
        else if (StringUtils.startsWith(url, "https://yt-img.fg-china.cn")) {
            downloadUrl = url;
        }
        // 七牛图片拼接私有链接
        else {
            String urlWithoutToken = StringUtils.substring(url, 0, url.indexOf("?"));
            // URL 转码，替换掉 URL 中的空格，有空格会报错
            urlWithoutToken = URLEncodeUtil.encode(urlWithoutToken);
            String keyOfQiniu = urlWithoutToken.replace(qiNiuAccount.getURL(), "");
            // 重新获取下载URL，之前保存的token可能已经过期
            downloadUrl = Qiniu.download(keyOfQiniu, qiNiuAccount);
        }
        return downloadUrl;
    }

    private void drawHyperlink(ISlide slide, float x, float y, float width, float height, MediaItem hyperlink) {
        IAutoShape hyperlinkShape = slide.getShapes().addAutoShape(ShapeType.Rectangle, x, y, width, height, false);

        hyperlinkShape.addTextFrame(hyperlink.getName());

        String url = hyperlink.getUrl();
        String downloadUrl = imageUrl(url);

        IPortionFormat portionFormat = hyperlinkShape.getTextFrame().getParagraphs().get_Item(0).getPortions().get_Item(0).getPortionFormat();
        portionFormat.setHyperlinkClick(new Hyperlink(downloadUrl));
        portionFormat.getHyperlinkClick().setTooltip(hyperlink.getName());
        portionFormat.setFontHeight(10);
        portionFormat.setFontBold((byte) 1); // 默认加错链接字体

        hyperlinkShape.getFillFormat().setFillType(FillType.NoFill);

        ILineFormat lineFormat = hyperlinkShape.getLineFormat();
        lineFormat.getFillFormat().setFillType(FillType.NoFill);
        lineFormat.getFillFormat().getSolidFillColor().setColor(Color.WHITE);
    }

    public void clear() {
        if (null == this.browser) return;

        this.browser.close();
        this.browser.disconnect();
        log.info("Gpt-PPT 关闭Chromium浏览器");
    }
}
