package pub.yuntu.superbrain.gpt.domain.ppt.template;

import com.aspose.slides.*;
import com.google.common.collect.Maps;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.foundation.json.JsonMapper;
import pub.yuntu.superbrain.gpt.domain.ppt.param.PptParam;
import pub.yuntu.superbrain.gpt.domain.ppt.component.StandardComponent;
import pub.yuntu.superbrain.gpt.domain.ppt.component.StandardComponentTypeNames;
import pub.yuntu.superbrain.gpt.domain.ppt.util.PresentationUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>ven on 2020/6/2 23:30
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentTemplate extends BaseTemplate {

    private static final String COMPONENT_TEMPLATE_NAME = "component-template.pptx";
    private static final String COMPONENT_V5_TEMPLATE_NAME = "component-template-v5.pptx";
    private static final JsonMapper JSON_MAPPER = new JsonMapper();
    private static long templateLastUpdateTime;
    Presentation presentation;
    /**
     * key: StandardComponent name, value: StandardComponent
     */
    Map<String, StandardComponent> components;

    public static List<StandardComponent> parse(IShapeCollection shapes, int slidePageNo) {
        IShape configShape = null;
        List<IShape> componentShapes = Lists.newArrayList();

        for (int i = 0; i < shapes.size(); i++) {
            final IShape shape = shapes.get_Item(i);
            if (isConfigShape(shape)) {
                configShape = shape;
            } else {
                componentShapes.add(shape);
            }
        }

        // 解析配置
        StandardComponentTypeNames standardComponentTypeNames = parseConfigMark(configShape, slidePageNo);
        // 拷贝标准组件
        List<StandardComponent> result = new ArrayList<>();
        if (null == standardComponentTypeNames) return result;

        for (String name : standardComponentTypeNames.getNames()) {
            StandardComponent standardComponent = new StandardComponent();
            standardComponent.setName(name);
            standardComponent.setComponentShapes(componentShapes);
            standardComponent.setType(standardComponentTypeNames.getComponentType());
            standardComponent.setSlidePageNo(slidePageNo);
            result.add(standardComponent);
        }
        return result;
    }

    /**
     * 解析配置部分
     */
    public static StandardComponentTypeNames parseConfigMark(IShape configShape, int slidePageNo) {
        return PresentationUtil.parseConfigMark(configShape, slidePageNo);
    }

    public static boolean isConfigShape(IShape shape) {
        if (shape instanceof AutoShape) {
            AutoShape _shape = (AutoShape) shape;
            String text = _shape.getTextFrame().getText();
            return text.startsWith("{");
        }
        return false;
    }

    public void load(PptParam param, Integer version) {
        long refreshTemplateUpdateTime = getTemplateUpdateTime(param.getTemplateRootPath(), templateName(version));
        if (null != presentation && templateLastUpdateTime == refreshTemplateUpdateTime) {
            log.info("模板没有发生变化，不重新加载");
            return;
        }
        templateLastUpdateTime = refreshTemplateUpdateTime;
        presentation = loadComponentTemplate(param.getTemplateRootPath(), version);
        readComponents();
    }

    private String templateName(Integer version) {
        return version == 5 ? COMPONENT_V5_TEMPLATE_NAME : COMPONENT_TEMPLATE_NAME;
    }

    /**
     * 加载标准组件模板
     *
     * @param templateRootPath 模板根路径
     * @param version          版本号，5.0和3.0使用不同的组件模板
     */
    private Presentation loadComponentTemplate(String templateRootPath, Integer version) {
        long start = System.currentTimeMillis();
        log.info("使用基础组件模板：{}", templateName(version));

        Presentation presentation = loadTemplate(templateRootPath, templateName(version));
        log.info("载入基础组件模板完成，用时：{}", DateUtil.calPassedTime(start));

        return presentation;
    }

    /**
     * 读取标准组件
     */
    public void readComponents() {
        final ISlideCollection componentSlides = getPresentation().getSlides();
        for (int i = 0; i < componentSlides.size(); i++) {
            final ISlide componentSlide = componentSlides.get_Item(i);
            final IShapeCollection shapes = componentSlide.getShapes();

            if (shapes.size() == 0) {
                throw new IllegalStateException(String.format("请检查标准组件模板第%d页，没有配置组件", i));
            }

            List<StandardComponent> standardComponents = parse(shapes, i);
            log.debug("Parsed Component on page {}: {}", i, standardComponents.size());

            if (null == getComponents()) {
                setComponents(Maps.newHashMap());
            }

            for (StandardComponent standardComponent : standardComponents) {
                getComponents().put(standardComponent.getName(), standardComponent);
                log.debug("已注册 {} 类标准组件：[{}]", standardComponent.getType(), standardComponent.getName());
            }
        }
    }

    public void clear() {
        for (StandardComponent standardComponent : components.values()) {
            standardComponent.clear();
            // standardComponent = null;
        }
        this.components.clear();
        this.components = null;
        if (null != this.getPresentation()) {
            this.getPresentation().dispose();
            this.presentation = null;
        }
    }
}
