package pub.yuntu.superbrain.infrastructure.persistence.legacy;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.transaction.annotation.Transactional;
import pub.yuntu.foundation.reference.org.OrgCategoryID;
import pub.yuntu.superbrain.domain.model.legacy.msdExam.MsdExam;
import pub.yuntu.superbrain.infrastructure.persistence.AbstractRepository;

import java.util.List;

/**
 * 神客问卷(ExMsdExam)表数据库访问层
 *
 * <AUTHOR> <PERSON>
 * @since 2021-01-18 22:32:29
 */
public interface MsdExamRepository extends AbstractRepository<MsdExam> {

    @Query("select a from MsdExam a where a._id=?1")
    List<MsdExam> findBy_id(@Param("_id") long _id);

    @Query("select a from MsdExam a where a.status=?1")
    List<MsdExam> findByStatus(@Param("status") String status);

    @Query("select a from MsdExam a where a.name.name like %?1% order by _id desc")
    Page<MsdExam> findByName(@Param("name") String name, Pageable pageable);

    @Query("select a from MsdExam a where a.name.name like %?1% and a._id=?2 order by _id desc")
    Page<MsdExam> findByNameAndId(@Param("name") String name, @Param("examDbId") Long examDbId, Pageable pageable);

    @RestResource(path = "findByProjectID", rel = "findByProjectID")
    @Query("select a from MsdExam a where a.projectID.value=?1 order by _id desc")
    List<MsdExam> findByProjectID(@Param("projectID") String projectID);

    @Query("select a from MsdExam a where a.name.name like %?1% and a.status='FORMAL' order by _id desc")
    Page<MsdExam> findFormalByName(@Param("name") String name, Pageable pageable);

    @Query("select a from MsdExam a where a.orgCategoryID=?1 order by _id asc")
    List<MsdExam> findByOrgCategoryID(@Param("orgCategoryID") OrgCategoryID orgCategoryID);

    @Query("select a from MsdExam a where a.customerID.value=?1 and a.name.name like %?2% and a.status='FORMAL' order by _id desc")
    List<MsdExam> findByCustomerIdAndName(@Param("customerId") String customerId, @Param("name") String name);

    @Modifying
    @Transactional
    @Query("delete from MsdExam a where a.id in ?1")
    void deleteByIdList(List<String> legacyExamIdList);

    @Query("select a from MsdExam a where a.status='FORMAL' order by _id asc")
    List<MsdExam> findAllFormal();

    @Query("select a from MsdExam a where a.id in (?1) order by _id asc")
    List<MsdExam> findByIdList(List<String> idList);
}