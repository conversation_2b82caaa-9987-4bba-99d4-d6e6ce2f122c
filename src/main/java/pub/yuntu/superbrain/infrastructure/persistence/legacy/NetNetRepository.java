package pub.yuntu.superbrain.infrastructure.persistence.legacy;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pub.yuntu.superbrain.domain.model.legacy.net.net.NetNet;
import pub.yuntu.superbrain.infrastructure.persistence.AbstractRepository;

import java.util.List;

/**
 * 知识图谱 触点网络(NetNet)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-05-17 14:51:58
 */
public interface NetNetRepository extends AbstractRepository<NetNet> {

    @Query("select a from NetNet a where a._id=?1")
    List<NetNet> findBy_id(@Param("_id") long _id);

    @Query("select a from NetNet a where a.status=?1")
    List<NetNet> findByStatus(@Param("status") String status);

    @Query("select a from NetNet a where a.id in (?1)")
    List<NetNet> findByIdList(List<String> netIdList);

    @Query("select a from NetNet a where a._id in (?1) and a.status='FORMAL'")
    List<NetNet> findByDbIdList(List<Long> netDbIds);
}
