package pub.yuntu.superbrain.infrastructure.persistence.deliver;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import pub.yuntu.superbrain.domain.model.deliver.DeliverStat;
import pub.yuntu.superbrain.infrastructure.persistence.AbstractRepository;

import java.util.List;

public interface DeliverStatRepository extends AbstractRepository<DeliverStat> {

    @Query("select a from DeliverStat a where a.project=?1")
    List<DeliverStat> findByProject(@Param("project") String project);

    @Query("select a from DeliverStat a where a.customerId=?1")
    List<DeliverStat> findByCustomerId(@Param("customerId") String customerId);
}