{"id": 10, "author": "梅智", "name": "31_同城对比和排名", "dafeStreams": [{"type": "mysql", "signalType": "industryAna", "tableNames": ["$anaTableName"], "dbIdName": "_id", "dataCondition": "$anaDataCondition"}], "poolParam": {"indexPairs": [{"singleIndexId": "1", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "2", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "3", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "4", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "5", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "6", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "7", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "8", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "9", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "10", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "11", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "12", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "13", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "14", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "15", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "37", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "2783", "signalType": "industryAna", "singleDataTypes": ["P_45"]}, {"singleIndexId": "84512", "signalType": "industryAna", "singleDataTypes": ["P_45"]}], "periodPairs": [{"mainPeriod": "$mainPeriod"}], "constants": {"constantsMap": {"$anaTableName": "dataset_1_ana", "$anaDataCondition": "split_group_id in (35,38) AND data_period = '2022-0112' and source_table_field in ('ra1a5','ra3a5','ra2a5','rc0a5','rc03a5','rd0a5','re0a5','rf0a5','rk0a5','rj0a5','rh0a5','rt0a5','rl0a5','rx0a5','rq03a5','re04a5','rt0a52')", "$fixedNum1": 5, "$fixedNum2": 0.5, "$fixedNum3": 0.25, "$fixedNum4": 4, "$mainPeriod": "2022-0112"}}, "actions": [{"action": "sort", "name": "按城市维度排序所有城市的开发商", "type": "line", "unitPrepare": {"convertUnit": "$groupBy.city", "loop": "", "saveUnit": "original", "saveUnit说明": "sort动作特殊，original必须填写，即使配置了 type=line, 结果也会保存在每个切分单元身上，每个切分单元都有自己的排名信息", "condition": ""}, "order": "desc", "returnValue": "$sort"}, {"action": "collect", "name": "收集每个城市的总体值放入LineSpace", "type": "line", "unitPrepare": {"convertUnit": "$children.benchMarkGroup", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "1==1", "group": "$benchMark1", "returnValue": "$city", "collectFormat": {"$splitName": "$split"}, "取数表达式": "$city[cityName]", "结果": "结果标记在切分单元身上"}, {"action": "diff", "name": "与城市总体差值", "说明": "行业城市的总体均值，是当前切分的城市，与当前切分的城市要一一对应，行业城市总体的值与行业总体不同，按城市来取值", "diffType": "splitDiff", "right": "$benchMark1.$city[$metaGivenValue]", "metaGivenValue": "$split.city", "metaGivenValue 说明": "将 left 的属性记录在上下文的 meta 中，right 匹配城市时使用", "returnValue": "$diff", "取数表达式": "$diff"}, {"action": "count", "name": "开发商在排名前三城市的数量", "unitPrepare": {"convertUnit": "$groupBy.customerName", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "$sort[sort] <= 3", "returnValue": "$开发商在排名前三城市的数量", "说明": "将所有的开发商+城市的切分单元按开发商分组，统计出排名前三的城市数量，但是由于没有单独开发商的切分，计算的结果将在每个开发商+城市的切分单元中都保存一份"}, {"action": "count", "name": "开发商布局城市的数量", "unitPrepare": {"convertUnit": "$groupBy.customerName", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "1==1", "returnValue": "$开发商布局城市的数量"}, {"action": "count", "name": "开发商布局城市中高于城市总体的数量", "unitPrepare": {"convertUnit": "$groupBy.customerName", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "$diff > 0", "returnValue": "$开发商布局城市中高于城市总体的数量"}, {"action": "proportion", "name": "高于城市均值占比", "numerator": "$开发商布局城市中高于城市总体的数量", "denominator": "$开发商布局城市的数量", "returnValue": "$高于城市均值占比"}, {"action": "proportion", "name": "排名前三城市的占比", "numerator": "$开发商在排名前三城市的数量", "denominator": "$开发商布局城市的数量", "returnValue": "$排名前三城市的占比"}], "summaries": [{"name": "结论1", "treaties": [{"text": "$开发商布局城市的数量 > $fixedNum1"}, {"text": "$高于城市均值占比 > $fixedNum2"}, {"text": "$排名前三城市的占比 < $fixedNum3"}], "dspText": {"conclusionType": "单指标对比", "conclusionName": "同城对比和排名", "businessType": "总结", "dimension": "upperVs", "score": "-1", "text": "同城市对标，#$indexName#多数达到当地均值，但占领导地位的城市少。", "结果查询 NGQL，后面的都一样": "lookup on conclusion where conclusion.conclusionType=='单指标对比' and conclusion.conclusionName=='同城对比和排名' and conclusion.businessType=='总结' and conclusion.dimension=='upperVs' yield conclusion.conclusionType, conclusion.conclusionName, conclusion.conclusionText, conclusion.dataPeriod, conclusion.indexId, conclusion.indexCode, conclusion.planarCode, conclusion.splitGroupId, conclusion.splitUnitId, conclusion.splitUnitName, conclusion.attribute, conclusion.businessType, conclusion.dimension, conclusion.score"}}, {"name": "结论2", "treaties": [{"text": "$开发商布局城市的数量 > $fixedNum1"}, {"text": "$高于城市均值占比 > $fixedNum2"}, {"text": "$开发商在排名前三城市的数量 < $fixedNum4"}], "dspText": {"conclusionType": "单指标对比", "conclusionName": "同城对比和排名", "businessType": "总结", "dimension": "upperVs", "score": "-1", "text": "同城市对标，#$indexName#多数达到当地均值，但占领导地位的城市少。"}}], "tasks": [{"batchNo": 1, "name": "同城对比和排名", "mainSplitGroupId": "A_35", "benchMarkGroupId1": "A_38", "actions": ["按城市维度排序所有城市的开发商", "收集每个城市的总体值放入LineSpace", "与城市总体差值", "开发商在排名前三城市的数量", "开发商布局城市的数量", "开发商布局城市中高于城市总体的数量", "高于城市均值占比", "排名前三城市的占比"], "summaries": ["结论1", "结论2"]}]}}