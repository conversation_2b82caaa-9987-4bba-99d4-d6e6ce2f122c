package pub.yuntu.superbrain.domain.model.creativity.parser.segment;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.id.IdGenerator;
import pub.yuntu.superbrain.domain.model.creativity.CreativityParam;
import pub.yuntu.superbrain.domain.model.creativity.block.TableBlock;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPointBuilder;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.brainPointProducer.*;
import pub.yuntu.superbrain.domain.model.creativity.processor.AbstractProcessor;
import pub.yuntu.superbrain.domain.model.creativity.processor.ProcessorParam;
import pub.yuntu.superbrain.domain.model.legacy.customer.Customer;
import pub.yuntu.superbrain.domain.model.legacy.indexGrid.IndexGrid;
import pub.yuntu.superbrain.domain.model.legacy.kg.GraphEdge;
import pub.yuntu.superbrain.domain.model.legacy.kg.GraphInteraction;
import pub.yuntu.superbrain.domain.model.legacy.kg.NebulaGraph;
import pub.yuntu.superbrain.domain.model.legacy.net.grouping.AbstractGrouping;
import pub.yuntu.superbrain.domain.model.legacy.net.grouping.LegacyIndustryGrouping;
import pub.yuntu.superbrain.domain.model.legacy.net.grouping.LegacyOrgGrouping;
import pub.yuntu.superbrain.domain.model.legacy.net.interaction.NetInteraction;
import pub.yuntu.superbrain.domain.model.legacy.net.interaction.NetInteractionAssembly;
import pub.yuntu.superbrain.domain.model.legacy.net.net.NetNet;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgCategory;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgUnit;
import pub.yuntu.superbrain.domain.model.legacy.rh.param.CommercialParam;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.AbstractViewPointDataMatrixInstance;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.ViewPointDataMatrixInstance;
import pub.yuntu.superbrain.domain.model.legacy.split.AllianceSplitGroup;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitGroup;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.standard.StandardParam;
import pub.yuntu.superbrain.domain.model.legacy.stream.MsdType;
import pub.yuntu.superbrain.domain.model.legacy.superbrain.data.signal.LianjiaSignal;
import pub.yuntu.superbrain.domain.model.standard.StandardAttributeEnum;

import java.util.*;

/**
 * Created by Qianyi on 2022/3/18 18:06.
 */
@Slf4j
@Data
public class BrainDataSegment extends AbstractSegment {
    public static final String[] OWNER_FOUR_ARRAY = new String[]{"准业主", "磨合期", "稳定期", "老业主"};
    public static final String[] OWNER_SEVEN_ARRAY = new String[]{"准业主1", "准业主2", "准业主3", "磨合期1", "磨合期2", "稳定期", "老业主1", "老业主2", "老业主3"};
    public static final String[] OWNER_ALL_ARRAY = new String[]{"准业主", "磨合期", "稳定期", "老业主", "", "准业主1", "准业主2", "准业主3", "磨合期1", "磨合期2", "稳定期", "老业主1", "老业主2", "老业主3"};
    public static final String[] HOUSE_TYPE_ARRAY = new String[]{"高层", "小高层", "多层/花园洋房", "别墅", "酒店式公寓/SOHO公寓", "其他"};
    public static final String[] DECORATION_TYPE_ARRAY = new String[]{"毛坯房", "装修房"};
    public static final String[] PROPERTYCOSTLEVEL_ARRAY = new String[]{"低于1元", "1至2元", "2至3元", "3至4元", "4至5元", "5至8元", "8至12元", "12元以上"};
    String expression;
    boolean loaded;
    List<String> postProcessList;
    // 切分
    List<Long> splitUnitDbIdList;
    String orgCategoryId;
    MsdType msdType;
    boolean isIndustry;
    // 属性
    List<String[]> attributeList;
    String attributeExp;
    // 触点
    List<String> indexCodeList;
    Map<String, NetInteraction> idAndNetInteractionMap;
    Map<String, GraphInteraction> planarCodeAndGraphInteractionMap;
    Map<String, NetInteractionAssembly> planarCodeAndAssemblyMap;
    // 知识点
    String statDimension;
    String knowledgePointType;
    List<String> knowledgePointSecondTypes;
    List<String> nonStandardCustomers;
    String geoType;
    List<String> geoNames;
    String percentBy;
    // 行业销售数据
    String scope;
    // 高净值客户
    String highNetWorthClientSecondType;
    // 链家
    String louPanType;
    String imgType;
    // 克而瑞城市已售产品类型 salesTypeStat
    String analysisType;
    String branch;
    List<String> periods;
    String dataType;
    String nameType;
    String xNameType;
    String yNameType;
    String[] dataPrepareKeys;
    String postCalculateType;
    Map<String, String> filterVoiceConditions;
    String brainName;

    public BrainDataSegment(String expression) {
        this.expression = expression;
        this.loaded = false;
    }

    @Deprecated
    public String asText(CreativityParam param) {
        if (!loaded) {
            List<AbstractDataPoint> dataPointList;
            if (param.getCurrentBlock().pointFromNetwork && !StringUtils.contains(this.expression, "branch:dataPrepare")) {
                dataPointList = param.getCurrentBlock().pointsFromNetwork(param, "{" + this.expression + "}");
            } else {
                dataPointList = asDataPointList(param);
            }
            if (dataPointList != null && dataPointList.size() > 0) {
                AbstractDataPoint dataPoint = dataPointList.get(0);
                if (StringUtils.isNotBlank(dataPoint.getStrValue())) {
                    return dataPoint.getStrValue();
                } else {
                    return Math.round(dataPoint.getRealValue()) + "";
                }
            }
        }
        return null;
    }

    public String _asText(CreativityParam param) {
        if (!loaded) {
            List<AbstractDataPoint> dataPointList;
            if (param.getCurrentBlock().pointFromNetwork && !StringUtils.contains(this.expression, "branch:dataPrepare")) {
                dataPointList = param.getCurrentBlock()._pointsFromNetwork(param, "{" + this.expression + "}");
            } else {
                dataPointList = asDataPointList(param);
            }
            if (dataPointList != null && dataPointList.size() > 0) {
                AbstractDataPoint dataPoint = dataPointList.get(0);
                if (StringUtils.isNotBlank(dataPoint.getStrValue())) {
                    return dataPoint.getStrValue();
                } else {
                    return Math.round(dataPoint.getRealValue()) + "";
                }
            }
        }
        return null;
    }

    public List<AbstractDataPoint> asDataPointList(CreativityParam param) {
        List<AbstractDataPoint> result = new ArrayList<>();
        if (!loaded) {
            parseExpression(param);

            AbstractBrainPointProducer pointProducer = getPointProducer();
            if (pointProducer == null)
                return result;
            result = pointProducer.producePoints(param, this);
            result = postProcess(param, result);

            if (StringUtils.isNotBlank(this.attributeExp)
                    && !isContainPostProcess("sort")
                    && !isContainPostProcess("interval"))
                result = sortAttribute(param, this.attributeExp, result);
        }
        return result;
    }

    public List<AbstractDataPoint> buildDataNetwork(CreativityParam param) {
        List<AbstractDataPoint> result = new ArrayList<>();
        if (!loaded) {
            parseExpression(param);

            AbstractBrainPointProducer pointProducer = getPointProducer();
            if (pointProducer == null)
                return result;
            result = pointProducer.buildDataNetwork(param, this);
            param.getCommercialParam().getExpAndPostProcessMap().putIfAbsent("{" + this.expression + "}", this.postProcessList);
        }
        return result;
    }

    @Override
    public List<AbstractDataPoint> _buildDataNetwork(CreativityParam param) {
        return buildDataNetwork(param);
    }

    @Override
    public void clear() {
        if (null != postProcessList) {
            postProcessList.clear();
        }
        if (null != splitUnitDbIdList) {
            splitUnitDbIdList.clear();
        }
        if (null != attributeList) {
            attributeList.clear();
        }
        if (null != indexCodeList) {
            indexCodeList.clear();
        }
        if (null != idAndNetInteractionMap) {
            idAndNetInteractionMap.clear();
        }
        if (null != planarCodeAndGraphInteractionMap) {
            planarCodeAndGraphInteractionMap.clear();
        }
        if (null != planarCodeAndAssemblyMap) {
            planarCodeAndAssemblyMap.clear();
        }
        if (null != knowledgePointSecondTypes) {
            knowledgePointSecondTypes.clear();
        }
        if (null != nonStandardCustomers) {
            nonStandardCustomers.clear();
        }
        if (null != geoNames) {
            geoNames.clear();
        }
        if (null != filterVoiceConditions) {
            filterVoiceConditions.clear();
        }
    }

    private void parseExpression(CreativityParam param) {
        String[] expressionArray = this.expression.split(",");
        for (String anExpression : expressionArray) {
            String[] exps = anExpression.split(":");
            switch (exps[0]) {
                case "brain":
                    this.brainName = exps[1];
                    break;
                case "branch":
                    this.branch = exps[1];
                    break;
                case "split":
                    // split:group
                    setSplit(param, exps);
                    break;
                case "attribute":
                    // attribute:[NONE][OWNER_FOUR_MO][OWNER_FOUR_MO;DECORATION_TYPE_ZHUANG_XIU]
                    addAttributeList(param, exps[1]);
                    this.attributeExp = anExpression;
                    break;
                case "period":
                    // period:current
                    setPeriod(param, exps[1]);
                    break;
                case "interaction":
                    // interaction:satis:code:201002
                    setInteractionPlanarCodeAndIndexCode(param, anExpression);
                    break;
                case "indexLevel":
                    // indexLevel:2/3
                    filterIndexCodeByIndexLevel(param, exps[1]);
                    break;
                case "related":
                    // related:satis
                    relatedInteraction(param, exps[1]);
                    break;
                case "dataType":
                    // dataType:P-45
                    this.dataType = exps[1];
                    break;
                case "name":
                    // name:index
                    this.nameType = exps[1];
                    break;
                case "xName":
                    // xName:index
                    this.xNameType = exps[1];
                    break;
                case "yName":
                    // yName:index
                    this.yNameType = exps[1];
                    break;
                case "statDimension":
                    this.statDimension = exps[1];
                    break;
                case "knowledgePointType":
                    this.knowledgePointType = exps[1];
                    break;
                case "knowledgePointSecondType":
                    this.knowledgePointSecondTypes = Arrays.asList(exps[1].split(";"));
                    break;
                case "nonStandardCustomer":
                    setNonStandardCustomers(param, exps[1]);
                    break;
                case "geoType":
                    this.geoType = exps[1];
                    break;
                case "geoName":
                    this.geoNames = Arrays.asList(exps[1].split(";"));
                    break;
                case "percentBy":
                    this.percentBy = exps[1];
                    break;
                case "postProcess":
                    setPostProcessList(exps[1]);
                    break;
                case "dataPrepare":
                    this.dataPrepareKeys = exps[1].split("/");
                    break;
                case "scope":
                    this.scope = exps[1];
                    break;
                case "highNetWorthClientSecondType":
                    this.highNetWorthClientSecondType = exps[1];
                    break;
                case "louPanType":
                    // louPanType:住宅
                    this.louPanType = exps[1];
                    break;
                case "productLabel":
                    // productLabel:高端
                    projectOfProductLabel(param, exps[1]);
                    break;
                case "filterSplit":
                    filterSplit(param, exps);
                    break;
                case "analysisType":
                    // analysisType:城市+房型+面积
                    this.analysisType = exps[1];
                    break;
                case "filterVoice":
                    filterVoice(exps[1]);
                    break;
                case "imgType":
                    // imgType:效果图
                    this.imgType = exps[1];
                    break;
            }
        }

        if (this.attributeList == null) {
            this.attributeList = new ArrayList<>();
            this.attributeList.add(new String[]{"总体"});
        }
    }

    protected AbstractBrainPointProducer getPointProducer() {
        switch (this.branch) {
            case "satis":
                return new SatisBrainPointProducer();
            case "msd":
                return new MsdBrainPointProducer();
            case "industryFree":
                return new IndustryFreeBrainPointProducer();
            case "industryMark":
                return new IndustryMarkBrainPointProducer();
            case "text":
                return new TextBrainPointProducer();
            case "house":
                return new HouseBrainPointProducer();
            case "nameList":
                return new NameListBrainPointProducer();
            case "knowledgePoint":
                return new KnowledgePointBrainPointProducer();
            case "sameBatch":
                return new SameBatchBrainPointProducer();
            case "global":
                return new GlobalBrainPointProducer();
            case "voice":
                return new VoiceBrainPointProducer();
            case "globalVoice":
                return new GlobalVoiceBrainPointProducer();
            case "dataPrepare":
                return new DataPrepareBrainPointProducer();
            case "saleScale":
                return new SaleScaleBrainPointProducer();
            case "mathMatrix":
                return new MathMatrixBrainPointProducer();
            case "wordCloud":
                return new WordCloudBrainPointProducer();
            case "industrySaleScale":
                return new IndustrySaleScaleBrainPointProducer();
            case "highNetWorthClient":
                return new HighNetWorthClientBrainPointProducer();
            case "postCalculate":
                return new PostCalculateBrainPointProducer();
            case "lianJia":
                return new LianJiaBrainPointProducer();
            case "lianJiaHouseLayout":
                return new LianJiaHouseLayoutBrainPointProducer();
            case "cityHousePrice":
                return new CityHousePriceBrainPointProducer();
            case "cityPrimaryAndSecondaryMarkets":
                return new CityPrimaryAndSecondaryMarketsBrainPointProducer();
            case "districtPrimaryAndSecondaryMarkets":
                return new DistrictPrimaryAndSecondaryMarketsBrainPointProducer();
            case "salesTypeStat":
                return new SalesTypeStatBrainPointProducer();
            case "freeCalculate":
                return new FreeCalculateBrainPointProducer();
            case "surroundings":
                return new SurroundingsBrainPointProducer();
            case "lianJiaAlbum":
                return new LianJiaAlbumBrainPointProducer();
            case "label":
                return new LabelBrainPointProducer();
            case "conclusion":
                return new ConclusionBrainPointProducer();
            case "customStat":
                return new CustomStatBrainPointProducer();
        }
        return null;
    }

    protected void setNonStandardCustomers(CreativityParam param, String exp) {
        String[] customerNames = exp.split(";");
        this.nonStandardCustomers = new ArrayList<>();
        for (String name : customerNames) {
            if (StringUtils.equalsIgnoreCase(name, "customerName")) {
                this.nonStandardCustomers.add(param.getCustomerName());
            } else {
                this.nonStandardCustomers.add(name);
            }
        }
    }

    private void setSplit(CreativityParam param, String[] exps) {
        this.splitUnitDbIdList = new ArrayList<>();
        String exp = exps[1];

        if (exp.startsWith("property")) {
            // 物业
            setPropertySplit(param, exp);
        } else if (exp.startsWith(MsdType.MSD_SALES.getValue())) {
            // 销售神客
            setMsdSalesSplit(param, exp);
        } else if (exp.startsWith(MsdType.MSD_PROPERTY.getValue())) {
            // 物业神客
            setMsdPropertySplit(param, exp);
        } else if (StringUtils.containsIgnoreCase(exp, "industry")) {
            // 行业
            setIndustrySplit(param, exps);
        } else if (StringUtils.startsWith(exp, "cal_")) {
            // 后计算
            this.postCalculateType = exp;
        } else {
            // 地产
            setMainSplit(param, exp);
        }
    }

    private void setMainSplit(CreativityParam param, String exp) {
        this.orgCategoryId = param.getCustomerOrgCategoryId();
        String splitGroupId = null;

        StandardParam standardParam = param.getStandardParam();
        switch (exp) {
            case "main":
                Long unitId = param.unitId();
                if (null != unitId) {
                    this.splitUnitDbIdList.add(unitId);
                }
                return;
            case "group":
                splitGroupId = null== standardParam ? null : standardParam.getOrgGroupSplitId();
                break;
            case "area":
                splitGroupId = null== standardParam ? null : standardParam.getOrgAreaSplitId();
                break;
            case "company":
                splitGroupId = null== standardParam ? null : standardParam.getOrgCompanySplitId();
                break;
            case "city":
                splitGroupId = null== standardParam ? null : standardParam.getOrgCitySplitId();
                break;
            case "project":
                splitGroupId = null== standardParam ? null : standardParam.getOrgProjectSplitId();
                break;
            case "stage":
                splitGroupId = null== standardParam ? null : standardParam.getOrgStageSplitId();
                break;
        }

        addSplitUnitDbId(param, splitGroupId);
    }

    private void setPropertySplit(CreativityParam param, String exp) {
        if (param.getSplitMapping() == null)
            return;
        String splitUnitId = param.getSplitMapping().getPropertyUnitId();
        String splitGroupId = null;

        switch (exp) {
            case "propertyMain":
                SplitUnit splitUnit = param.getFullSplitStrIdMap().get(splitUnitId);
                if (splitUnit == null)
                    return;
                this.splitUnitDbIdList.add(splitUnit.get_id());

                String splitGroupID = splitUnit.getGroupID();
                SplitGroup splitGroup = param.getCommercialParam().findSplitGroup(splitGroupID);
                if (splitGroup == null)
                    return;
                this.orgCategoryId = splitGroup.getOrgCategoryID();
                return;
            case "propertyGroup":
            case "propertyArea":
            case "propertyCompany":
            case "propertyCity":
            case "propertyProject":
            case "propertyStage":
                String orgLevel = exp.replace("property", "").toLowerCase();
                splitGroupId = findSplitGroupIdBySplitUnitIdAndLevel(param, splitUnitId, orgLevel);
                break;
        }

        addSplitUnitDbId(param, splitGroupId);
    }

    private void setMsdSalesSplit(CreativityParam param, String exp) {
        if (param.getSplitMapping() == null)
            return;
        String splitUnitId = param.getSplitMapping().getMsdSalesUnitId();
        String splitGroupId = null;
        this.msdType = MsdType.MSD_SALES;

        switch (exp) {
            case "msdSalesMain":
                SplitUnit splitUnit = param.getFullSplitStrIdMap().get(splitUnitId);
                if (splitUnit == null)
                    return;
                this.splitUnitDbIdList.add(splitUnit.get_id());

                String splitGroupID = splitUnit.getGroupID();
                SplitGroup splitGroup = param.getCommercialParam().findSplitGroup(splitGroupID);
                if (splitGroup == null)
                    return;
                this.orgCategoryId = splitGroup.getOrgCategoryID();
                break;
            case "msdSalesGroup":
            case "msdSalesArea":
            case "msdSalesCompany":
            case "msdSalesCity":
            case "msdSalesProject":
            case "msdSalesStage":
                String orgLevel = exp.replace(MsdType.MSD_SALES.getValue(), "").toLowerCase();
                splitGroupId = findSplitGroupIdBySplitUnitIdAndLevel(param, splitUnitId, orgLevel);
                break;
        }

        addSplitUnitDbId(param, splitGroupId);
    }

    private void setMsdPropertySplit(CreativityParam param, String exp) {
        if (param.getSplitMapping() == null)
            return;
        String splitUnitId = param.getSplitMapping().getMsdPropertyUnitId();
        String splitGroupId = null;
        this.msdType = MsdType.MSD_PROPERTY;

        switch (exp) {
            case "msdPropertyMain":
                SplitUnit splitUnit = param.getFullSplitStrIdMap().get(splitUnitId);
                if (splitUnit == null)
                    return;
                this.splitUnitDbIdList.add(splitUnit.get_id());

                String splitGroupID = splitUnit.getGroupID();
                SplitGroup splitGroup = param.getCommercialParam().findSplitGroup(splitGroupID);
                if (splitGroup == null)
                    return;
                this.orgCategoryId = splitGroup.getOrgCategoryID();
                break;
            case "msdPropertyGroup":
            case "msdPropertyArea":
            case "msdPropertyCompany":
            case "msdPropertyCity":
            case "msdPropertyProject":
            case "msdPropertyStage":
                String orgLevel = exp.replace("msdProperty", "").toLowerCase();
                splitGroupId = findSplitGroupIdBySplitUnitIdAndLevel(param, splitUnitId, orgLevel);
                break;
        }

        addSplitUnitDbId(param, splitGroupId);
    }

    private void addSplitUnitDbId(CreativityParam param, String splitGroupId) {
        if (StringUtils.isBlank(splitGroupId))
            return;

        CommercialParam commercialParam = param.getCommercialParam();
        AbstractGrouping grouping = commercialParam.findSplitGrouping(splitGroupId);
        if (null == grouping) {
            log.error("没有找到 Grouping，splitGroupId={} ", splitGroupId);
            return;
        }

        if (grouping instanceof LegacyOrgGrouping) {
            LegacyOrgGrouping orgGrouping = (LegacyOrgGrouping) grouping;
            orgGrouping.getLegacyUnitList().forEach(x -> this.splitUnitDbIdList.add(x.get_id()));
        }
        if (grouping instanceof LegacyIndustryGrouping) {
            LegacyIndustryGrouping allianceGrouping = (LegacyIndustryGrouping) grouping;
            allianceGrouping.getLegacyAllianceUnitList().forEach(x -> this.splitUnitDbIdList.add(x.get_id()));
        }

        // 迁移到SuperBrain时注释掉
//        if (param.isSplitIncludeAllList() && param.getSplitIncludeGroupIdList().contains(splitGroupId)) {
//            AbstractGrouping grouping = param.getCommercialParam().findSplitGrouping(splitGroupId);
//            if (grouping == null)
//                throw new IllegalStateException("visual include split all grouping name is wrong " + splitGroupId);
//            if (grouping instanceof LegacyOrgGrouping) {
//                LegacyOrgGrouping orgGrouping = (LegacyOrgGrouping) grouping;
//                orgGrouping.getLegacyUnitList().stream().forEach(x -> this.splitUnitDbIdList.add(x.get_id()));
//            }
//            else {
//                throw new IllegalStateException("visual include split all grouping only support legacy grouping now " + splitGroupId);
//            }
//        }
//        else {
//            AbstractViewPointDataMatrixInstance abstractInstance = param.getAbstractViewPointDataMatrixInstance();
//            if (abstractInstance instanceof ViewPointDataMatrixInstance) {
//                ViewPointDataMatrixInstance instance = (ViewPointDataMatrixInstance) abstractInstance;
//
//                List<SplitUnit> splitUnits = instance.getUnitList();
//                if (splitUnits != null && !splitUnits.isEmpty()) {
//                    splitUnits.stream()
//                            .filter(x -> StringUtils.equalsIgnoreCase(x.getGroupID(), splitGroupId))
//                            .forEach(x -> this.splitUnitDbIdList.add(x.get_id()));
//                }
//
//                List<AllianceSplitUnit> allianceSplitUnits = instance.getAllianceUnitList();
//                if (allianceSplitUnits != null && !allianceSplitUnits.isEmpty()) {
//                    allianceSplitUnits.stream()
//                            .filter(x -> StringUtils.equalsIgnoreCase(x.getGroupID(), splitGroupId))
//                            .forEach(x -> this.splitUnitDbIdList.add(x.get_id()));
//                }
//            }
//        }
    }

    public String findSplitGroupIdBySplitUnitIdAndLevel(CreativityParam param, String splitUnitId, String orgLevel) {
        // 与YT-Intelligence不一致... 配置了多个标准参数，通过组织结构分类ID找匹配的"
        StandardParam standardParam = param.getStandardParam();
//        String orgCategoryID = findOrgCategoryIdBySplitUnitId(param, splitUnitId);
//        List<StandardParam> standardParams = param.getCommercialParam().getStandardParams();
//        StandardParam standardParam = standardParams.stream()
//                .filter(x -> StringUtils.equalsIgnoreCase(orgCategoryID, x.getCustomerOrgCategoryId()))
//                .findAny()
//                .orElseThrow(() -> new IllegalStateException("没有配置组织结构分类的标准参数，orgCategoryID=" + orgCategoryID));

        switch (orgLevel) {
            case "group":
                return standardParam.getOrgGroupSplitId();
            case "area":
                return standardParam.getOrgAreaSplitId();
            case "company":
                return standardParam.getOrgCompanySplitId();
            case "city":
                return standardParam.getOrgCitySplitId();
            case "project":
                return standardParam.getOrgProjectSplitId();
            case "stage":
                return standardParam.getOrgStageSplitId();
        }
        return null;
    }

    /*private String findOrgCategoryIdBySplitUnitId(CreativityParam param, String splitUnitId) {
        if (StringUtils.isNotBlank(this.orgCategoryId)) {
            return this.orgCategoryId;
        }

        SplitUnit splitUnit = param.getCommercialParam().getFullSplitStrIdMap().get(splitUnitId);
        if (splitUnit == null)
            return null;
        String splitGroupID = splitUnit.getGroupID();

        SplitGroup splitGroup = param.getCommercialParam().findSplitGroup(splitGroupID);
        if (splitGroup == null)
            return null;

        String orgCategoryID = splitGroup.getOrgCategoryID();
        this.orgCategoryId = orgCategoryID;
        return orgCategoryID;
    }*/

    private void setIndustrySplit(CreativityParam param, String[] exps) {
        String exp = exps[1];
        isIndustry = true;
        String splitGroupId = null;

        switch (exp) {
            case "industryAverage":
                this.splitUnitDbIdList.add(-10101L);
                break;
            case "industryBenchmark":
                this.splitUnitDbIdList.add(-20101L);
                break;
            case "industryCity":
                // 行业城市
                splitGroupId = "47A16422-075B-4ECD-AF29-B028B0151696";
                break;
            case "customerIndustryCity":
                // 开发商+行业城市
                splitGroupId = "77BF8533-38B5-47F0-B2AE-B04D2E96215F";
                break;
            case "industryCustomer":
                // 行业全体开发商
                splitGroupId = "E4683A26-AAE1-436B-A75E-C2E1F1684144";
                break;
            case "industryDistrict":
                // 各区总体，如：广州市-天河区
                splitGroupId = "2377E48D-C62F-4085-B5DB-80E7D9AEA1EB";
                break;
            case "industryFreeSplitGroupId":
                // 行业自由切分组id
                Long _id = Long.parseLong(exps[2]);
                AllianceSplitGroup allianceSplitGroup = param.getKanbanFramework().getCommandParam().getAllianceSplitGroupRepository().findBy_id(_id).get(0);
                splitGroupId = allianceSplitGroup.getId();
                break;
            case "industryFreeSplitUnitId":
                // 行业自由切分单元id 136#top11-20
                if (!exps[2].contains("#"))
                    return;
                long groupDbId = Long.parseLong(exps[2].split("#")[0]);
                String unitName = exps[2].split("#")[1];
                this.splitUnitDbIdList.add(IdGenerator.generateUnitDbIdByMurmur64(groupDbId, unitName));
                break;
            case "industryPercent10":
            case "industryPercent15":
            case "industryPercent20":
            case "industryPercent25":
            case "industryPercent30":
            case "industryPercent35":
            case "industryPercent40":
            case "industryPercent45":
            case "industryPercent50":
            case "industryPercent55":
            case "industryPercent60":
            case "industryPercent65":
            case "industryPercent70":
            case "industryPercent75":
            case "industryPercent80":
            case "industryPercent85":
            case "industryPercent90":
            case "industryPercent95":
                String percent = exp.replace("industryPercent", "");
                String id = "-301" + percent;
                this.splitUnitDbIdList.add(Long.parseLong(id));
                break;
        }

        addSplitUnitDbId(param, splitGroupId);
    }

    private void setInteractionPlanarCodeAndIndexCode(CreativityParam param, String anExpression) {
        String[] exps = anExpression.split(":");
        if (exps.length < 2) {
            String message = "wrong format : " + anExpression;
            log.error("{}", message);
            throw new IllegalStateException(message);
        }
        String interactionType = exps[1];

        switch (interactionType) {
            case "satis":
                setCode(param, exps, "满意度");
                break;
            case "ms":
                setCode(param, exps, "神客");
                break;
            case "text":
                setTextCode(param, exps, "openText");
                break;
            case "assembly":
                setAssemblyCode(param, exps);
                break;
            case "node":
                setCodeByNode(param);
                break;
        }
    }

    private void setCode(CreativityParam param, String[] exps, String category) {
        this.indexCodeList = new ArrayList<>();
        this.idAndNetInteractionMap = new HashMap<>();

        switch (exps[2]) {
            case "code":
                // interaction:satis:code:201002
                String[] planarCodes = exps[3].split("/");
                List<NetInteraction> netInteractions = param.getKanbanFramework().getCommandParam().getNetInteractionRepository()
                        .findByCategoryAndPlanarIndexCodes(category, Arrays.asList(planarCodes));
                netInteractions.forEach(x -> {
                    addIndexCode(x);
                    this.idAndNetInteractionMap.put(x.getId(), x);
                });
                break;
            case "parentAndChild":
                // interaction:satis:parentAndChild:201002
                String parentAndChildCode = exps[3];
                if (parentAndChildCode.equalsIgnoreCase("node")) {
                    if (param.getAbstractViewPointDataMatrixInstance() instanceof ViewPointDataMatrixInstance) {
                        ViewPointDataMatrixInstance instance = (ViewPointDataMatrixInstance) param.getAbstractViewPointDataMatrixInstance();

                        CommercialParam commercialParam = param.getCommercialParam();
                        IndexGrid indexGrid = commercialParam.getIndexCodeGridMap().get(instance.getIndices()[0]);
                        parentAndChildCode = indexGrid.getPlanarIndexCode();
                    }
                }
                NetInteraction interaction = getNetInteractionByPlanarCode(param, category, parentAndChildCode);
                if (interaction != null) {
                    addIndexCode(interaction);
                    this.idAndNetInteractionMap.put(interaction.getId(), interaction);

                    List<NetInteraction> childInteractions = param.getKanbanFramework().getCommandParam().getNetInteractionRepository()
                            .searchByCategoryAndParent(category, interaction.getName());
                    if (childInteractions != null && childInteractions.size() > 0) {
                        childInteractions.forEach(x -> {
                            addIndexCode(x);
                            this.idAndNetInteractionMap.put(x.getId(), x);
                        });
                    }
                }
                break;
            case "child":
                // interaction:satis:child:201002
                parentAndChildCode = exps[3];
                if (parentAndChildCode.equalsIgnoreCase("node")) {
                    if (param.getAbstractViewPointDataMatrixInstance() instanceof ViewPointDataMatrixInstance) {
                        ViewPointDataMatrixInstance instance = (ViewPointDataMatrixInstance) param.getAbstractViewPointDataMatrixInstance();
                        CommercialParam commercialParam = param.getCommercialParam();
                        IndexGrid indexGrid = commercialParam.getIndexCodeGridMap().get(instance.getIndices()[0]);
                        parentAndChildCode = indexGrid.getPlanarIndexCode();
                    }
                }
                interaction = getNetInteractionByPlanarCode(param, category, parentAndChildCode);
                if (interaction != null) {
                    List<NetInteraction> childInteractions = param.getKanbanFramework().getCommandParam().getNetInteractionRepository()
                            .searchByCategoryAndParent(category, interaction.getName());
                    if (childInteractions != null && childInteractions.size() > 0) {
                        childInteractions.forEach(x -> {
                            addIndexCode(x);
                            this.idAndNetInteractionMap.put(x.getId(), x);
                        });
                    }
                }
                break;
            case "netId":
                // interaction:satis:netId:38
                long net_id = Long.parseLong(exps[3]);
                List<NetNet> nets = param.getKanbanFramework().getCommandParam().getNetNetRepository().findBy_id(net_id);
                if (nets != null && nets.size() > 0) {
                    NetNet net = nets.get(0);
                    String ngql = String.format("match (v:interaction) where v.netId=='%s' and v.interactionType ends with '%s' return v.interactionId;"
                            , net.getId(), category);
                    List<HashMap<String, Object>> dataList = param.getKanbanFramework().getCommandParam().getNebulaLoader().loadNebulaDataList(ngql, new String[]{"id"});
                    List<String> idList = new ArrayList<>();
                    dataList.forEach(x -> idList.add(x.get("id") + ""));
                    List<NetInteraction> interactions = param.getKanbanFramework().getCommandParam().getNetInteractionRepository().findByIdList(idList);
                    if (interactions != null && interactions.size() > 0) {
                        interactions.forEach(x -> {
                            addIndexCode(x);
                            this.idAndNetInteractionMap.put(x.getId(), x);
                        });
                        param.setInteractions(interactions);
                    }
                }
                break;
        }
    }

    private NetInteraction getNetInteractionByPlanarCode(CreativityParam param, String category, String planarCode) {
        List<NetInteraction> netInteractions = param.getKanbanFramework().getCommandParam().getNetInteractionRepository()
                .findByPlanarIndexCodeAndCategory(planarCode, category);

        if (netInteractions == null || netInteractions.size() == 0)
            return null;

        return netInteractions.get(0);
    }

    private void addIndexCode(NetInteraction interaction) {
        String indexCode = interaction.getCode();
        if (StringUtils.isBlank(indexCode)) {
            String type = StringUtils.equals(interaction.getCategory(), "满意度") ? "SATIS" : "MS";
            this.indexCodeList.add(type + "_" + interaction.getPlanarIndexCode());
            return;
        }

        if (StringUtils.equalsIgnoreCase(indexCode, "ts")) {
            // 区分神客销售总分、物业总分
            this.indexCodeList.add(indexCode + "_" + interaction.getPlanarIndexCode());
        } else {
            this.indexCodeList.add(indexCode);
        }
    }

    private void setTextCode(CreativityParam param, String[] exps, String category) {
        this.planarCodeAndGraphInteractionMap = new HashMap<>();

        switch (exps[2]) {
            case "code":
                // interaction:text:code:101
                String[] planarCodes = exps[3].split("/");
                List<GraphInteraction> graphInteractions = param.getKanbanFramework().getCommandParam().getGraphInteractionRepository()
                        .findByOpenTypeAndCodes(category, Arrays.asList(planarCodes));
                graphInteractions.forEach(x -> this.planarCodeAndGraphInteractionMap.put(x.getCode(), x));
                break;
            case "parentAndChild":
                // interaction:text:parentAndChild:101
                GraphInteraction interaction = getGraphInteractionByPlanarCode(param, category, exps[3]);
                if (interaction != null) {
                    this.planarCodeAndGraphInteractionMap.put(interaction.getCode(), interaction);

                    List<GraphInteraction> childInteractions = param.getKanbanFramework().getCommandParam().getGraphInteractionRepository()
                            .searchByOpenTypeAndBelongTo(category, interaction.getName());
                    if (childInteractions != null && childInteractions.size() > 0) {
                        childInteractions.forEach(x -> this.planarCodeAndGraphInteractionMap.put(x.getCode(), x));
                    }
                }
                break;
            case "child":
                // interaction:text:child:101
                interaction = getGraphInteractionByPlanarCode(param, category, exps[3]);
                if (interaction != null) {
                    List<GraphInteraction> childInteractions = param.getKanbanFramework().getCommandParam().getGraphInteractionRepository()
                            .searchByOpenTypeAndBelongTo(category, interaction.getName());
                    if (childInteractions != null && childInteractions.size() > 0) {
                        childInteractions.forEach(x -> this.planarCodeAndGraphInteractionMap.put(x.getCode(), x));
                    }
                }
                break;
            case "netId":
                // interaction:text:netId:38
                long net_id = Long.parseLong(exps[3]);
                List<NetNet> nets = param.getKanbanFramework().getCommandParam().getNetNetRepository().findBy_id(net_id);
                if (nets != null && nets.size() > 0) {
                    NetNet net = nets.get(0);
                    String ngql = String.format("match (v:interaction) where v.netId=='%s' and v.interactionType ends with '%s' return v.interactionId;"
                            , net.getId(), category);
                    List<HashMap<String, Object>> dataList = param.getKanbanFramework().getCommandParam().getNebulaLoader().loadNebulaDataList(ngql, new String[]{"id"});
                    List<String> idList = new ArrayList<>();
                    dataList.forEach(x -> idList.add(x.get("id") + ""));
                    List<GraphInteraction> interactions = param.getKanbanFramework().getCommandParam().getGraphInteractionRepository().findByIdList(idList);
                    if (interactions != null && interactions.size() > 0) {
                        interactions.forEach(x -> this.planarCodeAndGraphInteractionMap.put(x.getCode(), x));
                    }
                    if (this.expression.contains("reorg")) {
                        // 需要保存文本触点的父子关系
                        NebulaGraph nebulaGraph = param.getKanbanFramework().getCommandParam().getGraphEditApplication().produceNetGraph(net.getId());
                        if (nebulaGraph != null) {
                            HashMap<String, String> interactionMap = new HashMap<>();
                            for (GraphEdge edge : nebulaGraph.getEdgeList()) {
                                if (edge.getEdgeName().equals("parent")) {
                                    String srcId = "" + nebulaGraph.getVertexMap().get(edge.getSrcVid()).get("interactionId");
                                    String distId = "" + nebulaGraph.getVertexMap().get(edge.getDstVid()).get("interactionId"); // parent
                                    interactionMap.put(srcId, distId);
                                }
                            }
                            param.setInteractionMap(interactionMap);
                        }
                    }
                }
                break;
        }
    }

    private GraphInteraction getGraphInteractionByPlanarCode(CreativityParam param, String category, String planarCode) {
        List<GraphInteraction> graphInteractions = param.getKanbanFramework().getCommandParam().getGraphInteractionRepository()
                .findByOpenTypeAndCodes(category, Collections.singletonList(planarCode));

        if (graphInteractions == null || graphInteractions.size() == 0)
            return null;

        return graphInteractions.get(0);
    }

    private void setAssemblyCode(CreativityParam param, String[] exps) {
        this.planarCodeAndAssemblyMap = new HashMap<>();

        switch (exps[2]) {
            case "code":
                // interaction:assembly:code:302101001
                String[] planarCodes = exps[3].split("/");
                List<NetInteractionAssembly> interactionList = param.getKanbanFramework().getCommandParam().getNetInteractionAssemblyRepository()
                        .findByPlanarIndexCodes(Arrays.asList(planarCodes));
                interactionList.forEach(x -> this.planarCodeAndAssemblyMap.put(x.getPlanarIndexCode(), x));
                break;
            case "parentAndChild":
                // interaction:assembly:parentAndChild:302101001
                NetInteractionAssembly interaction = getAssemblyByPlanarCode(param, exps[3]);
                if (interaction != null) {
                    this.planarCodeAndAssemblyMap.put(interaction.getPlanarIndexCode(), interaction);

                    List<NetInteractionAssembly> children = param.getKanbanFramework().getCommandParam().getNetInteractionAssemblyRepository().searchByParent(interaction.getName());
                    if (children != null && children.size() > 0) {
                        children.forEach(x -> this.planarCodeAndAssemblyMap.put(x.getPlanarIndexCode(), x));
                    }
                }
                break;
            case "child":
                // interaction:assembly:child:302101001
                interaction = getAssemblyByPlanarCode(param, exps[3]);
                if (interaction != null) {
                    List<NetInteractionAssembly> children = param.getKanbanFramework().getCommandParam().getNetInteractionAssemblyRepository().searchByParent(interaction.getName());
                    if (children != null && children.size() > 0) {
                        children.forEach(x -> this.planarCodeAndAssemblyMap.put(x.getPlanarIndexCode(), x));
                    }
                }
                break;
            case "netId":
                // interaction:assembly:netId:38
                long net_id = Long.parseLong(exps[3]);
                List<NetNet> nets = param.getKanbanFramework().getCommandParam().getNetNetRepository().findBy_id(net_id);
                if (nets != null && nets.size() > 0) {
                    NetNet net = nets.get(0);
                    String ngql = String.format("match (v:interaction) where v.netId=='%s' and v.interactionType ends with '%s' return v.interactionId;"
                            , net.getId(), "组合触点");
                    List<HashMap<String, Object>> dataList = param.getKanbanFramework().getCommandParam().getNebulaLoader().loadNebulaDataList(ngql, new String[]{"id"});
                    List<String> idList = new ArrayList<>();
                    dataList.forEach(x -> idList.add(x.get("id") + ""));
                    List<NetInteractionAssembly> interactions = param.getKanbanFramework().getCommandParam().getNetInteractionAssemblyRepository().findByIdList(idList);
                    if (interactions != null && interactions.size() > 0) {
                        interactions.forEach(x -> this.planarCodeAndAssemblyMap.put(x.getPlanarIndexCode(), x));
                    }
                }
                break;
        }
    }

    private NetInteractionAssembly getAssemblyByPlanarCode(CreativityParam param, String planarCode) {
        List<NetInteractionAssembly> interactions = param.getKanbanFramework().getCommandParam().getNetInteractionAssemblyRepository().findByPlanarIndexCode(planarCode);
        if (interactions == null || interactions.size() == 0)
            return null;
        return interactions.get(0);
    }

    private void setCodeByNode(CreativityParam param) {
        this.indexCodeList = new ArrayList<>();
        this.idAndNetInteractionMap = new HashMap<>();
        this.planarCodeAndGraphInteractionMap = new HashMap<>();

        AbstractViewPointDataMatrixInstance abstractInstance = param.getAbstractViewPointDataMatrixInstance();
        CommercialParam commercialParam = param.getCommercialParam();
        if (commercialParam == null)
            return;

        if (abstractInstance instanceof ViewPointDataMatrixInstance) {
            ViewPointDataMatrixInstance instance = (ViewPointDataMatrixInstance) abstractInstance;
            String[] indices = instance.getIndices();
            if (indices == null || indices.length == 0)
                return;

            for (String exp : indices) {
                if (exp.startsWith("text_")) {
                    // 文本
                    String planarCode = exp.replace("text_", "");
                    GraphInteraction interaction = commercialParam.getPlanarCodeAndGraphInteractionMap().get(planarCode);
                    if (interaction == null)
                        continue;
                    this.planarCodeAndGraphInteractionMap.put(interaction.getCode(), interaction);

                } else {
                    // 满意度、神客
                    NetInteraction interaction;
                    if (exp.startsWith("ts_")) {
                        String[] expParts = exp.split("_");
                        interaction = commercialParam.getIdAndNetInteractionMap().values().stream()
                                .filter(x -> StringUtils.equalsIgnoreCase(x.getCode(), expParts[0]))
                                .filter(x -> StringUtils.equalsIgnoreCase(x.getPlanarIndexCode(), expParts[1]))
                                .findAny()
                                .orElse(null);
                    } else {
                        interaction = commercialParam.getIdAndNetInteractionMap().values().stream()
                                .filter(x -> StringUtils.equalsIgnoreCase(x.getCode(), exp))
                                .findAny()
                                .orElse(null);
                    }
                    if (interaction == null)
                        continue;
                    addIndexCode(interaction);
                    this.idAndNetInteractionMap.put(interaction.getId(), interaction);
                }
            }
        }
    }

    private void addAttributeList(CreativityParam param, String exp) {
        this.attributeList = new ArrayList<>();
        List<String> attributeConditions = matchText(p_squareBrackets, exp);

        for (String condition : attributeConditions) {
            condition = condition.replaceAll("[\\[\\]]", "");

            if (condition.contains(";")) {
                String[] attributeArray = condition.split(";");
                for (int i = 0; i < attributeArray.length; i++) {
                    String attribute = attributeArray[i];
                    StandardAttributeEnum attributeEnum = StandardAttributeEnum.getInstance(attribute);
                    if (attributeEnum != null) {
                        attributeArray[i] = attributeEnum.getPureOptionName();
                    }
                }
                this.attributeList.add(attributeArray);

            } else {
                List<String> attributes = new ArrayList<>();
                switch (condition) {
                    case "NONE":
                        attributes.add("总体");
                        break;
                    case "OWNER_FOUR":
                        attributes.addAll(Arrays.asList(OWNER_FOUR_ARRAY));
                        break;
                    case "OWNER_SEVEN":
                        attributes.addAll(Arrays.asList(OWNER_SEVEN_ARRAY));
                        break;
                    case "HOUSE":
                        attributes.addAll(Arrays.asList(HOUSE_TYPE_ARRAY));
                        break;
                    case "DECORATION":
                        attributes.addAll(Arrays.asList(DECORATION_TYPE_ARRAY));
                        break;
                    case "PROPERTYCOSTLEVEL":
                        attributes.addAll(Arrays.asList(PROPERTYCOSTLEVEL_ARRAY));
                        break;
                    default:
                        StandardAttributeEnum attributeEnum = StandardAttributeEnum.getInstance(condition);
                        if (attributeEnum != null) {
                            attributes.add(attributeEnum.getPureOptionName());
                        }
                        break;
                }
                attributes.forEach(x -> this.attributeList.add(new String[]{x}));
            }
        }
    }

    protected void filterIndexCodeByIndexLevel(CreativityParam param, String exp) {
        // 必须先有indexCodeList，再过滤indexLevel
        if (this.indexCodeList == null || this.indexCodeList.size() == 0)
            return;

        CommercialParam commercialParam = param.getCommercialParam();
        if (commercialParam == null)
            return;

        List<String> indexLevelList = Arrays.asList(exp.split("/"));
        List<String> newIndexCodeList = new ArrayList<>();
        for (String indexCode : this.indexCodeList) {
            IndexGrid indexGrid = commercialParam.getIndexCodeGridMap().get(indexCode);
            if (indexGrid == null)
                continue;
            Integer indexLevel = indexGrid.getIndexLevel();
            if (!indexLevelList.contains(indexLevel + ""))
                continue;
            newIndexCodeList.add(indexCode);
        }
        this.indexCodeList = newIndexCodeList;
    }

    private void relatedInteraction(CreativityParam param, String exp) {
        // 迁移到SuperBrain时注释掉 看板中没有商业矩阵
        log.error("迁移到SuperBrain时注释掉，看板中没有商业矩阵");
//        CommercialMatrixInstance commercialMatrixInstance = param.getCommercialMatrixInstance();
//        if (commercialMatrixInstance.getNodes() == null || commercialMatrixInstance.getLinks() == null)
//            return;
//
//        List<CommercialMatrixNode> nodes = null;
//        if (this.indexCodeList != null && !this.indexCodeList.isEmpty()) {
//            nodes = commercialMatrixInstance.getNodes().stream()
//                    .filter(x -> this.indexCodeList.contains(x.getData().getIndexFission()))
//                    .collect(Collectors.toList());
//        }
//        if (this.planarCodeAndGraphInteractionMap != null && !this.planarCodeAndGraphInteractionMap.isEmpty()) {
//            nodes = commercialMatrixInstance.getNodes().stream()
//                    .filter(x -> this.planarCodeAndGraphInteractionMap.containsKey(x.getData().getIndexFission().replace("text_", "")))
//                    .collect(Collectors.toList());
//        }
//
//        if (nodes == null || nodes.isEmpty())
//            return;
//        List<String> nodeIds = nodes.stream().map(CommercialMatrixNode::getId).collect(Collectors.toList());
//
//        List<CommercialMatrixLink> links = commercialMatrixInstance.getLinks().stream()
//                .filter(x -> StringUtils.equals(x.getText(), "相关"))
//                .filter(x -> nodeIds.contains(x.getFrom()) || nodeIds.contains(x.getTo()))
//                .collect(Collectors.toList());
//
//        List<String> relatedNodeIds = new ArrayList<>();
//        for (CommercialMatrixLink link : links) {
//            if (!nodeIds.contains(link.getFrom())) {
//                relatedNodeIds.add(link.getFrom());
//            }
//            if (!nodeIds.contains(link.getTo())) {
//                relatedNodeIds.add(link.getTo());
//            }
//        }
//
//        List<CommercialMatrixNode> relatedNodes = commercialMatrixInstance.getNodes().stream()
//                .filter(x -> relatedNodeIds.contains(x.getId()))
//                .collect(Collectors.toList());
//
//        switch (exp) {
//            case "satis":
//            case "ms":
//                List<String> newIndexCodeList = new ArrayList<>();
//                relatedNodes.forEach(x -> newIndexCodeList.add(x.getData().getIndexFission()));
//                this.indexCodeList = newIndexCodeList;
//                break;
//            case "text":
//                Map<String, GraphInteraction> newMap = new HashMap<>();
//                relatedNodes.forEach(x -> {
//                    String planarCode = x.getData().getIndexFission().replace("text_", "");
//                    GraphInteraction graphInteraction = param.getPlanarCodeAndGraphInteractionMap().get(planarCode);
//                    if (graphInteraction != null)
//                        newMap.put(planarCode, graphInteraction);
//                });
//                this.planarCodeAndGraphInteractionMap = newMap;
//                break;
//        }
    }

    private void setPeriod(CreativityParam param, String exp) {
        this.periods = new ArrayList<>();

        // period:current/part;longDataPeriods;1
        String[] expParts = exp.split("/");

        for (String part : expParts) {
            String[] partSplits = part.split(";");
            String period = null;
            switch (partSplits[0]) {
                case "part":
                    if (partSplits.length == 3) {
                        int index = Integer.parseInt(partSplits[2]);
                        String _period = getPeriod(param, partSplits[1]);
                        String[] _periods = _period.split(",");
                        if (_periods.length >= index) {
                            period = _periods[index - 1];
                        }
                    }
                    break;
                default:
                    period = getPeriod(param, partSplits[0]);
                    break;
            }
            if (StringUtils.isNotBlank(period))
                this.periods.addAll(Arrays.asList(period.split(",")));
        }
    }

    private String getPeriod(CreativityParam param, String periodExp) {
        String period = null;
        switch (periodExp) {
            case "current":
                if (isIndustry && StringUtils.isNotBlank(param.getIndustryMainPeriod())) {
                    period = param.getIndustryMainPeriod();
                } else {
                    period = param.latestDataPeriod();
                }
                break;
            case "previous":
                if (isIndustry && StringUtils.isNotBlank(param.getIndustryPrePeriod())) {
                    period = param.getIndustryPrePeriod();
                } else {
                    period = param.previousDataPeriod();
                }
                break;
            case "shortDataPeriods":
                period = param.getDataPeriods();
                break;
            case "longDataPeriods":
                period = param.getLongDataPeriods();
                break;
            case "quarterDataPeriods":
                period = param.getQuarterDataPeriods();
                break;
            case "monthDataPeriods":
                period = param.getMonthDataPeriods();
                break;
            case "thisYearAccumulation":
                period = param.getThisYearAccumulation();
                break;
            case "thisYearFirstHalf":
                period = param.getThisYearFirstHalf();
                break;
            case "thisYearSecondHalf":
                period = param.getThisYearSecondHalf();
                break;
            case "lastYearAccumulation":
                period = param.getLastYearAccumulation();
                break;
            case "lastYearFirstHalf":
                period = param.getLastYearFirstHalf();
                break;
            case "lastYearSecondHalf":
                period = param.getLastYearSecondHalf();
                break;
            case "lastYearEachSeason":
                period = param.getLastYearEachSeason();
                break;
            case "lastYearEachMonth":
                period = param.getLastYearEachMonth();
                break;
            case "lastYearSamePeriod":
                period = param.getLastYearSamePeriod();
                break;
            case "thisSeason":
                period = param.getThisSeason();
                break;
            case "lastSeason":
                period = param.getLastSeason();
                break;
            case "thisSeasonEachMonth":
                period = param.getThisSeasonEachMonth();
                break;
            case "lastSeasonEachMonth":
                period = param.getLastSeasonEachMonth();
                break;
            case "thisMonth":
                period = param.getThisMonth();
                break;
            case "lastMonth":
                period = param.getLastMonth();
                break;
            case "thisMonthAccumulation":
                period = param.getThisMonthAccumulation();
                break;
            case "lastMonthAccumulation":
                period = param.getLastMonthAccumulation();
                break;
            case "thisYearEachMonthAccu":
                period = param.getThisYearEachMonthAccu();
                break;
            case "lastYearEachMonthAccu":
                period = param.getLastYearEachMonthAccu();
                break;
        }
        return period;
    }

    protected void setPostProcessList(String exp) {
        if (this.postProcessList == null)
            this.postProcessList = new ArrayList<>();

        List<String> postProcessExps = matchText(p_squareBrackets, exp);
        for (String postProcessExp : postProcessExps) {
            String process = postProcessExp.replaceAll("[\\[\\]]", "");
            this.postProcessList.add(process);
        }
    }

    protected void projectOfProductLabel(CreativityParam param, String exp) {
        if (this.splitUnitDbIdList == null || this.splitUnitDbIdList.isEmpty())
            return;

        Set<String> projectNames = param.getCommercialParam().getProductLabelAndProjectNamesMap().get(exp);
        if (projectNames == null) {
            HashMap<String, String> categoryIdAndCustomerNameMap = param.getCommercialParam().getCategoryIdAndCustomerNameMap();
            if (categoryIdAndCustomerNameMap.isEmpty()) {
                List<OrgCategory> orgCategoryList = param.getKanbanFramework().getCommandParam().getOrgCategoryRepository().findAllFormal();
                List<Customer> customerList = param.getKanbanFramework().getCommandParam().getCustomerRepository().findAllFormal();
                orgCategoryList.forEach(org ->
                        customerList.stream()
                                .filter(cus -> StringUtils.equals(cus.getId(), org.getCustomerId()))
                                .findAny()
                                .ifPresent(cus -> categoryIdAndCustomerNameMap.put(org.getId(), LianjiaSignal.transferLianJiaCustomerName(cus.getName()))));
            }

            projectNames = new HashSet<>();
            List<OrgUnit> orgUnits = param.getKanbanFramework().getCommandParam().getOrgUnitRepository().findByProductLabel(exp);
            for (OrgUnit orgUnit : orgUnits) {
                int count = 0;
                // 添加组织结构最后两层
                String orgCategoryId = orgUnit.getOrgCategoryId().getValue();
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg8());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg7());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg6());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg5());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg4());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg3());
                count = addProjectName(param, projectNames, count, orgCategoryId, orgUnit.getOrg2());
            }
            param.getCommercialParam().getProductLabelAndProjectNamesMap().put(exp, projectNames);
        }

        List<Long> list = new ArrayList<>();
        for (Long splitUnitDbId : this.splitUnitDbIdList) {
            // 迁移到SuperBrain时注释掉
            log.error("迁移到SuperBrain时注释掉，应该从神经元网络中找切分");
//            FeatureSplitUnit splitUnit = param.getCommercialParam().getFullAllianceSplitMap().get(splitUnitDbId + "");
//            if (splitUnit == null)
//                continue;
//            if (projectNames.contains(splitUnit.name())) {
//                list.add(splitUnitDbId);
//            }
        }
        this.splitUnitDbIdList = list;
    }

    private int addProjectName(CreativityParam param,
                               Set<String> projectNames,
                               int count,
                               String orgCategoryId,
                               String name) {
        if (count >= 2 || StringUtils.isBlank(name))
            return count;

        count++;
        if (isProjectName(name)) {
            String customerName = param.getCommercialParam().getCategoryIdAndCustomerNameMap().get(orgCategoryId);
            projectNames.add(customerName + "_" + name);
        }
        return count;
    }

    private boolean isProjectName(String name) {
        if (StringUtils.isBlank(name))
            return false;
        String[] exceptWords = {"区域", "公司", "市", "期"};
        long count = Arrays.stream(exceptWords).filter(x -> StringUtils.contains(name, x)).count();
        return count == 0;
    }

    protected void filterSplit(CreativityParam param, String[] exps) {
        if (this.splitUnitDbIdList == null || this.splitUnitDbIdList.isEmpty())
            return;

        List<String> mustContainStrList = new ArrayList<>();
        switch (exps[1]) {
            case "customerName":
                mustContainStrList.add(param.transformCustomerName());
                break;
            case "currentSplitName":
                mustContainStrList.add(param.getCurrentUserSplit().name());
                break;
            case "city":
                mustContainStrList.addAll(filterSplitByCity(param, exps[2]));
                break;
            default:
                mustContainStrList.addAll(Arrays.asList(exps[1].split("/")));
                break;
        }

        List<Long> list = new ArrayList<>();
        for (Long splitUnitDbId : this.splitUnitDbIdList) {
            log.error("迁移到SuperBrain时注释掉，应该从神经元网络中找切分");
            /*FeatureSplitUnit splitUnit = param.getCommercialParam().getFullSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                splitUnit = param.getCommercialParam().getFullAllianceSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                continue;
            String splitName = splitUnit.name();

            for (String mustContainStr : mustContainStrList) {
                if (StringUtils.contains(mustContainStr, "_") || StringUtils.contains(mustContainStr, "-")
                        || StringUtils.contains(mustContainStr, ",") || StringUtils.contains(mustContainStr, "(")) {
                    if (StringUtils.contains(splitName, mustContainStr)) {
                        list.add(splitUnitDbId);
                        break;
                    }
                } else {
                    String[] parts = splitName.split("[(,_-]");
                    if (Arrays.asList(parts).contains(mustContainStr)) {
                        list.add(splitUnitDbId);
                        break;
                    }
                }
            }*/
        }
        this.splitUnitDbIdList = list;
    }

    protected List<String> filterSplitByCity(CreativityParam param, String exp) {
        List<String> result = new ArrayList<>();
        List<String> cityList = new ArrayList<>();

        if (StringUtils.equals(exp, "currentOrgCity")) {
            String orgUnitId = param.getCurrentUserSplit().splitUnitId();
            if (StringUtils.isBlank(orgUnitId))
                return result;
            OrgUnit orgUnit = param.getKanbanFramework().getCommandParam().getOrgUnitRepository().findById(orgUnitId).orElse(null);
            if (orgUnit == null)
                return result;
            cityList.add(orgUnit.getCity());
        } else {
            cityList = Arrays.asList(exp.split("/"));
        }

        for (Long splitUnitDbId : this.splitUnitDbIdList) {
            log.error("迁移到SuperBrain时注释掉，应该从神经元网络中找切分");
            /*AllianceSplitUnit splitUnit = param.getCommercialParam().getFullAllianceSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                continue;
            long count = cityList.stream().filter(x -> StringUtils.contains(splitUnit.getName(), x) || StringUtils.contains(splitUnit.getParentUnitName(), x)).count();
            if (count > 0) {
                result.add(splitUnit.getName());
            }*/
        }
        return result;
    }

    protected void filterVoice(String exp) {
        this.filterVoiceConditions = new HashMap<>();
        List<String> cons = matchText(p_squareBrackets, exp);
        for (String con : cons) {
            String[] parts = con.replaceAll("[\\[\\]]", "").split(";");
            if (parts.length < 2)
                continue;
            this.filterVoiceConditions.put(parts[0], parts[1]);
        }
    }

    protected List<AbstractDataPoint> sortAttribute(CreativityParam param, String exp, List<AbstractDataPoint> dataPointList) {
        if (exp.contains("OWNER_FOUR") && !exp.contains("OWNER_SEVEN")) {
            dataPointList = sortAttributeDataPoint(param, dataPointList, OWNER_FOUR_ARRAY);
        } else if (!exp.contains("OWNER_FOUR") && exp.contains("OWNER_SEVEN")) {
            dataPointList = sortAttributeDataPoint(param, dataPointList, OWNER_SEVEN_ARRAY);
        } else if (exp.contains("OWNER")) {
            dataPointList = sortAttributeDataPoint(param, dataPointList, OWNER_ALL_ARRAY);
        }

        if (exp.contains("HOUSE")) {
            dataPointList = sortAttributeDataPoint(param, dataPointList, HOUSE_TYPE_ARRAY);
        }

        if (exp.contains("DECORATION")) {
            dataPointList = sortAttributeDataPoint(param, dataPointList, DECORATION_TYPE_ARRAY);
        }

        return dataPointList;
    }

    protected List<AbstractDataPoint> sortAttributeDataPoint(CreativityParam param,
                                                             List<AbstractDataPoint> allDataPointList,
                                                             String[] attributeArray) {
        List<AbstractDataPoint> attributeDataPoints = new ArrayList<>();
        Set<String> containAttributes = new HashSet<>();
        int startIndex = -1; // 当前属性数据序列首次出现的下标
        int endIndex = -1; // 当前属性数据序列最后出现的下标

        for (int i = 0; i < allDataPointList.size(); i++) {
            AbstractDataPoint point = allDataPointList.get(i);
            String attribute = point.getAttribute();
            if (StringUtils.isBlank(attribute))
                continue;
            if (Arrays.asList(attributeArray).contains(attribute)) {
                attributeDataPoints.add(point);
                containAttributes.add(attribute);
                if (startIndex == -1) {
                    startIndex = i;
                }
                endIndex = i;
            }
        }

        if (attributeDataPoints.size() < 2 || containAttributes.size() < 2)
            return allDataPointList;

        List<AbstractDataPoint> result = new ArrayList<>();
        DataPoint blankPoint = DataPointBuilder.aDataPoint().name("").build();

        if (startIndex > 0) {
            // 把下标小于startIndex的DataPoint回填
            List<AbstractDataPoint> subList = allDataPointList.subList(0, startIndex);
            result.addAll(subList);
            if (StringUtils.isNotBlank(subList.get(subList.size() - 1).getName()) && !(param.getCurrentBlock() instanceof TableBlock)) {
                result.add(blankPoint);
            }
        }

        for (String attribute : attributeArray) {
            if (StringUtils.isBlank(attribute)) {
                if (!(param.getCurrentBlock() instanceof TableBlock)) {
                    result.add(blankPoint);
                }
                continue;
            }
            List<String> keyList = new ArrayList<>();
            attributeDataPoints.stream()
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getAttribute(), attribute))
                    .forEach(x -> {
                        // 4类、7类业主都有稳定期
                        if (!keyList.contains(keyForSortAttribute(x))) {
                            keyList.add(keyForSortAttribute(x));
                            result.add(x);
                        }
                    });
        }

        if (endIndex < allDataPointList.size() - 1) {
            // 把下标大于endIndex的DataPoint回填
            List<AbstractDataPoint> subList = allDataPointList.subList(endIndex + 1, allDataPointList.size());
            if (StringUtils.isNotBlank(subList.get(0).getName()) && !(param.getCurrentBlock() instanceof TableBlock)) {
                result.add(blankPoint);
            }
            result.addAll(subList);
        }

        return result;
    }

    private String keyForSortAttribute(AbstractDataPoint point) {
        return point.getName() + "_" + point.getSplitName() + "_" + point.getIndexName() + "_" + point.getAttribute() + "_" + point.getDataPeriod();
    }

    private boolean isContainPostProcess(String type) {
        if (this.postProcessList == null || this.postProcessList.isEmpty())
            return false;

        for (String exp : this.postProcessList) {
            if (StringUtils.startsWith(exp, type + ";"))
                return true;
        }

        return false;
    }

    private List<AbstractDataPoint> postProcess(CreativityParam creativityParam,
                                                List<AbstractDataPoint> dataPoints) {
        if (this.postProcessList == null || this.postProcessList.size() == 0)
            return dataPoints;

        ProcessorParam processorParam = new ProcessorParam();
//        processorParam.setIdAndSplitUnitMap(this.idAndSplitUnitMap);

        List<AbstractProcessor> processorList = AbstractProcessor.getProcessorListInstance(this.postProcessList);
        for (AbstractProcessor processor : processorList) {
            dataPoints = processor.process(creativityParam, processorParam, dataPoints);
        }

        return dataPoints;
    }
}
