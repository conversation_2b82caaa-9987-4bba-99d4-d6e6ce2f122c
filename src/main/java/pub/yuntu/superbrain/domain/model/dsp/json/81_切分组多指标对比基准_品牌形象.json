{"id": 90, "author": "xsj", "名称": "81_切分组多指标对比基准_品牌形象", "dafeStreams": [{"type": "mysql", "signalType": "ana", "tableNames": ["$anaTableName1"], "dbIdName": "id", "dataCondition": "data_period in ('$mainPeriod') and $splitCondition_ana and $indexCondition_ana"}, {"type": "mysql", "signalType": "industryMark", "tableNames": ["$tableName_industry_mark"], "dbIdName": "id", "dataCondition": "data_period in ('$industryPeriod') and $indexCondition_industry_mark and  (industry_type>=1 and industry_type<=5)"}, {"type": "mysql", "signalType": "industryMark", "tableNames": ["$transformTableName1"], "dbIdName": "id", "dataCondition": "data_period = '$transformPeriod1' AND $indexCondition_transform1", "dataCondition备注": "条件要锁定当前需要对比的范围譬如这里只能是TOP20相关的数", "transformToGuideLine": "$highGuideLine", "备注": "$highGuideLine-- 对标名称参数，与constantsMap配置的GuideLine匹配"}, {"type": "mysql", "signalType": "industryMark", "tableNames": ["$transformTableName2"], "dbIdName": "id", "dataCondition": "data_period = '$transformPeriod2' and $indexCondition_transform2 ", "dataCondition备注": "条件要锁定集团相关的数（注意这里取的集团的数会转化为行业形式的数）。要用$P形式的取数要在别的取数地方加上集团。", "transformToGuideLine": "$lowGuideLine", "备注": "$lowGuideLine-- 对标名称参数，与constantsMap配置的GuideLine匹配"}], "poolParam": {"indexPairs": [{"complexIndexIds": [59, 122, 123], "signalType": "ana", "complexDataTypes": ["P_45", "SAMPLE"]}], "periodPairs": [{"mainPeriod": "$mainPeriod", "assistPeriods": ["$prePeriod"]}], "constants": {"constantsMap": {"$anaTableName1": "z_ana_2089", "$indexCondition_ana": "analysis_index_id in (59,122,123) ", "$splitCondition_ana": "split_group_id in (9531,9532,9533,9534,9535,9536,9537,9538,9539,9541,9542,9543,9544)", "$mainPeriod": "2022-0112", "$tableName_industry_mark": "industry_mark", "$indexCondition_industry_mark": " index_id in (59,122,123) ", "$industryPeriod": "2022", "$lowGuideLine": "行业总体", "$highGuideLine": "行业标杆", "$fixedSplit_group": 9531, "$fixedSplit_area": 9532, "$fixedSplit_company": 9533, "$fixedSplit_project": 9534, "$fixedSplit_stage": 9535, "$fixedNum1": 0, "$fixedNum2": 0.5, "$mainPeriodConvert": "$period[$mainPeriod]|PeriodConvert(yyyy年年度)", "$prePeriodConvert": "$period[$prePeriod]|PeriodConvert(yyyy年年度)", "$transformTableName1": "industry_mark", "$indexCondition_transform1": "index_id in (59,122,123) and industry_type in (5) ", "$transformPeriod1": "2022", "$transformTableName2": "industry_mark", "$indexCondition_transform2": "index_id in (59,122,123) and industry_type in (1,2,3,4) ", "$transformPeriod2": "2022"}}, "actions": [{"action": "diff", "name": "切分单元指标对比基准差值", "unitPrepare": {"convertUnit": "$children.benchMarkGroup", "loop": "", "saveUnit": "", "condition": ""}, "left": "$P", "right": "$lowGuideLine", "returnValue": "$diff", "returnValue说明": "因为默认按指标循环，每个 diff 一定是对应一个指标的，所以不需要返回指标 ID 组合"}, {"action": "count", "name": "好于基准个数", "unitPrepare": {"convertUnit": "$children.benchMarkGroup", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "$diff > $fixedNum1 ", "returnValue": "$improveCount"}, {"action": "count", "name": "样本量大于20的切分个数", "unitPrepare": {"convertUnit": "$children.benchMarkGroup", "loop": "", "saveUnit": "original", "condition": ""}, "expression": "$totalSample >= 20", "returnValue": "$countChildren"}, {"action": "fun", "name": "好于基准切分占比", "unitPrepare": {"convertUnit": "", "loop": "", "saveUnit": "original", "condition": ""}, "method": "arithmetic", "expression": "$improveCount/$countChildren", "returnValue": "$proportion"}, {"action": "count", "name": "占比高指标个数", "unitPrepare": {"convertUnit": "", "loop": "index", "saveUnit": "original", "condition": ""}, "expression": "$proportion >= 0.5", "returnValue": "$countIndex"}, {"action": "count", "name": "样本量大于0指标个数", "unitPrepare": {"convertUnit": "", "loop": "index", "saveUnit": "original", "condition": ""}, "expression": "$totalSample >= 20", "returnValue": "$countIndex2"}, {"action": "append", "name": "占比高指标名称", "unitPrepare": {"convertUnit": "", "loop": "index", "saveUnit": "original", "condition": ""}, "treaty": "$proportion >= 0.5", "appendValue": "$indexName", "append": "、", "postSort": "", "returnValue": "$append1"}, {"action": "append", "name": "占比低指标名称", "unitPrepare": {"convertUnit": "", "loop": "index", "saveUnit": "original", "condition": ""}, "treaty": "$proportion < 0.5", "appendValue": "$indexName", "append": "、", "postSort": "", "returnValue": "$append2"}], "conditionPool": [{"name": "指标数量相等", "text": "$countIndex == $countIndex2"}, {"name": "指标数量小于", "text": "$countIndex < $countIndex2 & $countIndex > 0"}, {"name": "指标数量为0", "text": "$countIndex == 0"}], "summaries": [{"name": "结论1_品牌", "treaties": [{"text": "指标数量相等"}], "dspText": {"text": "#$groupName.benchMark1#业主对#$customerName#的品牌认知普遍好于#$lowGuideLine#。", "conclusionType": "指标组合", "conclusionName": "切分组多指标对比基准_品牌形象", "businessType": "品牌", "dimension": "lowerVs", "score": "2", "indexId": "4"}}, {"name": "结论2_品牌", "treaties": [{"text": "指标数量小于"}], "dspText": {"text": "#$groupName.benchMark1#的业主对#$customerName#的品牌认知有差异，#$append1#普遍好于#$lowGuideLine#，关注#$append2#普遍低于#$lowGuideLine#。", "conclusionType": "指标组合", "conclusionName": "切分组多指标对比基准_品牌形象", "businessType": "品牌", "dimension": "lowerVs", "score": "2", "indexId": "4"}}, {"name": "结论3_品牌", "treaties": [{"text": "指标数量为0"}], "dspText": {"text": "大部分#$groupName.benchMark1#业主对#$customerName#的品牌认知普遍低于#$lowGuideLine#。", "conclusionType": "指标组合", "conclusionName": "切分组多指标对比基准_品牌形象", "businessType": "品牌", "dimension": "lowerVs", "score": "-2", "indexId": "4"}}], "tasks": [{"batchNo": 1, "name": "集团_区域结论", "mainSplitGroupId": "$fixedSplit_group", "benchMarkGroupId1": "$fixedSplit_area", "actions": ["切分单元指标对比基准差值", "好于基准个数", "样本量大于20的切分个数", "好于基准切分占比", "占比高指标个数", "样本量大于0指标个数", "占比高指标名称", "占比低指标名称"], "summaries": ["结论1_品牌", "结论2_品牌", "结论3_品牌"]}, {"batchNo": 2, "name": "集团_公司结论", "mainSplitGroupId": "$fixedSplit_group", "benchMarkGroupId1": "$fixedSplit_company", "actions": ["切分单元指标对比基准差值", "好于基准个数", "样本量大于20的切分个数", "好于基准切分占比", "占比高指标个数", "样本量大于0指标个数", "占比高指标名称", "占比低指标名称"], "summaries": ["结论1_品牌", "结论2_品牌", "结论3_品牌"]}, {"batchNo": 3, "name": "集团_项目结论", "mainSplitGroupId": "$fixedSplit_group", "benchMarkGroupId1": "$fixedSplit_project", "actions": ["切分单元指标对比基准差值", "好于基准个数", "样本量大于20的切分个数", "好于基准切分占比", "占比高指标个数", "样本量大于0指标个数", "占比高指标名称", "占比低指标名称"], "summaries": ["结论1_品牌", "结论2_品牌", "结论3_品牌"]}, {"batchNo": 4, "name": "集团_期别结论", "mainSplitGroupId": "$fixedSplit_group", "benchMarkGroupId1": "$fixedSplit_stage", "actions": ["切分单元指标对比基准差值", "好于基准个数", "样本量大于20的切分个数", "好于基准切分占比", "占比高指标个数", "样本量大于0指标个数", "占比高指标名称", "占比低指标名称"], "summaries": ["结论1_品牌", "结论2_品牌", "结论3_品牌"]}]}}