package pub.yuntu.superbrain.domain.model.brain;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.superbrain.config.YmlConfig;
import pub.yuntu.superbrain.domain.model.brain.command.BaseWave;
import pub.yuntu.superbrain.domain.model.brain.command.BrainWave;
import pub.yuntu.superbrain.domain.model.brain.command.ShareInfo;
import pub.yuntu.superbrain.domain.model.brain.param.BiocurrentParam;
import pub.yuntu.superbrain.domain.model.brain.param.ConclusionLogicParam;
import pub.yuntu.superbrain.domain.model.conclusionLogic.BaseConclusionLogic;
import pub.yuntu.superbrain.domain.model.legacy.indexGrid.IndexGrid;
import pub.yuntu.superbrain.domain.model.stencil.BiocurrentStencil;
import pub.yuntu.superbrain.domain.model.stencil.BiocurrentStencilShot;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.IndexGridRepository;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.LegacyIndexRepository;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.MsdExamIndexRepository;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.MsdExamUniteIndexRepository;
import pub.yuntu.superbrain.infrastructure.persistence.stencil.BiocurrentStencilRepository;
import pub.yuntu.superbrain.infrastructure.persistence.stencil.BiocurrentStencilShotRepository;
import pub.yuntu.superbrain.infrastructure.persistence.stencil.ConclusionLogicRepository;
import pub.yuntu.superbrain.infrastructure.util.JsonUtil;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;

/**
 * Created by dongdong on 2022/9/19 20:10.
 */
@Component
@Slf4j
@Data
public class BrainCenter {
    /**
     * todo 全部替换成基于 Queue 的分布式操作
     */
    private static HashMap<String, BrainInstance> brainInstanceMap = new HashMap<>();
    private static HashMap<String, List<BaseWave>> waitingWaveMap = new HashMap<>();

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void triggerBiocurrentByShot(String shotId,
                                        String user) {
        BiocurrentStencilShot shot = biocurrentStencilShotRepository.findById(shotId)
                .orElseThrow(() -> new NullPointerException("生物电流快照不存在，ID=" + shotId));
        shot.setLastRunner(user);
        shot.setLastRunTime(DateUtil.now());
        biocurrentStencilShotRepository.save(shot);

        // 转成对应的 buildParam
        BiocurrentParam param = JsonUtil.jsonMapper.fromJson(shot.getJsonContent(), BiocurrentParam.class);
        param.setUser(user);
        param.setWaveBatchNumber(shot.getStencilId() + "_" + shot.getId());
        String shotName = shot.getShotName();
        param.setBiocurrentName(shotName);

        // 删除已有的信号追踪文件，文件以生物电流为单位保存，每个生物电流一个文件，每次执行生物电流删除旧的信号追踪文件，重新生成
        deleteSignalProcessFile(shotName);

        this.triggerBiocurrent(param);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void triggerBiocurrent(String biocurrentId,
                                  String user) {
        BiocurrentStencil stencil = biocurrentStencilRepository.findById(biocurrentId)
                .orElseThrow(() -> new NullPointerException("生物电流配置不存在，ID=" + biocurrentId));
        // 记录最后一次执行信息
        stencil.setLastRunner(user);
        stencil.setLastRunTime(DateUtil.now());
        biocurrentStencilRepository.save(stencil);

        BiocurrentParam param = JsonUtil.jsonMapper.fromJson(stencil.getJsonContent(), BiocurrentParam.class);
        param.setUser(user);
        param.setWaveBatchNumber(stencil.getId() + "_original");
        String biocurrentName = stencil.getStencilName();
        param.setBiocurrentName(biocurrentName);

        // 删除已有的信号追踪文件，文件以生物电流为单位保存，每个生物电流一个文件，每次执行生物电流删除旧的信号追踪文件，重新生成
        deleteSignalProcessFile(biocurrentName);

        this.triggerBiocurrent(param);
    }

    private void deleteSignalProcessFile(String biocurrentName) {
        String path = ymlConfig.signalProcessPath() + biocurrentName + ".log";
        Path filePath = Paths.get(path);
        if (!Files.exists(filePath)) return;

        try {
            Files.delete(filePath);
        } catch (IOException e) {
            log.error("删除文件失败，文件：{}", path, e);
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void triggerBiocurrent(BiocurrentParam param) {
        // 1. 转换命令序列
        List<BaseWave> waveList = brainWave.switchWaveSeries(param);
        initShareInfo(param, waveList);
        waveList.forEach(x -> x.setConclusionLogicList(analyzeConclusionLogic(x.getConclusionLogicParam())));

        if (waveList.size() > 0) {
            // 2. 切换到对应的大脑执行命令，异步方式执行每一条命令
            // todo 查找到对应的大脑实例，执行命令
            BaseWave nextWave = waveList.get(0);
            BrainInstance instance = brainInstanceMap.get(nextWave.getTargetBrain());
            if (null == instance) {
                log.error("大脑[{}]不存在，请先构建", nextWave.getTargetBrain());
                return;
            }
            instance.addWave(nextWave);
            waveList.remove(0);
        }
        // 3. 剩余命令放入待定队列
        if (waveList.size() > 0) {
            waitingWaveMap.put(param.getWaveBatchNumber(), waveList);
        }
    }

    private List<BaseConclusionLogic> analyzeConclusionLogic(ConclusionLogicParam param) {
        if (null == param) return null;
        return param.analyzeConclusionLogic(conclusionLogicRepository);
    }

    public void registerBrainInstance(BrainInstance instance) {
        instance.setBrainCenter(this);
        brainInstanceMap.put(instance.getBrainName(), instance);
    }

    public void oneWaveDone(String waveBatchNumber) {
        List<BaseWave> waveList = waitingWaveMap.get(waveBatchNumber);
        if (waveList != null && !waveList.isEmpty()) {
            // 执行该命令序列的下一个命令
            BaseWave nextWave = waveList.get(0);
            BrainInstance instance = brainInstanceMap.get(nextWave.getTargetBrain());
            if (null == instance) {
                log.warn("wave中配置的大脑实例不存在，TargetBrain: {}", nextWave.getTargetBrain());
                return;
            }
            instance.addWave(nextWave);
            waveList.remove(0);
            if (waveList.size() > 0) {
                waitingWaveMap.put(waveBatchNumber, waveList);
            }
        }
    }

    public void initShareInfo(BiocurrentParam param, List<BaseWave> waveList) {
        long start = System.currentTimeMillis();
        log.info("initShareInfo start");

        HashMap<Long, String> indexNIdAndStrIdMap = new HashMap<>();
        legacyIndexRepository.findAll().forEach(x -> indexNIdAndStrIdMap.put(x.get_id(), x.getId()));

        HashMap<String, IndexGrid> indexIdAndIndexGridMap = new HashMap<>();
        HashMap<Long, IndexGrid> indexDbIdAndIndexGridMap = new HashMap<>();
        HashMap<String, IndexGrid> indexCodeAndIndexGridMap = new HashMap<>();
        indexGridRepository.findAllFormal().stream()
                .filter(x -> !StringUtils.containsIgnoreCase(x.getIndexGridName(), "yjl"))
                .forEach(x -> {
                    if (StringUtils.equals(x.getStrPlanarIndexEnum(), "SATIS_INDEX") && x.getLegacyIndexId() != null) {
                        indexDbIdAndIndexGridMap.put(x.getLegacyIndexId(), x);
                        String strId = indexNIdAndStrIdMap.get(x.getLegacyIndexId());
                        if (StringUtils.isNotBlank(strId))
                            indexIdAndIndexGridMap.put(strId, x);
                    } else if (StringUtils.equals(x.getStrPlanarIndexEnum(), "MS_INDEX") && StringUtils.isNotBlank(x.getLegacyIndexStrId())) {
                        indexIdAndIndexGridMap.put(x.getLegacyIndexStrId(), x);
                    }
                    if (StringUtils.isNotBlank(x.getLegacyIndexCode())) {
                        String indexCode = x.getLegacyIndexCode();
                        if (StringUtils.equals(indexCode, "ts")) {
                            indexCode = indexCode + "_" + x.getPlanarIndexCode();
                        }
                        indexCodeAndIndexGridMap.put(indexCode, x);
                    }
                });

        HashMap<String, String> msdExamIndexIdAndStandardIdMap = new HashMap<>();
        msdExamIndexRepository.findAll().forEach(x -> {
            if (StringUtils.isNotBlank(x.getMsdIndexId()))
                msdExamIndexIdAndStandardIdMap.put(x.getId(), x.getMsdIndexId());
        });

        HashMap<String, String> msdExamUniteIndexIdAndStandardIdMap = new HashMap<>();
        msdExamUniteIndexRepository.findAll().forEach(x -> {
            if (StringUtils.isNotBlank(x.getMsdUniteIndexId()))
                msdExamUniteIndexIdAndStandardIdMap.put(x.getId(), x.getMsdUniteIndexId());
        });

        ShareInfo shareInfo = new ShareInfo();
        shareInfo.setIndexIdAndIndexGridMap(indexIdAndIndexGridMap);
        shareInfo.setIndexDbIdAndIndexGridMap(indexDbIdAndIndexGridMap);
        shareInfo.setIndexCodeAndIndexGridMap(indexCodeAndIndexGridMap);
        shareInfo.setMsdExamIndexIdAndStandardIdMap(msdExamIndexIdAndStandardIdMap);
        shareInfo.setMsdExamUniteIndexIdAndStandardIdMap(msdExamUniteIndexIdAndStandardIdMap);
        shareInfo.setPeriods(param.getPeriods());
        shareInfo.setSpecialPeriodNameMap(param.getSpecialPeriodNameMap());

        waveList.forEach(x -> x.setShareInfo(shareInfo));

        log.info("initShareInfo end, cost time: {}", DateUtil.calPassedTime(start));
    }

    private String getIndexCode(IndexGrid indexGrid) {
        String indexCode = indexGrid.getLegacyIndexCode();
        if (StringUtils.isBlank(indexCode)) {
            return indexGrid.getStrPlanarIndexEnum().split("_")[0] + "_" + indexGrid.getPlanarIndexCode();
        }

        if (StringUtils.equalsIgnoreCase(indexCode, "ts")) {
            // 区分神客销售总分、物业总分
            return indexCode + "_" + indexGrid.getPlanarIndexCode();
        }
        return indexCode;
    }

    public BrainInstance getInstance(String brainName) {
        return brainInstanceMap.get(brainName);
    }

    public void destroyExistBrainInstance(String brainName) {
        BrainInstance instance = this.getInstance(brainName);
        if (null == instance) return;
        instance.destroy();
        instance = null;
        brainInstanceMap.remove(brainName);
        System.gc();
    }

    @Autowired
    BrainWave brainWave;
    @Autowired
    BiocurrentStencilShotRepository biocurrentStencilShotRepository;
    @Autowired
    BiocurrentStencilRepository biocurrentStencilRepository;
    @Autowired
    MsdExamIndexRepository msdExamIndexRepository;
    @Autowired
    MsdExamUniteIndexRepository msdExamUniteIndexRepository;
    @Autowired
    LegacyIndexRepository legacyIndexRepository;
    @Autowired
    IndexGridRepository indexGridRepository;
    @Autowired
    YmlConfig ymlConfig;
    @Autowired
    ConclusionLogicRepository conclusionLogicRepository;

}
