package pub.yuntu.superbrain.domain.model.legacy.planar.data;

import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.math.MathUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by dongdong on 2020/5/5 10:59.
 */
public class LegacyDataReader {

    public static String findAnaDataInMap(Map data, String dataType, boolean isSecret) {
        String result = "";
        switch (dataType) {
            case "GROUP_TOTAL_MEMBERS":
                result = "" + data.get("GROUP_TOTAL_MEMBERS");
                break;
            case "GROUP_RANKING_REVERSE":
            case "group_ranking_reverse":
                result = "" + data.get("GROUP_RANKING_REVERSE");
                break;
            case "GROUP_RANKING_EQ_VALUE":
                result = "" + data.get("GROUP_RANKING_EQ_VALUE");
                break;
            case "PARENT_DEVELOPER_AREA_VALUE":
                result = "" + data.get("PARENT_DEVELOPER_AREA_VALUE");
                break;
            case "PARENT_DEVELOPER_COMPANY_VALUE":
                result = "" + data.get("PARENT_DEVELOPER_COMPANY_VALUE");
                break;
            case "mean_amount":
            case "MEAN_AMOUNT":
                result = "" + data.get("MEAN_AMOUNT");
                break;
            case "VOICE":
                result = "" + data.get("VOICE");
                break;
            case "WEIGHTED_RATE":
                result = "" + data.get("WEIGHTED_RATE");
                break;
            case "WEIGHTED_DIFF":
                result = "" + data.get("WEIGHTED_DIFF");
                break;
            case "WEIGHTED_EFFECT":
                result = "" + data.get("WEIGHTED_EFFECT");
                break;
            case "SAMPLE_RATE":
                result = "" + data.get("SAMPLE_RATE");
                break;
            case "SAMPLE_PERCENT":
                result = "" + data.get("SAMPLE_PERCENT");
                break;
            case "SAMPLING_FRAME_PERCENT":
                result = "" + data.get("SAMPLING_FRAME_PERCENT");
                break;
            case "DISPERSION":
                result = "" + data.get("DISPERSION");
                break;
            case "INDUSTRY_DISPERSION":
                result = "" + data.get("INDUSTRY_DISPERSION");
                break;
            case "INDUSTRY_RANKING":
                result = "" + data.get("INDUSTRY_RANKING");
                break;
            case "INDUSTRY_TOTAL_CUSTOMERS":
                result = "" + data.get("INDUSTRY_TOTAL_CUSTOMERS");
                break;
            case "INDUSTRY_RANKING_EQ_VALUE":
                result = "" + data.get("INDUSTRY_RANKING_EQ_VALUE");
                break;
            case "TEAM_RANKING":
                result = "" + data.get("TEAM_RANKING");
                break;
            case "TEAM_TOTAL_MEMBERS":
                result = "" + data.get("TEAM_TOTAL_MEMBERS");
                break;
            case "TEAM_RANKING_EQ_VALUE":
                result = "" + data.get("TEAM_RANKING_EQ_VALUE");
                break;
            case "CUSTOMER_CITY_RANKING_REVERSE":
                result = "" + data.get("CUSTOMER_CITY_RANKING_REVERSE");
                break;
            case "CITY_TOTAL_CUSTOMERS":
                result = "" + data.get("CITY_TOTAL_CUSTOMERS");
                break;
            case "rank":
                result = "" + data.get("RANKING");
                break;
            case "total":
            case "t":
                String strTotalOption = "" + data.get("SAMPLE_OF_OPTIONS");
                if (isSecret) {
                    return strTotalOption;
                }
                if (StringUtils.isNotEmpty(strTotalOption) && !strTotalOption.equalsIgnoreCase("null")) {
                    String[] str = strTotalOption.split(";");
                    int max = 0;
                    for (int i = 0; i < str.length; i++) {
                        int option = Integer.parseInt(str[i].split("\\|\\|\\|")[1]);
                        max = Math.max(max, option);
                    }
                    return "" + max;
                } else
                    result = "" + data.get("TOTAL_SAMPLE");
                break;
            case "wt":
                String wt = "" + data.get("WEIGHTED_TOTAL_SAMPLE");
                if (StringUtils.isEmpty(wt) || wt.equalsIgnoreCase("null"))
                    wt = "0";
                result = wt;
                break;
            case "t1":
                Object totalByOption = data.get("SAMPLE_OF_OPTIONS");
                if (totalByOption != null && StringUtils.isNotBlank("" + totalByOption)) {
                    result = getCount(1, data);
                } else
                    result = "" + data.get("C1");
                break;
            case "t2":
            case "t3":
            case "t4":
            case "t5":
            case "t6":
            case "t7":
            case "t8":
            case "t9":
            case "t10":
            case "t11":
            case "t12":
            case "t13":
            case "t14":
            case "t15":
            case "t16":
            case "t17":
            case "t18":
            case "t19":
            case "t20":
            case "t21":
            case "t22":
            case "t23":
            case "t24":
            case "t25":
            case "t26":
            case "t27":
            case "t28":
            case "t29":
            case "t30":
            case "t31":
            case "t32":
            case "t33":
            case "t34":
            case "t35":
            case "t36":
                int index = Integer.parseInt(dataType.substring(1));
                result = getCount(index, data);
                break;
            case "p1":
            case "p2":
            case "p3":
            case "p4":
            case "p5":
            case "p6":
            case "p7":
            case "p8":
            case "p9":
            case "p10":
            case "p11":
            case "p12":
            case "p13":
            case "p14":
            case "p15":
            case "p16":
            case "p17":
            case "p18":
            case "p19":
            case "p20":
            case "p21":
            case "p22":
            case "p23":
            case "p24":
            case "p25":
            case "p26":
            case "p27":
            case "p28":
            case "p29":
            case "p30":
            case "p31":
            case "p32":
            case "p33":
            case "p34":
            case "p35":
            case "p36":
            case "p37":
            case "p38":
            case "p39":
            case "p40":
            case "p41":
            case "p42":
            case "p43":
            case "p44":
            case "p45":
            case "p46":
            case "p47":
            case "p48":
            case "p49":
            case "p50":
            case "p51":
            case "p52":
            case "p53":
            case "p54":
            case "p55":
                index = Integer.parseInt(dataType.substring(1));
                result = getWeightedPercent(data, index);
                break;
            case "average":
                result = "" + data.get("AVERAGE_SCORE");
                break;
            case "p_45":
            case "p-45":
                if (isSecret) {
                    Object s_weighted45 = data.get("WEIGHTED_4_OR_5_PERCENTAGE");
                    double weighted45 = 0D;
                    if (s_weighted45 != null && StringUtils.isNotBlank("" + s_weighted45)) {
                        weighted45 = Double.parseDouble("" + s_weighted45);
                    }
                    result = "" + weighted45;
                } else {
                    double total = Double.parseDouble("" + (null == data.get("TOTAL_SAMPLE") ? 0 : data.get("TOTAL_SAMPLE")));
                    if (total == 0D)
                        result = "";
                    else {
                        Object s_weighted45 = data.get("WEIGHTED_4_OR_5_PERCENTAGE");
                        if (s_weighted45 != null && StringUtils.isNotBlank("" + s_weighted45) && !("" + s_weighted45).equalsIgnoreCase("null")) {
                            String temp45 = "" + s_weighted45;
                            if (temp45.equals("0") || temp45.equals("0.0")) {
                                // 判断是否真的为0
                                if(StringUtils.isNotBlank(getWeightedPercent(data, 5)) && StringUtils.isNotBlank(getWeightedPercent(data, 4))) {
                                    double p5 = Double.parseDouble(getWeightedPercent(data, 5));
                                    double p4 = Double.parseDouble(getWeightedPercent(data, 4));
                                    if (p5 + p4 > 0) {
                                        return "" + (p4 + p5);
                                    }
                                    else
                                        result = temp45;
                                }
                                else
                                    result = temp45;
                            }
                            else
                                result = temp45;
                        }
                        else {
                            if(StringUtils.isBlank(getWeightedPercent(data, 5))
                                    || StringUtils.isBlank(getWeightedPercent(data, 4)))
                                return "";
                            double p5 = Double.parseDouble(getWeightedPercent(data, 5));
                            double p4 = Double.parseDouble(getWeightedPercent(data, 4));
                            return "" + (p4 + p5);
                        }
                    }
                }
                break;
            case "p_23":
            case "p-23":
                double c2 = Double.parseDouble("" + data.get("C2"));
                double c3 = Double.parseDouble("" + data.get("C3"));
                double total = Double.parseDouble("" + data.get("TOTAL_SAMPLE"));
                result = MathUtil.getSixPoint((c2 + c3) / total);
                break;
            case "p_34":
            case "p-34":
                Object s_weighted45 = data.get("WEIGHTED_4_OR_5_PERCENTAGE");
                double weighted45 = 0D;
                if (s_weighted45 != null && StringUtils.isNotBlank("" + s_weighted45)) {
                    weighted45 = Double.parseDouble("" + s_weighted45);
                }
                if (weighted45 > 0D)
                    result = "" + weighted45;
                else {
                    double c4 = Double.parseDouble("" + data.get("C4"));
                    c3 = Double.parseDouble("" + data.get("C3"));
                    total = Double.parseDouble("" + data.get("TOTAL_SAMPLE"));
                    result = MathUtil.getSixPoint((c4 + c3) / total);
                }
                break;
            case "p_13":
                if(StringUtils.isBlank(getWeightedPercent(data, 1)))
                    return "";
                double p1 = Double.parseDouble(getWeightedPercent(data, 1));
                String str_p3 = getWeightedPercent(data, 3);
                if (StringUtils.isNotBlank(str_p3)) {
                    result = "" + (p1 + Double.parseDouble(str_p3));
                }
                else {
                    result = "" + p1;
                }
                break;
            case "p_12":
                double c1 = Double.parseDouble("" + data.get("C1"));
                c2 = Double.parseDouble("" + data.get("C2"));
                total = Double.parseDouble("" + data.get("TOTAL_SAMPLE"));
                result = MathUtil.getSixPoint((c1 + c2) / total);
                break;
            case "p_123":
                c1 = Double.parseDouble("" + data.get("C1"));
                c2 = Double.parseDouble("" + data.get("C2"));
                c3 = Double.parseDouble("" + data.get("C3"));
                total = Double.parseDouble("" + data.get("TOTAL_SAMPLE"));
                result = MathUtil.getSixPoint((c1 + c2 + c3) / total);
                break;
            case "p_345":
                c3 = Double.parseDouble("" + data.get("C3"));
                double c4 = Double.parseDouble("" + data.get("C4"));
                double c5 = Double.parseDouble("" + data.get("C5"));
                total = Double.parseDouble("" + data.get("TOTAL_SAMPLE"));
                result = MathUtil.getSixPoint((c3 + c4 + c5) / total);
                break;
            case "c1":
            case "c2":
            case "c3":
            case "c4":
            case "c5":
            case "c6":
            case "c7":
            case "c8":
            case "c9":
            case "c10":
            case "c11":
            case "c12":
            case "c13":
            case "c14":
            case "c15":
            case "c16":
            case "c17":
            case "c18":
            case "c19":
            case "c20":
            case "c21":
            case "c22":
            case "c23":
            case "c24":
            case "c25":
            case "c26":
            case "c27":
            case "c28":
            case "c29":
            case "c30":
            case "c31":
            case "c32":
            case "c33":
            case "c34":
            case "c35":
            case "c36":
                result = "" + data.get(dataType.toUpperCase());
                break;
            case "c-12":
            case "c_12":
                c1 = Double.parseDouble("" + data.get("C1"));
                c2 = Double.parseDouble("" + data.get("C2"));
                result = "" + (c1 + c2);
                break;
            case "c-45":
            case "c_45":
                c4 = Double.parseDouble("" + (null == data.get("C4") ? 0 : data.get("C4")));
                c5 = Double.parseDouble("" + (null == data.get("C5") ? 0 : data.get("C5")));
                result = "" + (c4 + c5);
                break;
            case "nps":
                String str5 = getWeightedPercent(data, 5);
                if (StringUtils.isBlank(str5))
                    return "";
                double p5 = Double.parseDouble(str5);
                p1 = 0D;
                String sp1 = getWeightedPercent(data, 1);
                if (StringUtils.isNotBlank(sp1)) {
                    p1 = Double.parseDouble(sp1);
                }
                double p2 = 0D;
                String sp2 = getWeightedPercent(data, 2);
                if (StringUtils.isNotBlank(sp2)) {
                    p2 = Double.parseDouble(sp2);
                }
                double p3 = 0D;
                String sp3 = getWeightedPercent(data, 3);
                if (StringUtils.isNotBlank(sp3)) {
                    p3 = Double.parseDouble(sp3);
                }
                result = "" + (p5 - (p1 + p2 + p3));
                break;
            case "sa":
                c1 = Double.parseDouble("" + data.get("C1"));
                c2 = Double.parseDouble("" + data.get("C2"));
                result = MathUtil.getSixPoint(c1 / c2);
                break;
            case "deduction":
                if (StringUtils.isNotBlank("" + data.get("CALCULATED_WEIGHT")))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("CALCULATED_WEIGHT")));
                break;
            case "ss":
                if (StringUtils.isNotBlank("" + data.get("STANDARD_SCORE")) && !StringUtils.equalsIgnoreCase("" + data.get("STANDARD_SCORE"), "null"))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("STANDARD_SCORE")));
                break;
            case "rs":
                if (StringUtils.isNotBlank("" + data.get("RAW_SCORE")))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("RAW_SCORE")));
                break;
            case "cs":
                if (StringUtils.isNotBlank("" + data.get("CALCULATED_SCORE")) && !StringUtils.equalsIgnoreCase("" + data.get("CALCULATED_SCORE"), "null"))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("CALCULATED_SCORE")));
                break;
            case "nws":
                if (StringUtils.isNotBlank("" + data.get("NON_WEIGHT_SCORE")))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("NON_WEIGHT_SCORE")));
                break;
            case "pcs":
                if (StringUtils.isNotBlank("" + data.get("PROFESSION_CALCULATED_SCORE")))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("PROFESSION_CALCULATED_SCORE")));
                break;
            case "pnws":
                if (StringUtils.isNotBlank("" + data.get("PROFESSION_NON_WEIGHT_SCORE")))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("PROFESSION_NON_WEIGHT_SCORE")));
                break;
            case "cor": // 相关性分析
                if (StringUtils.isNotBlank("" + data.get("ANA_CORRELATION")) && !("" + data.get("ANA_CORRELATION")).equals("null"))
                    result = MathUtil.getSixPoint(Double.parseDouble("" + data.get("ANA_CORRELATION")));
                break;
            case "GOOD_PERCENT":
                result = "" + data.get("GOOD_PERCENT");
                break;
            case "BAD_PERCENT":
                result = "" + data.get("BAD_PERCENT");
                break;
            case "NET_PRAISE_PERCENT":
                String goodPercent = "" + data.get("GOOD_PERCENT");
                String badPercent = "" + data.get("BAD_PERCENT");
                if (StringUtils.isNotBlank(goodPercent) && !StringUtils.equalsIgnoreCase(goodPercent, "null")
                        && StringUtils.isNotBlank(badPercent) && !StringUtils.equalsIgnoreCase(badPercent, "null")) {
                    double netPraisePercent = Double.parseDouble(goodPercent) - Double.parseDouble(badPercent);
                    result = "" + netPraisePercent;
                }
                break;
            case "TOTAL_PERCENT":
                result = "" + data.get("TOTAL_PERCENT");
                break;
        }
        return result;
    }

    public static String getWeightedPercent(Map data, int weightIndex) {
        String result = "";
        String stotal = getTotal(data, weightIndex);
        if (stotal.equals("") || stotal.equalsIgnoreCase("null")) {
            result = "";
        }
        else {
            double total = Double.parseDouble(stotal);
            if (total == 0D)
                result = "";
            else {
                Object s_weighted = data.get("WEIGHTED_" + weightIndex + "_PERCENTAGE");
                Double weighted = null;
                if (s_weighted != null && StringUtils.isNotBlank("" + s_weighted)) {
                    weighted = Double.parseDouble("" + s_weighted);
                }
                if (weighted != null)
                    result = "" + weighted;
                else {
                    if(null == data.get("C" + weightIndex))
                        return "";
                    double c = Double.parseDouble("" + data.get("C" + weightIndex));
                    result = MathUtil.getEightPoint(c / total);
                }
            }
        }
        return result;
    }

    private static String getCount(int index, Map data) {
        Object totalByOption = data.get("SAMPLE_OF_OPTIONS");
        if (totalByOption != null && StringUtils.isNotBlank("" + totalByOption)) {
            String[] str = ("" + totalByOption).split(";");
            HashMap<String, String> map = new HashMap<>();
            for (String s : str) {
                String[] arr = s.split("\\|\\|\\|");
                String[] keys = arr[0].split(",");
                for (String key : keys)
                    map.put(key.trim(), arr[1].trim());
            }
            String val = map.get("" + index);
            if (StringUtils.isEmpty(val))
                val = "";
            return val;
        } else
            return "" + data.get("C" + index);
    }

    private static String getTotal(Map data, int value) {
        Object totalByOption = data.get("SAMPLE_OF_OPTIONS");
        if (totalByOption != null && StringUtils.isNotEmpty("" + totalByOption)) {
            return getCount(value, data);
        } else
            return "" + data.get("TOTAL_SAMPLE");
    }
}
