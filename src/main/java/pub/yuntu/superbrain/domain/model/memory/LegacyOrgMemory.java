package pub.yuntu.superbrain.domain.model.memory;

import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.nlpcn.commons.lang.util.StringUtil;
import pub.yuntu.superbrain.domain.model.deliver.DeliverStat;
import pub.yuntu.superbrain.domain.model.deliver.DeliverStatStatistics;
import pub.yuntu.superbrain.domain.model.identity.LegacyOrgIdentity;
import pub.yuntu.superbrain.domain.model.legacy.exam.LegacyIndex;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitUnit;
import pub.yuntu.superbrain.domain.model.signal.BaseSignal;
import pub.yuntu.superbrain.domain.model.stat.AnaStat;
import pub.yuntu.superbrain.domain.model.stat.FoundationStat;
import pub.yuntu.superbrain.domain.model.stat.WeightedStat;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Description 传统组织结构记忆区
 * @createTime 2022年09月14日 13:35:38
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class LegacyOrgMemory extends BaseMemory {

    Long examDbId;
    String earliestDeliverPeriod;
    ConcurrentHashMap<String, FoundationStat> foundationStatMap;// 9083_2022-07,FoundationStat
    ConcurrentHashMap<String, WeightedStat> weightedStatMap;// 9084_2022-07,WeightedStat
    ConcurrentHashMap<String, Object> mapData;
    ConcurrentHashMap<String, String> infoData;
    ConcurrentHashMap<String, String> statData;
    ConcurrentHashMap<String, AtomicInteger> countMap;
    ConcurrentHashMap<String, List<HashMap>> listMap; // key: user/data
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<HashMap>>>> listMapTransform;
    ConcurrentHashMap<String, Set<String>> listMapCache; // key: user/data
    Set<String> keySet;
    ConcurrentHashMap<String, Integer> projectStatMap; // data_period/准1数量
    ConcurrentHashMap<String, Integer> projectDeliveredMap; // data_period/磨1数量
    Set<String> nonZhunPeriods; // 项目/有非准业主的月份
    ConcurrentHashMap<String, ConcurrentHashMap<String, DeliverStatStatistics>> deliverStatStatisticsMap;// data_period/plan_deliver_number,real_deliver_number,first_time_deliver

    /**
     * 读取用户
     *
     * @param examId     问卷数字ID
     * @param conditions 查询条件
     * @return
     */
    public List<HashMap> readUser(long examId, String conditions) {
        String table = "z_user_" + examId;
        return null;
    }

    /**
     * 读取数据
     *
     * @param examId     问卷数字ID
     * @param conditions 查询条件
     * @return
     */
    public List<HashMap> readData(long examId, String conditions) {
        String table = "z_data_" + examId;
        return null;
    }

    /**
     * 读取分析结果
     *
     * @param examId     问卷数字ID
     * @param conditions 查询条件
     * @return
     */
    public List<HashMap> readAna(long examId, String conditions) {
        String table = "z_ana_" + examId;
        return null;
    }

    public synchronized int addUserToList(BaseSignal signal) {
        if (null == listMapCache) {
            listMapCache = new ConcurrentHashMap<>();
        }
        listMapCache.computeIfAbsent("user", k -> Sets.newHashSet());
        if (null == listMap) {
            listMap = new ConcurrentHashMap<>();
        }
        listMap.computeIfAbsent("user", k -> Lists.newArrayList());

        HashMap<String, Object> mapData = signal.getMapData();
        Object encryptObj = mapData.get("ENCRYPT");
        if (null == encryptObj) {
            log.error("名单数据中没有 ENCRYPT");
            return -1;
        }
        String encrypt = encryptObj + "";
        if (!listMapCache.get("user").contains(encrypt)) {
            listMap.get("user").add(mapData);
            listMapCache.get("user").add(encrypt);
        }

        return listMap.get("user").size();
    }

    public synchronized int addDataToList(BaseSignal signal) {
        if (null == listMapCache) {
            listMapCache = new ConcurrentHashMap<>();
        }
        listMapCache.computeIfAbsent("data", k -> Sets.newHashSet());
        if (null == listMap) {
            listMap = new ConcurrentHashMap<>();
        }
        listMap.computeIfAbsent("data", k -> Lists.newArrayList());

        HashMap<String, Object> mapData = signal.getMapData();
        Object encryptObj = mapData.get("ENCRYPT");
        if (null == encryptObj) {
            log.error("数据中没有 ENCRYPT");
            return -1;
        }
        String encrypt = encryptObj + "";
        if (!listMapCache.get("data").contains(encrypt)) {
            listMap.get("data").add(mapData);
            listMapCache.get("data").add(encrypt);
        }

        return listMap.get("data").size();
    }

    /**
     * 写入分析结果
     *
     * @param examId   问卷数字ID
     * @param dataList
     */
    public void writeAna(long examId, List<HashMap> dataList) {
        String table = "z_ana_" + examId;
    }

    public void checkFoundationStat(String attributeType, String dataPeriod, Long examDbId) {
        if (this.foundationStatMap == null) {
            this.foundationStatMap = new ConcurrentHashMap<>();
            this.foundationStatMap.put(attributeType + "_" + dataPeriod, new FoundationStat());
        }
        if (this.foundationStatMap.get(attributeType + "_" + dataPeriod) == null)
            this.foundationStatMap.put(attributeType + "_" + dataPeriod, new FoundationStat());
        if (this.examDbId == null)
            this.examDbId = examDbId;
    }

    public void checkWeightedStat(String attributeType, String dataPeriod, Long examDbId) {
        if (this.weightedStatMap == null) {
            this.weightedStatMap = new ConcurrentHashMap<>();
            this.weightedStatMap.put(attributeType + "_" + dataPeriod, new WeightedStat());
        }
        if (this.weightedStatMap.get(attributeType + "_" + dataPeriod) == null)
            this.weightedStatMap.put(attributeType + "_" + dataPeriod, new WeightedStat());
        if (this.examDbId == null)
            this.examDbId = examDbId;
    }

    public void acceptFoundationStat(HashMap<String, SplitUnit> allUnitMap, ConcurrentHashMap<String, FoundationStat> childFoundationStatMap, String dataPeriod, Long examDbId) {
        for (String key : allUnitMap.keySet()) {
            FoundationStat childFoundationStat = childFoundationStatMap.get(key + "_" + dataPeriod);
            if (childFoundationStat != null) {
                this.checkFoundationStat(key, dataPeriod, examDbId);
                SplitUnit splitUnit = allUnitMap.get(key);
                FoundationStat foundationStat = this.getFoundationStatMap().get(key + "_" + dataPeriod);
                foundationStat.addFoundationStat(splitUnit, childFoundationStat);
                this.foundationStatMap.put(key + "_" + dataPeriod, foundationStat);
            }
        }
    }

    public ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<HashMap>>>> transformListMap(LegacyOrgIdentity identity) {
        // 1 user / data
        // 2 attr type: datasource / d_survy_mode / d_owner_type_four / d_owner_type_seven_plus ...
        // 3 attr value
        // 4 value list
        if (null == identity.getChildren()) {
            transformListMap();
            return listMapTransform;
        } else {
            identity.getChildren().forEach(childIdentity -> {
                LegacyOrgMemory childMemory = (LegacyOrgMemory) childIdentity.getMemory();
                ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<HashMap>>>> childMap = childMemory.transformListMap((LegacyOrgIdentity) childIdentity);

                if (null == childMap) return;
                if (null == listMapTransform) {
                    listMapTransform = new ConcurrentHashMap<>();
                }
                Arrays.asList("user", "data").forEach(key -> {
                    listMapTransform.computeIfAbsent(key, k -> new ConcurrentHashMap<>());
                    childMap.get(key).forEach((attr1, value1) -> {
                        if (null == value1) return;
                        value1.forEach((attr2, value2) -> {
                            if (null == value2) return;
                            value2.forEach(data -> {
                                listMapTransform.get(key).computeIfAbsent(attr1, k -> new ConcurrentHashMap<>());
                                listMapTransform.get(key).get(attr1).computeIfAbsent(attr2, k -> Lists.newArrayList());
                                listMapTransform.get(key).get(attr1).get(attr2).add(data);
                            });
                            /*value2.forEach((attr3, value3) -> {
                                if (null == value3) return;
                                value3.forEach(data -> {
                                    listMapTransform.get(key).computeIfAbsent(attr1, k -> new ConcurrentHashMap<>());
                                    listMapTransform.get(key).get(attr1).computeIfAbsent(attr2, k -> Lists.newArrayList());
                                    listMapTransform.get(key).get(attr1).get(attr2).add(data);
                                });
                            });*/
                        });
                    });
                });
            });
        }
        return listMapTransform;
    }

    private ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<HashMap>>>> transformListMap() {
        if (null == listMap) return null;
        if (null == listMapTransform) {
            listMapTransform = new ConcurrentHashMap<>();
        }
        listMapTransform.computeIfAbsent("user", k -> new ConcurrentHashMap<>());
        listMapTransform.computeIfAbsent("data", k -> new ConcurrentHashMap<>());
        List<HashMap> userList = listMap.get("user"), dataList = listMap.get("data");
        String[] attrTypes = new String[]{"CALLCENTER_CALL_STATUS", "D_SURVEY_MODE", "DATA_SOURCE", "D_OWNER_TYPE_FOUR"
                , "D_OWNER_TYPE_SEVEN_PLUS", "R1"};
        for (String attrType : attrTypes) {
            listMapTransform.get("user").put(attrType, new ConcurrentHashMap<>());
            listMapTransform.get("data").put(attrType, new ConcurrentHashMap<>());
        }
        if (null != userList) {
            userList.forEach(user -> {
                Arrays.stream(attrTypes).forEach(attrType -> {
                    if (!user.containsKey(attrType)) return;

                    ConcurrentHashMap<String, List<HashMap>> userMap = listMapTransform.get("user").get(attrType);
                    String attrValue = user.get(attrType) + "";
                    userMap.computeIfAbsent(attrValue, k -> Lists.newArrayList());
                    userMap.get(attrValue).add(user);
                });
            });
        }
        if (null != dataList) {
            dataList.forEach(data -> {
                Arrays.stream(attrTypes).forEach(attrType -> {
                    if (!data.containsKey(attrType)) return;

                    ConcurrentHashMap<String, List<HashMap>> dataMap = listMapTransform.get("data").get(attrType);
                    String attrValue = data.get(attrType) + "";
                    dataMap.computeIfAbsent(attrValue, k -> Lists.newArrayList());
                    dataMap.get(attrValue).add(data);
                });
            });
        }
        return listMapTransform;
    }

    public void clearListMapTransform() {
        if (null == listMapTransform) return;
        listMapTransform.forEach((key1, map1) -> {
            if (null == map1) return;
            map1.forEach((key2, map2) -> {
                if (null == map2) return;
                map2.forEach((key3, list) -> {
                    if (null == list) return;
                    list.clear();
                });
                map2.clear();
            });
            map1.clear();
        });
        listMapTransform.clear();
    }

    @Override
    public boolean hasData() {
        boolean hasData = super.hasData();
        if (hasData) return true;

        if (null != foundationStatMap) return true;
        if (null != weightedStatMap) return true;
        if (null != mapData) return true;
        if (null != infoData) return true;
        if (null != statData) return true;
        if (null != countMap) return true;
        if (null != listMap) return true;
        if (null != listMapTransform) return true;
        if (null != listMapCache) return true;
        if (null != keySet) return true;
        if (null != projectStatMap) return true;
        if (null != projectDeliveredMap) return true;
        if (null != nonZhunPeriods) return true;
        return null != deliverStatStatisticsMap;
    }

    @Override
    public void clear() {
        super.clear();
        if (null != foundationStatMap) {
            foundationStatMap.values().forEach(FoundationStat::clear);
            foundationStatMap.clear();
        }
        if (null != weightedStatMap) {
            weightedStatMap.values().forEach(WeightedStat::clear);
            weightedStatMap.clear();
        }
        if (null != mapData) {
            mapData.clear();
        }
        if (null != infoData) {
            infoData.clear();
        }
        if (null != statData) {
            statData.clear();
        }
        if (null != countMap) {
            countMap.clear();
        }
        if (null != listMap) {
            listMap.forEach((key, list) -> {
                if (null != list) list.clear();
            });
            listMap.clear();
        }
        this.clearListMapTransform();
        if (null != listMapCache) {
            listMapCache.forEach((key, set) -> {
                if (null != set) set.clear();
            });
            listMapCache.clear();
        }
    }

    public void acceptWeightedStat(SplitUnit splitUnit, FoundationStat foundationStat, String dataPeriod, boolean isBaseUnit, Long examDbId) {
        ConcurrentHashMap<LegacyIndex, AnaStat> anaStatHashMap = foundationStat.getAnaStatHashMap();
        for (AnaStat baseAnaStat : anaStatHashMap.values()) {
            if (baseAnaStat.getWeightedType() != null && StringUtil.isNotBlank(baseAnaStat.getWeightedType())) {
                String attribute = splitUnit.getAttributeType();
                this.checkWeightedStat(attribute, dataPeriod, examDbId);
                WeightedStat weightedStat = this.getWeightedStatMap().get(attribute + "_" + dataPeriod);
                weightedStat.addWeightedStat(splitUnit, baseAnaStat, isBaseUnit);
                this.weightedStatMap.put(attribute + "_" + dataPeriod, weightedStat);
            }
        }
    }

    public void acceptWeightedStat(HashMap<String, SplitUnit> allUnitMap, ConcurrentHashMap<String, WeightedStat> childWeightedStatMap, String dataPeriod, Long examDbId) {
        if (childWeightedStatMap == null)
            return;
        for (String childStatKey : childWeightedStatMap.keySet()) {
            String unitKey = childStatKey.split("_")[0];
            SplitUnit splitunit = allUnitMap.get(unitKey);
            WeightedStat childWeightedStat = childWeightedStatMap.get(childStatKey);
            this.checkWeightedStat(splitunit.getAttributeType(), dataPeriod, examDbId);
            WeightedStat weightedStat = this.getWeightedStatMap().get(splitunit.getAttributeType() + "_" + dataPeriod);
            weightedStat.addWeightedStat(splitunit, childWeightedStat);
            this.weightedStatMap.put(unitKey + "_" + dataPeriod, weightedStat);
        }
    }

}
