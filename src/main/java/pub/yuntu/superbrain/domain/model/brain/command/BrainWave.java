package pub.yuntu.superbrain.domain.model.brain.command;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pub.yuntu.foundation.id.UUIDUtil;
import pub.yuntu.superbrain.domain.model.brain.param.BiocurrentParam;
import pub.yuntu.superbrain.domain.model.brain.param.SignalLogicChainItem;
import pub.yuntu.superbrain.domain.model.brain.param.WaveParam;
import pub.yuntu.superbrain.infrastructure.util.ExceptionUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by dongdong on 2022/9/20 16:00.
 */
@Component
@Slf4j
public class BrainWave {

    public List<BaseWave> switchWaveSeries(BiocurrentParam param) {
        List<BaseWave> list = new ArrayList<>();
        for (WaveParam waveParam : param.getWaves()) {
            BaseWave wave = null;
            switch (waveParam.getType()) {
                case "outerDataSequence":
                    wave = new OuterDataSequenceWave();
                    ((OuterDataSequenceWave) wave).setDafeStreams(waveParam.getDafeStreams());
                    ((OuterDataSequenceWave) wave).setDeliverStatParam(waveParam.getDeliverStatParam());
                    ((OuterDataSequenceWave) wave).setIndexCodeList(waveParam.getIndexCodeList());
                    break;
                case "innerDataSequence":
                    wave = new InnerDataSequenceWave();
                    ((InnerDataSequenceWave) wave).setCalculatorResultNames(waveParam.getCalculatorResultNames());
                    ((InnerDataSequenceWave) wave).setCalculatorResultNamePrefix(waveParam.getCalculatorResultNamePrefix());
                    ((InnerDataSequenceWave) wave).setTargetDataPeriod(waveParam.getTargetDataPeriod());
                    ((InnerDataSequenceWave) wave).setWeightedParam(waveParam.getWeightedParam());
                    ((InnerDataSequenceWave) wave).setExamDbId(waveParam.getExamDbId());
                    ((InnerDataSequenceWave) wave).setNamelistCompleteStatParam(waveParam.getNamelistCompleteStatParam());
                    break;
                case "command":
                    wave = new BrainCommandWave();
                    ((BrainCommandWave) wave).setCommand(waveParam.getCommand());
                    ((BrainCommandWave) wave).setOutputs(waveParam.getOutputs());
                    ((BrainCommandWave) wave).setPrintLogParams(waveParam.getPrintLogParams());
                    ((BrainCommandWave) wave).setMarkParams(waveParam.getMarkParams());
                    break;
                case "systemCommand":
                    wave = new SystemCommandWave();
                    ((SystemCommandWave) wave).setParams(waveParam.getParams());
                    break;
                case "triggerDataSequence":
                    log.error("TODO: 暂未实现的 wave 处理类型：{}", waveParam.getType());
                    continue;
                default:
                    ExceptionUtil.throwIllegalArgumentException("不支持的 wave type：" + waveParam.getType());
            }
            wave.setOrder(waveParam.getOrder());
            wave.setType(waveParam.getType());
            wave.setTriggerLogics(waveParam.getTriggerLogics());
            wave.setFromVirtualGroups(waveParam.getFromVirtualGroups());
            wave.setTargetVirtualGroups(waveParam.getTargetVirtualGroups());
            copyWaveProperties(param, waveParam, wave);

            if (null == waveParam.getSignalLogicChain()) {
                list.add(wave);
                continue;
            }

            /*
            - 如果一个signal顺序直接执行多个信号逻辑的计算器
                - 一次只能计算一个信号的数据，一个信号的数据在多个计算器中作为输入输出
                - 下一个计算器需要的统计结果还没有准备完成，因为前一个计算器没有执行完所有信号
            - 将一个wave有logicChain的，拆分成多个innerDataSequence的wave
                - 不影响现有的任何处理逻辑
                - 一个wave中的所有signal全部处理完，才会处理下一个wave，也就是一个计算器计算完所有信号数据后才会进入下一个计算器
                - 不会出现一个信号进入计算器A再进入计算器B再进入计算器C的情况，因为下一个计算器可能需要的是统计结果
             */
            // 分裂出多个wave，作为内部数据源wave
            splitOutSignalLogicChain(param, list, waveParam, wave);
        }
        return list;
    }

    private static void splitOutSignalLogicChain(BiocurrentParam param, List<BaseWave> list, WaveParam waveParam, BaseWave wave) {
        List<SignalLogicChainItem> chain = waveParam.getSignalLogicChain();
        // 排序链条中的信号逻辑配置
        List<SignalLogicChainItem> sortedChain = chain.stream()
                .sorted(Comparator.comparingInt(SignalLogicChainItem::getOrder))
                .collect(Collectors.toList());

        for (int i = 0; i < sortedChain.size(); i++) {
            SignalLogicChainItem chainItem = sortedChain.get(i);
            // 第一个信号逻辑放入第一个wave中
            if (i == 0) {
                wave.setTriggerLogics(chainItem.getTriggerLogics());
                wave.setComments(wave.getComments() + " - 拆分信号逻辑：" + String.join(", ", chainItem.getTriggerLogics()));
                list.add(wave);
                continue;
            }
            InnerDataSequenceWave splitOutWave = new InnerDataSequenceWave();
            splitOutWave.setTargetDataPeriod(waveParam.getTargetDataPeriod());
            splitOutWave.setWeightedParam(waveParam.getWeightedParam());
            splitOutWave.setExamDbId(waveParam.getExamDbId());
            splitOutWave.setNamelistCompleteStatParam(waveParam.getNamelistCompleteStatParam());

            copyWaveProperties(param, waveParam, splitOutWave);

            splitOutWave.setOrder(wave.getOrder() + ((float) i / 10)); // i==0已经跳过
            splitOutWave.setType("innerDataSequence");
            splitOutWave.setTriggerLogics(chainItem.getTriggerLogics());
            splitOutWave.setFromVirtualGroups(chainItem.getFromVirtualGroups());
            splitOutWave.setTargetVirtualGroups(chainItem.getTargetVirtualGroups());
            splitOutWave.setCalculatorResultNames(chainItem.getCalculatorResultNames());
            splitOutWave.setCalculatorResultNamePrefix(chainItem.getCalculatorResultNamePrefix());
            splitOutWave.setComments(splitOutWave.getComments() + " - 拆分信号逻辑：" + String.join(", ", chainItem.getTriggerLogics()));

            list.add(splitOutWave);
        }
    }

    private static void copyWaveProperties(BiocurrentParam param, WaveParam waveParam, BaseWave wave) {
        wave.setTargetBrain(waveParam.getTargetBrain());
        wave.setDownFlowVirtualGroups(waveParam.getDownFlowVirtualGroups());
        wave.setUpFlowVirtualGroups(waveParam.getUpFlowVirtualGroups());
        wave.setAcceptSignalById(waveParam.isAcceptSignalById());
        wave.setAlwaysAcceptSignal(waveParam.isAlwaysAcceptSignal());
        wave.setLogicReplaceParams(waveParam.getLogicReplaceParams());
        wave.setBatchNumber(UUIDUtil.getUUID());
        wave.setWaveBatchNumber(param.getWaveBatchNumber());
        wave.setComments(waveParam.getComments());
        wave.setTemporaryCalculationId(param.getTemporaryCalculationId());
        wave.setUser(param.getUser());
        wave.setSignalProcessParam(waveParam.getSignalProcessParam());
        wave.setConclusionLogicParam(waveParam.getConclusionLogicParam());
        wave.setKeepInfos(waveParam.getKeepInfos());
        wave.setKeepTextCode(waveParam.getKeepTextCode());
        wave.setOpenTextParam(waveParam.getOpenTextParam());
        wave.setTargetPeriodParams(waveParam.getTargetPeriodParams());
        wave.setBiocurrentName(param.getBiocurrentName());
    }

}
