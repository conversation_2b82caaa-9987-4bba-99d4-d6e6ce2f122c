package pub.yuntu.superbrain.domain.model.dsp.constants;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import pub.yuntu.superbrain.domain.model.dsp.param.DspParam;
import pub.yuntu.superbrain.infrastructure.util.JsonUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Description JSON中定义的变量替换
 * @createTime 2023年12月07日 17:12:17
 */
public class DspConstantsOperator {

    @Nullable
    public static DspParam parseDspParamFromText(String jsonContent) {
        // 不转换为业务对象，先通过 JSON 对象处理变量替换
        JSONObject paramJsonObj = JSONUtil.parseObj(jsonContent);
        JSONObject replacedJsonObj = replaceConstants(paramJsonObj);
//        return JSONUtil.toBean(replacedJsonObj, DspParam.class); // 不打印错误信息
        String s = replacedJsonObj.toString();
        return JsonUtil.jsonMapper.fromJson(s, DspParam.class);
    }

    public static DspParam parseDspParamFromText(String jsonContent, String shotJsonContent) {
        JSONObject stencilJsonObj = JSONUtil.parseObj(jsonContent);
        JSONObject shotJsonObj = JSONUtil.parseObj(shotJsonContent);

        // 使用快照中的变量替换模板中的变量
        JSONObject constantsMapFromShot = shotJsonObj.getJSONObject("constantsMap");
        JSONObject constantsMap = stencilJsonObj.getJSONObject("poolParam").getJSONObject("constants").getJSONObject("constantsMap");
        if (null != constantsMap && null != constantsMapFromShot) {
            constantsMap.putAll(constantsMapFromShot);
        }
        // 替换快照中指定的指标
        if (shotJsonObj.containsKey("indexPairs")) {
            JSONArray indexPairsFromShot = shotJsonObj.getJSONArray("indexPairs");
            stencilJsonObj.getJSONObject("poolParam").set("indexPairs", indexPairsFromShot);
        }

        JSONObject replacedJsonObj = replaceConstants(stencilJsonObj);
        // return JSONUtil.toBean(replacedJsonObj, DspParam.class); // 不打印错误信息
        String s = replacedJsonObj.toString();
        return JsonUtil.jsonMapper.fromJson(s, DspParam.class);
    }

    private static JSONObject replaceConstants(JSONObject stencilJsonObj) {
        JSONArray dafeStreams = stencilJsonObj.getJSONArray("dafeStreams");
        JSONObject poolParam = stencilJsonObj.getJSONObject("poolParam");
        // 定义的变量
        JSONObject constantsMap = poolParam.getJSONObject("constants").getJSONObject("constantsMap");
        // 需要被替换的两个部分：dafeStreams、poolParam(排除constants)
        JSONArray newDafeStreams = new JSONArray();
        for (int i = 0; i < dafeStreams.size(); i++) {
            JSONObject dafeStreamsJSONObject = dafeStreams.getJSONObject(i);
            Object replacedDafeStream = replaceConstants(constantsMap, dafeStreamsJSONObject);
            newDafeStreams.put(replacedDafeStream);
        }
        JSONObject newPoolParam = new JSONObject();
        for (String paramKey : poolParam.keySet()) {
            if (StringUtils.equalsIgnoreCase(paramKey, "constants")) {
                // 常量不替换，直接返回
                newPoolParam.put("constants", poolParam.get("constants"));
                continue;
            }

            Object newObj = replaceConstants(constantsMap, poolParam.get(paramKey));
            newPoolParam.put(paramKey, newObj);
        }

        JSONObject newObj = new JSONObject();
        newObj.put("dafeStreams", newDafeStreams);
        newObj.put("poolParam", newPoolParam);
        return newObj;
    }

    private static Object replaceConstants(JSONObject constantsMap, Object originalObj) {
        if (originalObj instanceof JSONObject) {
            return replaceConstants(constantsMap, (JSONObject) originalObj);
        }
        if (originalObj instanceof JSONArray) {
            JSONArray array = (JSONArray) originalObj;
            JSONArray newArray = new JSONArray();
            for (int i = 0; i < array.size(); i++) {
                JSONObject item = (JSONObject) array.get(i);
                newArray.add(replaceConstants(constantsMap, item));
            }
            return newArray;
        }
        return null;
    }

    /**
     * 替换配置的常量
     *
     * @param constantsMap 变量定义
     * @param originalObj  原始对象
     */
    public static Object replaceConstants(JSONObject constantsMap, JSONObject originalObj) {
        // 将constantsMap的 key 集合按字符串长度从长到短排序，先替换长度最长的，避免 key 值中有重叠的字符串被部分替换
        List<String> constantsKeyList = new ArrayList<>(constantsMap.keySet());
        constantsKeyList.sort(Comparator.comparingInt(String::length).reversed());

        // 转换成 JSON 对象
        for (String key : originalObj.keySet()) {
            // log.info("replace key: {}", key);
            // 待处理的对象中属性 key 里面不会存在变量的定义，递归处理每一个属性
            Object valueObj = originalObj.get(key);
            if (valueObj instanceof Integer || valueObj instanceof Long
                    || valueObj instanceof Float || valueObj instanceof Double
                    || valueObj instanceof Boolean) {
                continue;
            }

            if (valueObj instanceof String) {
                // 这里有两种情况，一种是变量在整个字符串中是一部分，另一种是整个字符串仅仅是一个变量名，需要完整替换
                for (String variableKey : constantsKeyList) {
                    Object variable = constantsMap.get(variableKey);
                    // 完整替换
                    if (StringUtils.equalsIgnoreCase(variableKey, valueObj + "")) {
                        valueObj = variable;
                        break;
                    } else {
                        valueObj = StringUtils.replaceIgnoreCase(valueObj + "", variableKey, variable + "");
                    }
                }
                originalObj.put(key, valueObj);
                continue;
            }

            if (valueObj instanceof JSONObject) {
                valueObj = replaceConstants(constantsMap, (JSONObject) valueObj);
                originalObj.put(key, valueObj);
                continue;
            }

            if (valueObj instanceof JSONArray) {
                JSONArray array = (JSONArray) valueObj;
                JSONArray newArray = new JSONArray();
                for (int i = 0; i < array.size(); i++) {
                    Object item = array.get(i);
                    if (item instanceof Integer || item instanceof Long
                            || item instanceof Float || item instanceof Double
                            || item instanceof Boolean) {
                        newArray.put(item);
                        continue;
                    }
                    if (item instanceof String) {
                        String text = item + "";
                        for (String variableKey : constantsKeyList) {
                            Object variable = constantsMap.get(variableKey);
                            text = StringUtils.replaceIgnoreCase(text, variableKey, variable + "");
                        }
                        newArray.put(text);
                    } else if (item instanceof JSONObject) {
                        item = replaceConstants(constantsMap, (JSONObject) item);
                        newArray.put(item);
                    } else {
                        throw new IllegalStateException("暂不支持的结构");
                    }
                }
                originalObj.put(key, newArray);
                continue;
            }

            if (valueObj instanceof JSONNull) {
                originalObj.put(key, null);
                continue;
            }

            originalObj.put(key, valueObj);
        }
        return JSONUtil.toBean(originalObj, JSONObject.class);
    }

}
