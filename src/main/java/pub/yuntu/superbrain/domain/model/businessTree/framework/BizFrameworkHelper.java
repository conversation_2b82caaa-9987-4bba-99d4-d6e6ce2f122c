package pub.yuntu.superbrain.domain.model.businessTree.framework;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import pub.yuntu.superbrain.domain.model.brain.param.BiocurrentParam;
import pub.yuntu.superbrain.domain.model.brain.param.ConclusionLogicParam;
import pub.yuntu.superbrain.domain.model.brain.param.ReplaceParam;
import pub.yuntu.superbrain.domain.model.brain.param.WaveParam;
import pub.yuntu.superbrain.domain.model.brain.param.input.AbstractInput;
import pub.yuntu.superbrain.domain.model.brain.param.input.MysqlInput;
import pub.yuntu.superbrain.domain.model.brain.param.input.NebulaInput;
import pub.yuntu.superbrain.domain.model.data.resultset.NebulaResultSet;
import pub.yuntu.superbrain.domain.model.legacy.rh.param.ViewPointParam;
import pub.yuntu.superbrain.domain.model.legacy.stream.group.StreamGroup;
import pub.yuntu.superbrain.infrastructure.util.JsonUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description BizFramework 辅助工具
 * @createTime 2023年04月11日 09:45:45
 */
@Component
@Slf4j
public class BizFrameworkHelper {

    static final String SCOPE_DELIMITER = ",";

    /**
     * 根据scope定义，过滤需要执行的Grouping
     */
    public static List<StreamGroup> filterGroupingsByScope(List<StreamGroup> groupings, String scopeText) {
        List<String> scopes = parseScopeText(scopeText);
        List<StreamGroup> filteredGroupings = groupings.stream()
                .filter(grouping -> scopes.contains(grouping.getName()))
                .collect(Collectors.toList());
        log.info("配置的Groupings[{}]，通过Scope过滤后保留的Groupings[{}]",
                groupings.stream().map(StreamGroup::getName).collect(Collectors.joining(SCOPE_DELIMITER)),
                filteredGroupings.stream().map(StreamGroup::getName).collect(Collectors.joining(SCOPE_DELIMITER)));
        return filteredGroupings;
    }

    /**
     * 解析scope文本
     * 文本可能是：行业、集团、区域、区域或公司、公司、项目...的组合
     */
    private static List<String> parseScopeText(String scopeText) {
        String[] textArray = scopeText.split(SCOPE_DELIMITER);
        List<String> scopes = Arrays.stream(textArray)
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        log.info("解析scope范围[{}]，结果：[{}]", scopeText, String.join(SCOPE_DELIMITER, scopes));
        return scopes;
    }

    /**
     * 根据scope定义，过滤需要执行的ViewPoints
     */
    public static List<ViewPointParam> filterViewPointsByScope(List<ViewPointParam> viewpoints, String scopeText) {
        List<String> scopes = parseScopeText(scopeText);
        List<ViewPointParam> filteredViewPoints = viewpoints.stream()
                .filter(viewPoint -> scopes.contains(viewPoint.getGroupingName()))
                .collect(Collectors.toList());
        log.info("配置的viewpoints[{}]，通过Scope过滤后保留的viewpoints[{}]",
                viewpoints.stream().map(ViewPointParam::getName).collect(Collectors.joining(SCOPE_DELIMITER)),
                filteredViewPoints.stream().map(ViewPointParam::getName).collect(Collectors.joining(SCOPE_DELIMITER)));
        return filteredViewPoints;
    }

    public void mergePeriods(List<BiocurrentParam> biocurrents, BiocurrentParam result) {
        Map<String, String> periods = new HashMap<>();
        biocurrents.stream()
                .filter(x -> x.getPeriods() != null)
                .forEach(x -> periods.putAll(x.getPeriods()));
        result.setPeriods(periods);
    }

    public void mergeSpecialPeriodNameMap(List<BiocurrentParam> biocurrents, BiocurrentParam result) {
        Map<String, String> specialPeriodNameMap = new HashMap<>();
        biocurrents.stream()
                .filter(x -> x.getSpecialPeriodNameMap() != null)
                .forEach(x -> specialPeriodNameMap.putAll(x.getSpecialPeriodNameMap()));
        result.setSpecialPeriodNameMap(specialPeriodNameMap);
    }

    public void mergeWaves(List<BiocurrentParam> biocurrents, BiocurrentParam result) {
        List<WaveParam> allWaves = new ArrayList<>();
        biocurrents.stream()
                .filter(x -> x.getWaves() != null)
                .forEach(x -> allWaves.addAll(x.getWaves()));

        Map<String, Map<String, List<WaveParam>>> typeAndWavesMap = allWaves.stream()
                .collect(
                        Collectors.groupingBy(WaveParam::getType,
                                Collectors.groupingBy(WaveParam::getTargetBrain)));

        List<WaveParam> outerDataWavesAfterMerge = mergeOuterDataWaves(allWaves, typeAndWavesMap.get("outerDataSequence"));
        List<WaveParam> clearDataWavesAfterMerge = mergeClearDataWaves(allWaves, typeAndWavesMap.get("command"));

        List<WaveParam> finalWaves = new ArrayList<>();
        finalWaves.addAll(outerDataWavesAfterMerge);
        finalWaves.addAll(allWaves);
        finalWaves.addAll(clearDataWavesAfterMerge);

        for (int i = 0; i < finalWaves.size(); i++) {
            WaveParam waveParam = finalWaves.get(i);
            waveParam.setOrder(i + 1);
        }

        result.setWaves(finalWaves);
    }

    public List<WaveParam> mergeOuterDataWaves(List<WaveParam> allWaves,
                                               Map<String, List<WaveParam>> outerDataWaves) {
        List<WaveParam> outerDataWavesAfterMerge = new ArrayList<>();
        if (outerDataWaves == null || outerDataWaves.isEmpty())
            return outerDataWavesAfterMerge;

        for (String targetBrain : outerDataWaves.keySet()) {
            List<WaveParam> waveParams = outerDataWaves.get(targetBrain).stream()
                    .filter(x -> x.getTargetVirtualGroups() != null && !x.getTargetVirtualGroups().isEmpty())
                    .filter(x -> x.getDafeStreams() != null && !x.getDafeStreams().isEmpty())
                    .collect(Collectors.toList());

            if (waveParams.isEmpty())
                continue;
            allWaves.removeAll(waveParams);

            if (waveParams.size() == 1) {
                outerDataWavesAfterMerge.add(waveParams.get(0));
            } else {
                mergeOuterDataWaves(outerDataWavesAfterMerge, targetBrain, waveParams);
            }
        }

        return outerDataWavesAfterMerge;
    }

    public void mergeOuterDataWaves(List<WaveParam> outerDataWavesAfterMerge,
                                    String targetBrain,
                                    List<WaveParam> waveParams) {
        Map<String, List<WaveParam>> waveMap = waveParams.stream()
                .collect(Collectors.groupingBy(x -> x.isAcceptSignalById() + "_" + x.isAlwaysAcceptSignal() + "_" + (x.getConclusionLogicParam() == null)));

        for (List<WaveParam> list : waveMap.values()) {
            Set<String> targetVirtualGroups = new LinkedHashSet<>();
            Set<String> downFlowVirtualGroups = new LinkedHashSet<>();
            Set<String> upFlowVirtualGroups = new LinkedHashSet<>();
            Set<String> triggerLogics = new LinkedHashSet<>();
            List<ReplaceParam> logicReplaceParams = new ArrayList<>();
            List<AbstractInput> dafeStreams = new ArrayList<>();
            Set<String> comments = new LinkedHashSet<>();
            // SignalProcessParam 的条件间只支持“且”的关系，无法合并
//            SignalProcessParam signalProcessParam = new SignalProcessParam();

            // 不同的电流可能会都用到同一个codeCondition参数（如startWith），无法合并conclusionLogicParam.codeCondition
            ConclusionLogicParam conclusionLogicParam = new ConclusionLogicParam();
            conclusionLogicParam.setNames(new ArrayList<>());
            conclusionLogicParam.setCodes(new ArrayList<>());

            list.forEach(x -> {
                addAllIfNotNull(targetVirtualGroups, x.getTargetVirtualGroups());
                addAllIfNotNull(downFlowVirtualGroups, x.getDownFlowVirtualGroups());
                addAllIfNotNull(upFlowVirtualGroups, x.getUpFlowVirtualGroups());
                addAllIfNotNull(triggerLogics, x.getTriggerLogics());
                addAllIfNotNull(logicReplaceParams, x.getLogicReplaceParams());
                addAllIfNotNull(dafeStreams, x.getDafeStreams());

                if (StringUtils.isNotBlank(x.getComments())) {
                    comments.add(x.getComments());
                }

                if (x.getConclusionLogicParam() != null) {
                    addAllIfNotNull(conclusionLogicParam.getNames(), x.getConclusionLogicParam().getNames());
                    addAllIfNotNull(conclusionLogicParam.getCodes(), x.getConclusionLogicParam().getCodes());
                }
            });

            // merge dafeStreams
            List<AbstractInput> dafeStreamsAfterMerge = mergeDafeStreams(dafeStreams);

            WaveParam merge = new WaveParam();
            merge.setType("outerDataSequence");
            merge.setTargetBrain(targetBrain);
            merge.setTargetVirtualGroups(new ArrayList<>(targetVirtualGroups));
            merge.setDownFlowVirtualGroups(new ArrayList<>(downFlowVirtualGroups));
            merge.setUpFlowVirtualGroups(new ArrayList<>(upFlowVirtualGroups));
            merge.setAcceptSignalById(list.get(0).isAcceptSignalById());
            merge.setAlwaysAcceptSignal(list.get(0).isAlwaysAcceptSignal());
            merge.setTriggerLogics(new ArrayList<>(triggerLogics));
            merge.setLogicReplaceParams(logicReplaceParams);
            merge.setDafeStreams(dafeStreamsAfterMerge);
            merge.setConclusionLogicParam(conclusionLogicParam);
            merge.setComments(String.format("outerDataSequence: targetBrain=%s, targetVirtualGroups=%s, comments=%s",
                    merge.getTargetBrain(), merge.getTargetVirtualGroups(), comments));

            outerDataWavesAfterMerge.add(merge);
        }
    }

    private void addAllIfNotNull(Collection c1, Collection c2) {
        if (c1 != null && c2 != null) {
            c1.addAll(c2);
        }
    }

    public List<AbstractInput> mergeDafeStreams(List<AbstractInput> dafeStreams) {
        List<AbstractInput> dafeStreamsAfterMerge = new ArrayList<>();
        if (dafeStreams.size() == 1) {
            dafeStreamsAfterMerge = dafeStreams;
        } else {
            Map<String, List<AbstractInput>> inputMap = dafeStreams.stream().collect(Collectors.groupingBy(x -> x.getClass().getSimpleName()));
            List<AbstractInput> mysqlInputs = mergeMysqlInputs(inputMap.get("MysqlInput"));
            List<AbstractInput> nebulaInputs = mergeNebulaInputs(inputMap.get("NebulaInput"));
            dafeStreamsAfterMerge.addAll(mysqlInputs);
            dafeStreamsAfterMerge.addAll(nebulaInputs);
        }
        return dafeStreamsAfterMerge;
    }

    public List<AbstractInput> mergeMysqlInputs(List<AbstractInput> mysqlInputs) {
        List<AbstractInput> result = new ArrayList<>();
        if (mysqlInputs == null)
            return result;

        Map<String, List<MysqlInput>> inputMap = mysqlInputs.stream()
                .map(x -> (MysqlInput) x)
                .collect(Collectors.groupingBy(x -> x.getSignalType() + "_" +
                        StringUtils.join(sortStrArray(x.getTableNames()), "#") + "_" +
                        StringUtils.join(x.getColumns(), "#") + "_" +
                        x.getCheckCondition()));

        for (List<MysqlInput> inputs : inputMap.values()) {
            if (inputs.size() == 1) {
                result.add(inputs.get(0));
            } else {
                List<String> dataConditions = new ArrayList<>();
                inputs.stream()
                        .filter(x -> StringUtils.isNotBlank(x.getDataCondition()))
                        .forEach(x -> dataConditions.add("(" + x.getDataCondition() + ")"));

                MysqlInput merge = inputs.get(0).deepClone();
                merge.setDataCondition(StringUtils.join(dataConditions, " or "));
                result.add(merge);
            }
        }

        result.forEach(x -> x.setType("mysql"));

        return result;
    }

    private String[] sortStrArray(String[] array) {
        if (array != null && array.length > 1) {
            Arrays.sort(array);
        }
        return array;
    }

    public List<AbstractInput> mergeNebulaInputs(List<AbstractInput> nebulaInputs) {
        List<AbstractInput> result = new ArrayList<>();
        if (nebulaInputs == null)
            return result;

        Map<String, Map<String, List<NebulaInput>>> inputMap = nebulaInputs.stream()
                .map(x -> (NebulaInput) x)
                .collect(
                        Collectors.groupingBy(NebulaInput::getQueryType,
                                Collectors.groupingBy(x -> x.getSignalType() + "_" + x.getTag() + "_" + x.getListType())));

        Map<String, List<NebulaInput>> convertNodeMap = inputMap.get("convertNode");
        Map<String, List<NebulaInput>> conditionMap = inputMap.get("condition");

        if (convertNodeMap != null) {
            for (List<NebulaInput> inputs : convertNodeMap.values()) {
                if (inputs.size() == 1) {
                    result.add(inputs.get(0));

                } else {
                    List<String> queryNgqls = new ArrayList<>();
                    inputs.stream()
                            .filter(x -> StringUtils.isNotBlank(x.getQueryNgql()))
                            .forEach(x -> queryNgqls.add(x.getQueryNgql().replaceAll(";", "")));

                    NebulaInput merge = inputs.get(0).deepClone();
                    merge.setQueryNgql(StringUtils.join(queryNgqls, " union "));
                    result.add(merge);
                }
            }
        }

        if (conditionMap != null) {
            for (List<NebulaInput> inputs : conditionMap.values()) {
                if (inputs.size() == 1) {
                    result.add(inputs.get(0));

                } else {
                    List<String> queryNgqls = new ArrayList<>();
                    inputs.stream()
                            .map(NebulaResultSet::prepareNgqlListByCondition)
                            .filter(Objects::nonNull)
                            .forEach(queryNgqls::addAll);

                    NebulaInput merge = inputs.get(0).deepClone();
                    merge.setQueryType("convertNode");
                    merge.setQueryNgql(StringUtils.join(queryNgqls, " union "));
                    merge.setConditions(null);
                    result.add(merge);
                }
            }
        }

        result.forEach(x -> x.setType("nebula"));

        return result;
    }

    public List<WaveParam> mergeClearDataWaves(List<WaveParam> allWaves,
                                               Map<String, List<WaveParam>> commandWaves) {
        List<WaveParam> clearDataWavesAfterMerge = new ArrayList<>();
        if (commandWaves == null || commandWaves.isEmpty())
            return clearDataWavesAfterMerge;

        for (String targetBrain : commandWaves.keySet()) {
            List<WaveParam> waveParams = commandWaves.get(targetBrain).stream()
                    .filter(x -> StringUtils.equals(x.getCommand(), "clearData"))
                    .filter(x -> x.getTargetVirtualGroups() != null && !x.getTargetVirtualGroups().isEmpty())
                    .collect(Collectors.toList());

            if (waveParams.isEmpty())
                continue;
            allWaves.removeAll(waveParams);

            if (waveParams.size() == 1) {
                clearDataWavesAfterMerge.add(waveParams.get(0));

            } else {
                Set<String> targetVirtualGroups = new LinkedHashSet<>();
                waveParams.forEach(x -> targetVirtualGroups.addAll(x.getTargetVirtualGroups()));

                WaveParam merge = new WaveParam();
                merge.setType("command");
                merge.setTargetBrain(targetBrain);
                merge.setTargetVirtualGroups(new ArrayList<>(targetVirtualGroups));
                merge.setCommand("clearData");
                merge.setComments(String.format("clearData: targetBrain=%s, targetVirtualGroups=%s", merge.getTargetBrain(), merge.getTargetVirtualGroups()));

                clearDataWavesAfterMerge.add(merge);
            }
        }

        return clearDataWavesAfterMerge;
    }

    public String addJsonConfigInfo(BizParam param) {
        Map<Object, Object> jsonConfig = JsonUtil.jsonMapper.fromJson(param.getSnapshot().getJsonConfig(), Map.class);
        jsonConfig.put("bizInstanceId", param.getBizInstance().getId());
        jsonConfig.put("bizInstanceName", param.getBizInstance().getName());
        jsonConfig.put("bizInstanceSnapshotId", param.getSnapshot().getId());
        jsonConfig.put("bizInstanceSnapshotName", param.getSnapshot().getName());
        return JsonUtil.jsonMapper.toJson(jsonConfig);
    }
}
