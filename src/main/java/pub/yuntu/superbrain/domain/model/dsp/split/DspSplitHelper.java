package pub.yuntu.superbrain.domain.model.dsp.split;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.foundation.id.IdGenerator;
import pub.yuntu.foundation.persistence.TableDao;
import pub.yuntu.superbrain.domain.dto.*;
import pub.yuntu.superbrain.domain.mapstruct.*;
import pub.yuntu.superbrain.domain.model.dsp.space.DspSpace;
import pub.yuntu.superbrain.domain.model.dsp.task.DspLine;
import pub.yuntu.superbrain.domain.model.dsp.task.DspTask;
import pub.yuntu.superbrain.domain.model.dsp.task.TaskContext;
import pub.yuntu.superbrain.domain.model.legacy.net.lattice.NetLatticeHelper;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgCategory;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgCategoryID;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgCategoryTier;
import pub.yuntu.superbrain.domain.model.legacy.split.AllianceSplitGroup;
import pub.yuntu.superbrain.domain.model.legacy.split.AllianceSplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitGroup;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.standard.StandardParam;
import pub.yuntu.superbrain.domain.model.standard.StandardAttributeEnum;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.*;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static pub.yuntu.superbrain.domain.model.dsp.variable.BaseVariable.getDoubleValue;

/**
 * <AUTHOR>
 * @Description 切分辅助类
 * @createTime 2023年08月18日 17:02:04
 */
@Slf4j
public class DspSplitHelper {

    public static SplitGroupDTO loadOrgGroup(SplitGroupRepository splitGroupRepository,
                                             OrgCategoryTierRepository orgCategoryTierRepository,
                                             Long groupNid) {
        long start = System.currentTimeMillis();
        log.info("开始加载组织结构切分组，NID={}", groupNid);
        List<SplitGroup> groups = splitGroupRepository.findBy_id(groupNid);
        if (groups.isEmpty()) {
            throw new NullPointerException("SplitGroup is Null, ID=" + groupNid);
        }
        SplitGroup splitGroup = groups.get(0);
        SplitGroupDTO dto = SplitGroupMapper.INSTANCE.toDto(splitGroup);
        List<OrgCategoryTier> orgCategoryTiers = orgCategoryTierRepository.searchByCategoryIDAndTier(new OrgCategoryID(splitGroup.getOrgCategoryID()), splitGroup.getOrgCategoryTierLevel());
        if (orgCategoryTiers != null && !orgCategoryTiers.isEmpty()) {
            dto.setOrgCategoryStandardTierLevel(orgCategoryTiers.get(0).getStandardTier());
        }
        splitGroup = null;
        log.info("组织结构切分组[{}]加载完成，用时：{}", dto.getName(), DateUtil.calPassedTime(start));
        return dto;
    }

    public static SplitGroupDTO loadMixedOrgGroup(SplitGroupRepository splitGroupRepository, String groupId, String respondentId) {
        long start = System.currentTimeMillis();
        log.debug("开始加载组织结构混合切分组，组织结构切分组 ID={}，属性 ID={}", groupId, respondentId);
        List<SplitGroup> mixedGroupListFromDb = splitGroupRepository
                .findByMixedGroupIDAndRespondent1IDAndRespondent2IDIsNull(groupId, respondentId);
        List<SplitGroupDTO> mixedGroupList = SplitGroupMapper.INSTANCE.toDto(mixedGroupListFromDb);
        mixedGroupListFromDb.clear();

        if (mixedGroupList.isEmpty()) {
            log.debug("没有查询到混合切分，切分组ID={}，属性 ID={}", groupId, respondentId);
            return null;
        }

        SplitGroupDTO mixedGroup = mixedGroupList.get(0);
        log.info("组织结构混合切分组[{}]加载完成，用时：{}", mixedGroup.getName(), DateUtil.calPassedTime(start));
        return mixedGroup;
    }

    public static List<SplitUnitDTO> loadOrgUnits(SplitUnitRepository splitUnitRepository, SplitGroupDTO group) {
        long start = System.currentTimeMillis();
        log.debug("开始加载组织结构切分组[{}]下的切分单元", group.getName());

        List<SplitUnit> units = splitUnitRepository.findByGroupID(group.getId());
        if (units.isEmpty()) {
            log.warn("没有查询到切分组[{} - {}]的切分单元", group.get_id(), group.getName());
        }
        List<SplitUnitDTO> dtoList = SplitUnitMapper.INSTANCE.toDto(units);
        units.clear();
        log.debug("组织结构切分组[{}]下的切分单元加载完成，用时：{}", group.getName(), DateUtil.calPassedTime(start));
        return dtoList;
    }

    public static OrgCategoryDTO loadOrgCategory(OrgCategoryRepository orgCategoryRepository, String id) {
        long start = System.currentTimeMillis();
        log.info("开始加载组织结构分类，ID={}", id);
        OrgCategory categoryEntity = orgCategoryRepository.findById(id)
                .orElseThrow(() -> new NullPointerException("OrgCategory is Null, ID=" + id));
        OrgCategoryDTO category = OrgCategoryMapper.INSTANCE.toDto(categoryEntity);
        categoryEntity = null;
        return category;
    }

    public static StandardParamDTO loadStandardParam(StandardParamRepository standardParamRepository, String orgCategoryId) {
        List<StandardParam> standardParams = standardParamRepository.searchByCustomerOrgCategoryId(orgCategoryId);
        if (standardParams.isEmpty()) {
            throw new NullPointerException("没有查询到标准参数，组织结构分类ID=" + orgCategoryId);
        }
        StandardParamDTO standardParam = StandardParamMapper.INSTANCE.toDto(standardParams.get(0));
        standardParams.clear();
        return standardParam;
    }

    /**
     * 从 YT-Intelligence LegacyOrgGrouping 迁移，部分修改
     */
    public static DspSplit loadMixedGroup(SplitGroupRepository splitGroupRepository, SplitUnitRepository splitUnitRepository,
                                          SplitGroupDTO orgGroup, StandardAttributeEnum attribute, StandardParamDTO standardParam) {
        String respondentId_1 = NetLatticeHelper.getRespondentId(attribute, standardParam);
        // if (null != msdType) {
        //     switch (msdType) {
        //         case MSD_SALES:
        //             groupId = this.getMsdSalesId();
        //             break;
        //         case MSD_PROPERTY:
        //             groupId = this.getMsdPropertyId();
        //             break;
        //     }
        // }
        SplitGroupDTO mixedGroup = loadMixedOrgGroup(splitGroupRepository, orgGroup.getId(), respondentId_1);
        if (null == mixedGroup) return null;
        List<SplitUnitDTO> splitUnits = loadOrgUnits(splitUnitRepository, mixedGroup);
        // 过滤当前属性的切分单元
        splitUnits = splitUnits.stream()
                .filter(unit -> unit.getRespondentOption1Name().equals(attribute.getPureOptionName()))
                .collect(Collectors.toList());

        DspSplit mixedSplit = new DspSplit();
        mixedSplit.setGroupId(mixedGroup.get_id() + "");
        mixedSplit.setIndustry(false);
        mixedSplit.setOrgGroup(mixedGroup);
        mixedSplit.setOrgUnits(splitUnits);
        return mixedSplit;
    }

    public static DspSplit loadMixedGroup(SplitGroupRepository splitGroupRepository, SplitUnitRepository splitUnitRepository,
                                          SplitGroupDTO orgGroup, String[] attributes, StandardParamDTO standardParam) {
        List<String> respondentIds = new ArrayList<>();
        for (String attribute : attributes) {
            StandardAttributeEnum attributeEnum = StandardAttributeEnum.getInstanceByName(attribute);
            String respondentId = NetLatticeHelper.getRespondentId(attributeEnum, standardParam);
            respondentIds.add(respondentId);
        }

        List<SplitGroup> mixedSplitGroups = new ArrayList<>();
        if (respondentIds.size() == 1) {
            mixedSplitGroups = splitGroupRepository
                    .findByMixedGroupIDAndRespondent1IDAndRespondent2IDIsNull(orgGroup.getId(), respondentIds.get(0));
        } else if (respondentIds.size() == 2) {
            mixedSplitGroups = splitGroupRepository
                    .findByTwoRespondentOptions(orgGroup.getId(), respondentIds.get(0), respondentIds.get(1));
        } else if (respondentIds.size() >= 3) {
            mixedSplitGroups = splitGroupRepository
                    .findByThreeRespondentIDs(orgGroup.getId(), respondentIds);
        }

        if (mixedSplitGroups.size() == 0)
            return null;

        SplitGroup mixedGroupEntity = mixedSplitGroups.get(0);
        SplitGroupDTO mixedGroup = SplitGroupMapper.INSTANCE.toDto(mixedGroupEntity);
        mixedSplitGroups.clear();
        mixedGroupEntity = null;
        List<SplitUnitDTO> splitUnits = loadOrgUnits(splitUnitRepository, mixedGroup);

        DspSplit mixedSplit = new DspSplit();
        mixedSplit.setGroupId(mixedGroup.get_id() + "");
        mixedSplit.setIndustry(false);
        mixedSplit.setOrgGroup(mixedGroup);
        mixedSplit.setOrgUnits(splitUnits);
        return mixedSplit;
    }

    public static String transformAttribute(SplitUnitDTO unit, String attribute) {
        if (!StringUtils.contains(unit.getExpression(), "d_owner_type_four")) {
            if (StringUtils.equalsIgnoreCase(attribute, "老业主")) {
                if (StringUtils.contains(unit.getExpression(), "d_owner_type_seven_plus")) {
                    attribute = "老业主1";
                } else {
                    attribute = "老业主#7";
                }
            } else if (StringUtils.equalsIgnoreCase(attribute, "稳定期")) {
                attribute = "稳定期#7";
            }
        }
        return attribute;
    }

    public static String transformAttribute(AllianceSplitUnitDTO unit) {
        String attribute = unit.getExtensionReferValues();
        if (StringUtils.equalsIgnoreCase(attribute, "稳定期")
                && StringUtils.containsIgnoreCase(unit.getExpression(), "owner_type_seven_plus")) {
            attribute = "稳定期#7";
        }
        return attribute;
    }

    public static boolean mixedGroupIsOwner4(SplitGroupDTO group) {
        String respondent1EnglishName = group.getRespondent1EnglishName();
        if (StringUtils.isBlank(respondent1EnglishName)) {
            return false;
        }
        return StringUtils.startsWith(respondent1EnglishName, "d_owner_type_four");
    }

    public static boolean mixedGroupIsOwner7(SplitGroupDTO group) {
        String respondent1EnglishName = group.getRespondent1EnglishName();
        if (StringUtils.isBlank(respondent1EnglishName)) {
            return false;
        }
        return StringUtils.startsWith(respondent1EnglishName, "d_owner_type_seven_plus")
                || StringUtils.startsWith(respondent1EnglishName, "d_zssk_yzlxxf");
    }

    public static boolean isAttributeType(String text) {
        List<String> types = Lists.newArrayList("4类业主", "四类业主", "7类业主", "七类业主", "房屋类型", "装修类型", "楼盘档次", "物业费分段");
        return types.contains(text);
    }

    public static List<String> getAttributesByType(String type) {
        List<StandardAttributeEnum> enumList = getEnumsByType(type);
        return enumList.stream()
                .map(StandardAttributeEnum::getPureOptionName)
                .collect(Collectors.toList());
    }

    public static List<String> getAttributesByTypeByIndustry(String type) {
        List<StandardAttributeEnum> enumList = getEnumsByType(type);
        return enumList.stream()
                .map(StandardAttributeEnum::getPureOptionNameByIndustry)
                .collect(Collectors.toList());
    }

    public static List<StandardAttributeEnum> getEnumsByType(String type) {
        List<StandardAttributeEnum> enumList;
        switch (type) {
            case "4类业主":
            case "四类业主":
                enumList = StandardAttributeEnum.getByType("OWNER_FOUR");
                break;
            case "7类业主":
            case "七类业主":
                enumList = StandardAttributeEnum.getByType("OWNER_SEVEN");
                break;
            case "房屋类型":
                enumList = StandardAttributeEnum.getByType("HOUSE_TYPE");
                break;
            case "装修类型":
                enumList = StandardAttributeEnum.getByType("DECORATION_TYPE");
                break;
            case "楼盘档次":
                enumList = StandardAttributeEnum.getByType("BUILDINGGRADE");
                break;
            case "物业费分段":
                enumList = StandardAttributeEnum.getByType("PROPERTYCOSTLEVEL");
                break;
            default:
                throw new IllegalArgumentException("不支持的属性类型");
        }
        return enumList;
    }

    public static AllianceSplitGroupDTO loadAllianceOrgGroup(AllianceSplitGroupRepository allianceSplitGroupRepository, Long groupNid) {
        long start = System.currentTimeMillis();
        log.info("开始加载行业组织结构切分组，NID={}", groupNid);
        List<AllianceSplitGroup> groups = allianceSplitGroupRepository.findBy_id(groupNid);
        if (groups.isEmpty()) {
            throw new NullPointerException("SplitGroup is Null, ID=" + groupNid);
        }
        AllianceSplitGroup splitGroup = groups.get(0);
        AllianceSplitGroupDTO dto = AllianceSplitGroupMapper.INSTANCE.toDto(splitGroup);
        splitGroup = null;
        log.info("行业组织结构切分组[{}]加载完成，用时：{}", dto.getGroupName(), DateUtil.calPassedTime(start));
        return dto;
    }

    /**
     * FixMe: 当前 dataPeriod 在 dataset_1_ana 没数的切分将不被创建，但这些切分在其他表(tag)或其他时间可能有数
     */
    public static List<AllianceSplitUnitDTO> loadAllianceUnits(AllianceSplitGroupRepository allianceSplitGroupRepository,
                                                               TableDao tableDao,
                                                               EntityManager entityManager,
                                                               long splitGroupId,
                                                               String dataPeriod) {
        List<AllianceSplitUnit> allianceSplitUnitList = new ArrayList<>();
        AllianceSplitGroup allianceSplitGroup = allianceSplitGroupRepository.findBy_id(splitGroupId).get(0);
        long start = System.currentTimeMillis();
        log.info("开始加载行业组织结构切分组[{}]下的切分单元", allianceSplitGroup.getGroupName());
        String targetTable = "dataset_1_ana";
        String conditions = "data_period = '" + dataPeriod + "' AND split_group_id = " + splitGroupId;
        String[] columns = new String[]{"unit_name", "extension_refer_values", "parent_unit_name", "main_unit_name"};
        // 查询结果表中的切分单元名称
        List<Map<String, String>> unitNameList = tableDao.findDistinctValueOfColumnsWithGroupBy(entityManager, targetTable, columns, "", "unit_name", conditions);
        // 转换成AllianceSplitUnit
        allianceSplitUnitList.addAll(transformToSplitUnit(allianceSplitGroup, unitNameList));
        List<AllianceSplitUnitDTO> dtoList = AllianceSplitUnitMapper.INSTANCE.toDto(allianceSplitUnitList);
        allianceSplitUnitList.clear();
        log.info("组织结构切分组[{}]下的切分单元加载完成，用时：{}", allianceSplitGroup.getGroupName(), DateUtil.calPassedTime(start));
        return dtoList;
    }

    private static List<AllianceSplitUnit> transformToSplitUnit(AllianceSplitGroup allianceSplitGroup, List<Map<String, String>> unitNameList) {
        List<AllianceSplitUnit> allianceSplitUnitList = new ArrayList<>();
        String groupExtensionReferFields = allianceSplitGroup.getExtensionReferFields();
        for (Map<String, String> unitNameMap : unitNameList) {
            AllianceSplitUnit allianceSplitUnit = new AllianceSplitUnit();
            String unitName = unitNameMap.get("unit_name");
            String extensionReferValues = unitNameMap.get("extension_refer_values");
            String parentUnitName = unitNameMap.get("parent_unit_name");
            String mainUnitName = unitNameMap.get("main_unit_name");
            allianceSplitUnit.setName(unitName);
            allianceSplitUnit.setGroupID(allianceSplitGroup.getId());
            // transformAttribute中要根据表达式区分7类的稳定期
            if (groupExtensionReferFields != null && StringUtils.containsIgnoreCase(groupExtensionReferFields, "owner_type_seven_plus"))
                allianceSplitUnit.setExpression("owner_type_seven_plus");
            allianceSplitUnit.setExtensionReferValues(extensionReferValues.equalsIgnoreCase("null") ? null : extensionReferValues);
            allianceSplitUnit.setSplitReferValues(mainUnitName);
            allianceSplitUnit.setParentUnitName(parentUnitName.equalsIgnoreCase("null") ? null : parentUnitName);
            allianceSplitUnit.setMainUnitName(mainUnitName);
            allianceSplitUnit.setSplitUnitID(IdGenerator.generateUnitIdStr(allianceSplitGroup.get_id(), unitName));
            allianceSplitUnit.setId(IdGenerator.generateUnitIdStr(allianceSplitGroup.get_id(), unitName));
            allianceSplitUnit.set_id(IdGenerator.generateUnitDbIdByMurmur64(allianceSplitGroup.get_id(), unitName));
            allianceSplitUnitList.add(allianceSplitUnit);
        }
        return allianceSplitUnitList;
    }

    public static List<AllianceSplitUnitDTO> loadAllianceMixedGroup(AllianceSplitGroupRepository allianceSplitGroupRepository,
                                                                    AllianceSplitGroupDTO mainAllianceGroup,
                                                                    String attribute,
                                                                    Long groupNId,
                                                                    List<AllianceSplitUnitDTO> allianceUnits) {
        long start = System.currentTimeMillis();
        log.info("开始加载行业组织结构切分组【{}】下属性为【{}】的切分单元", mainAllianceGroup.getGroupName(), attribute);
        List<AllianceSplitUnit> allianceSplitUnitList = new ArrayList<>();
        attribute = attribute.replace("#7", "");
        AllianceSplitGroup allianceSplitGroup = allianceSplitGroupRepository.findBy_id(groupNId).get(0);
        String groupExtensionReferFields = allianceSplitGroup.getExtensionReferFields();

        // 因为切分单元ID为hash结果所以不用查询数据库
        for (AllianceSplitUnitDTO allianceUnit : allianceUnits) {
            AllianceSplitUnit newAllianceSplitUnit = new AllianceSplitUnit();
            String mainUnitName = allianceUnit.getName();
            String unitName = mainUnitName + "-" + attribute;
            String parentUnitName = StringUtils.isBlank(allianceUnit.getParentUnitName()) ? null : allianceUnit.getParentUnitName() + "-" + attribute;
            String extensionReferValues = attribute.replace("-", ",");

            newAllianceSplitUnit.setName(unitName);
            newAllianceSplitUnit.setGroupID(allianceSplitGroup.getId());
            // transformAttribute中要根据表达式区分7类的稳定期
            if (groupExtensionReferFields != null && StringUtils.containsIgnoreCase(groupExtensionReferFields, "owner_type_seven_plus"))
                newAllianceSplitUnit.setExpression("owner_type_seven_plus");
            newAllianceSplitUnit.setExtensionReferValues(extensionReferValues);
            newAllianceSplitUnit.setSplitReferValues(mainUnitName);
            newAllianceSplitUnit.setParentUnitName(parentUnitName);
            newAllianceSplitUnit.setMainUnitName(mainUnitName);
            newAllianceSplitUnit.setSplitUnitID(IdGenerator.generateUnitIdStr(allianceSplitGroup.get_id(), unitName));
            newAllianceSplitUnit.setId(IdGenerator.generateUnitIdStr(allianceSplitGroup.get_id(), unitName));
            newAllianceSplitUnit.set_id(IdGenerator.generateUnitDbIdByMurmur64(allianceSplitGroup.get_id(), unitName));
            allianceSplitUnitList.add(newAllianceSplitUnit);
        }
        List<AllianceSplitUnitDTO> dtoList = AllianceSplitUnitMapper.INSTANCE.toDto(allianceSplitUnitList);
        allianceSplitUnitList.clear();
        log.info("行业组织结构切分组【{}】下属性为【{}】的切分单元加载完成，用时：{}", mainAllianceGroup.getGroupName(), attribute, DateUtil.calPassedTime(start));
        return dtoList;
    }

    public static String getUnitName(FeatureSplitUnitDTO unit) {
        if (null == unit) return "";

        if (unit instanceof SplitUnitDTO) {
            return ((SplitUnitDTO) unit).getName();
        }
        if (unit instanceof AllianceSplitUnitDTO) {
            return ((AllianceSplitUnitDTO) unit).getName();
        }
        if (StringUtils.isNotBlank(unit.getOrgUnitName())) {
            return unit.getOrgUnitName();
        }
        throw new IllegalArgumentException("unit must be SplitUnitDTO or AllianceSplitUnitDTO");
    }

    public static String getGroupName(DspSplit group) {
        if (null == group) return "";
        return group.isIndustry() ? group.getAllianceGroup().getGroupName() : group.getOrgGroup().getName();
    }

    public static String getGroupId(DspSplit group) {
        if (null == group) return "";
        return group.isIndustry() ? group.getAllianceGroup().getId() : group.getOrgGroup().getId();
    }

    public static Long getGroupNId(DspSplit group) {
        if (null == group) return null;
        return group.isIndustry() ? group.getAllianceGroup().get_id() : group.getOrgGroup().get_id();
    }

    public static String translateAttribute(String attribute) {
        if (!StringUtils.startsWith(attribute, "total") && !StringUtils.startsWith(attribute, "owner")) {
            return attribute;
        }

        String result;
        switch (attribute) {
            case "total":
                result = "总体";
                break;
            case "owner4zhun":
                result = "准业主";
                break;
            case "owner4mo":
                result = "磨合期";
                break;
            case "owner4wen":
                result = "稳定期";
                break;
            case "owner4lao":
                result = "老业主";
                break;
            case "owner7zhun1":
                result = "准业主1";
                break;
            case "owner7zhun2":
                result = "准业主2";
                break;
            case "owner7zhun3":
                result = "准业主3";
                break;
            case "owner7mo1":
                result = "磨合期1";
                break;
            case "owner7mo2":
                result = "磨合期2";
                break;
            case "owner7wen":
                result = "稳定期";
                break;
            case "owner7lao1":
                result = "老业主1";
                break;
            case "owner7lao2":
                result = "老业主2";
                break;
            case "owner7lao3":
                result = "老业主3";
                break;
            default:
                throw new IllegalArgumentException("不支持的属性：" + attribute);
        }
        return result;
    }

    public static DspSplit findGroupByUnit(DspLine line, FeatureSplitUnitDTO unit) {
        DspSplit mainGroup = line.getTask().getMainSplitGroup();
        if (unit instanceof AllianceSplitUnitDTO) {
            return mainGroup;
        }

        if (StringUtils.equalsIgnoreCase(mainGroup.getOrgGroup().getId(), unit.getGroupID())) {
            return mainGroup;
        }
        List<DspSplit> benchMarkGroups = line.getTask().getBenchMarkGroups();
        if (null == benchMarkGroups || benchMarkGroups.isEmpty()) {
            throw new IllegalStateException("没有找到切分单元匹配的组，切分单元：" + unit.getName());
        }
        for (DspSplit benchMarkGroup : benchMarkGroups) {
            if (StringUtils.equalsIgnoreCase(benchMarkGroup.getOrgGroup().getId(), unit.getGroupID())) {
                return benchMarkGroup;
            }
        }
        return null;
    }

    public static List<FeatureSplitUnitDTO> findGroupUnit(DspTask task, String group) {
        if (StringUtils.isBlank(group) || StringUtils.equalsIgnoreCase(group, "main") || StringUtils.equalsIgnoreCase(group, "$main")) {
            return task.getSplitGroupUnits();
        }
        if (StringUtils.equalsIgnoreCase(group, "benchMark1") || StringUtils.equalsIgnoreCase(group, "$benchMark1")) {
            return task.getBenchMarkGroup1Units();
        }
        if (StringUtils.equalsIgnoreCase(group, "benchMark2") || StringUtils.equalsIgnoreCase(group, "$benchMark2")) {
            return task.getBenchMarkGroup2Units();
        }
        if (StringUtils.equalsIgnoreCase(group, "benchMark3") || StringUtils.equalsIgnoreCase(group, "$benchMark3")) {
            return task.getBenchMarkGroup3Units();
        }
        if (StringUtils.equalsIgnoreCase(group, "benchMark4") || StringUtils.equalsIgnoreCase(group, "$benchMark4")) {
            return task.getBenchMarkGroup4Units();
        }
        return null;
    }

    public static List<FeatureSplitUnitDTO> sortSplit(TaskContext context, List<FeatureSplitUnitDTO> units,
                                                      String sortName, String orderBy, String spaceKey) {
        if (StringUtils.isBlank(sortName)) return units;

        // 先剔除没有值的 unit
        List<FeatureSplitUnitDTO> filteredUnits = units.stream()
                .filter(unit -> getDoubleValue(context, sortName, unit) != null)
                .collect(Collectors.toList());
        log.debug("排序前：{}", filteredUnits.stream()
                .map(unit -> String.format("%s:%s", DspSplitHelper.getUnitName(unit), getDoubleValue(context, sortName, unit)))
                .collect(Collectors.joining(",")));

        Comparator<FeatureSplitUnitDTO> comparing = Comparator.comparing(unit -> {
            Double doubleValue = getDoubleValue(context, sortName, unit);
            log.trace("[{}] - {}: {}", DspSplitHelper.getUnitName(unit), sortName, doubleValue);
            if (null == doubleValue) {
                throw new NullPointerException(String.format("[%s]中没有找到[%s]的值，无法进行比较。", DspSplitHelper.getUnitName(unit), sortName));
            }
            return doubleValue;
        });
        filteredUnits = filteredUnits.stream().sorted(comparing).collect(Collectors.toList());

        if (null != orderBy) {
            if (StringUtils.equalsIgnoreCase(orderBy, "asc")) {
                Collections.reverse(filteredUnits);
            }
        }
        log.debug("排序后：{}", filteredUnits.stream()
                .map(unit -> String.format("%s:%s", DspSplitHelper.getUnitName(unit), getDoubleValue(context, sortName, unit)))
                .collect(Collectors.joining(",")));

        // 排序的结果存入 space
        DspSpace lineSpace = context.getLineSpace();
        String mainPeriod = context.getDspLine().getPeriodParam().getMainPeriod();
        String singleIndexId = context.getDspLine().getIndexParam().getSingleIndexId();
        for (int i = 0; i < filteredUnits.size(); i++) {
            FeatureSplitUnitDTO unit = filteredUnits.get(i);
            ConcurrentHashMap variableMap = lineSpace.findVariableMap(unit, mainPeriod, singleIndexId);
            variableMap.put(spaceKey, i + 1);
        }

        return filteredUnits;
    }

}
