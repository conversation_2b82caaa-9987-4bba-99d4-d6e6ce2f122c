package pub.yuntu.superbrain.domain.model.keyPath.fetch;

import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import pub.yuntu.superbrain.domain.model.es.ConclusionData;
import pub.yuntu.superbrain.domain.model.dsp.conclusionData.ConclusionDataQueryCriteria;
import pub.yuntu.superbrain.domain.model.keyPath.KeyPathContext;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 从 ElasticSearch 中提取数据
 * @createTime 2023年12月26日 15:38:27
 */
@Slf4j
@Accessors(chain = true)
public class EsFetcher {
    public List<FetchData> fetch(KeyPathContext context, EsCondition es) {
        ConclusionDataQueryCriteria criteria = es.getConclusionData();
        if (criteria.isEmpty()) {
            log.warn("没有 ES 查询条件，无法返回结果或者全量数据");
            return null;
        }

        List<ConclusionData> dataList = context.getDspComponents().getConclusionDataService()
                .query(criteria, context.getKeyPathParam().getDebug().getPrintSql());
        List<FetchData> list = FetchDataConvertor.convert(dataList);
        log.info("Fetch From ES: {}条记录", list == null ? null : list.size());
        return list;
    }
}
