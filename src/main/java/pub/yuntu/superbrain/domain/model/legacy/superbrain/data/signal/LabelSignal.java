package pub.yuntu.superbrain.domain.model.legacy.superbrain.data.signal;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import pub.yuntu.superbrain.domain.model.legacy.stream.datasource.DataFinder;

/**
 * Created by <PERSON><PERSON><PERSON> on 2022/12/28 15:09.
 */
@Slf4j
@Data
public class LabelSignal extends Signal {

    @Override
    public DataFinder city() {
        return null;
    }

    @Override
    public DataFinder categoryId() {
        return null;
    }

    @Override
    public DataFinder lastOrgCode() {
        return null;
    }

    @Override
    public String uniqueField() {
        return null;
    }

    @Override
    public DataFinder ownerFour() {
        return null;
    }

    @Override
    public DataFinder ownerSeven() {
        return null;
    }

    @Override
    public DataFinder decoration() {
        return null;
    }

    @Override
    public DataFinder district() {
        return null;
    }

    @Override
    public DataFinder customerName() {
        return null;
    }

    @Override
    public DataFinder splitUnitId() {
        Object obj = mapData.get("SPLITUNITID");
        if (obj == null)
            return new DataFinder(false, null);
        return new DataFinder(true, "" + obj);
    }

    @Override
    public DataFinder dataPeriod() {
        Object obj = mapData.get("DATAPERIOD");
        if (obj == null)
            return new DataFinder(false, null);
        return new DataFinder(true, "" + obj);
    }
}
