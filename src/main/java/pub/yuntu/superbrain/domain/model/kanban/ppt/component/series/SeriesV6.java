package pub.yuntu.superbrain.domain.model.kanban.ppt.component.series;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.series.Series;
import pub.yuntu.superbrain.domain.model.creativity.echart.style.ItemStyle;
import pub.yuntu.superbrain.domain.model.creativity.echart.style.Normal;
import pub.yuntu.superbrain.domain.model.creativity.echart.style.TextStyle;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.PptUtil;

import java.awt.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description PPT 单个序列
 * @createTime 2022年04月18日 15:23:54
 */
@Data
@Slf4j
public class SeriesV6 {

    SeriesParam seriesParam;

    int index; // 序列的序号
    String name;
    String type;

    Color color;
    List<? extends AbstractDataPoint> dataList; // 按DataPoint的Name匹配序列名
    List<String> scaleMarkList; // 刻度

    // 边框
    int borderWidth;
    Color borderColor;
    String borderType;

    // symbol
    String symbol;
    Integer symbolSize; // ppt symbol 与 EChart symbol 比例为 1:1.25

    // label
    boolean showLabel;
    String labelPosition;
    Color labelColor; // 标签颜色
    int labelRotate = 0;
    String labelFormat;
    int labelFontSize;
    String labelFontWeight;

    String stack; // 双堆叠

    public void parse(SeriesParam seriesParam) {
        this.seriesParam = seriesParam;
        this.index = seriesParam.getSeriesIndex();

        Series eChartSeries = seriesParam.getEChartSeries();
        this.name = eChartSeries.getName();
        this.type = eChartSeries.getType();
        this.stack = eChartSeries.getStack();

        parseColor(eChartSeries.getItemStyle());
        parseBorder(eChartSeries.getItemStyle());
        // 标签label
        parseLabel(eChartSeries);
        // 标记
        parseSymbol(eChartSeries);

        parseData(eChartSeries);
    }

    private void parseColor(ItemStyle itemStyle) {
        if (null == itemStyle) return;

        String colorText;

        Normal normal = itemStyle.getNormal();
        if (null == normal) {
            colorText = itemStyle.getColor();
        } else {
            colorText = normal.getColor();
        }

        if (StringUtils.isBlank(colorText)) return;

        this.color = PptUtil.parseColor(colorText);
    }

    private void parseBorder(ItemStyle itemStyle) {
        if (null == itemStyle) return;

        int borderWidth;
        String borderColorText;
        String borderType = null;

        Normal normal = itemStyle.getNormal();
        if (null == normal) {
            borderWidth = itemStyle.getBorderWidth() == null ? 0 : itemStyle.getBorderWidth();
            borderColorText = itemStyle.getBorderColor();
            borderType = itemStyle.getBorderType();
        } else {
            borderWidth = normal.getBorderWidth() == null ? 0 : normal.getBorderWidth();
            borderColorText = normal.getBorderColor();
            // borderType = normal.getBorderType();
        }

        this.borderWidth = borderWidth;
        if (StringUtils.isNotBlank(borderColorText)) {
            this.borderColor = PptUtil.parseColor(borderColorText);
        }
        this.borderType = borderType;
    }

    private void parseLabel(Series series) {
        ItemStyle itemStyle = series.getLabel();
        if (null != series.getMarkLine()) {
            itemStyle = series.getMarkLine().getItemStyle();
        }

        if (null == itemStyle) return;

        String color;
        Boolean show;
        String position = null;
        Object rotate = null;
        String formatter;
        Integer labelFontSize = null;
        String labelFontWeight = null;

        Normal normal = itemStyle.getNormal();
        if (null == normal) {
            color = itemStyle.getColor();
            show = itemStyle.getShow();
            // position = itemStyle.getPosition();
            // rotate = itemStyle.getRotate();
            formatter = itemStyle.getFormatter();

            TextStyle textStyle = itemStyle.getTextStyle();
            if (null != textStyle) {
                labelFontSize = textStyle.getFontSize();
                Object fontWeight = textStyle.getFontWeight();
                if (null != fontWeight) {
                    labelFontWeight = fontWeight.toString();
                }
            }
        } else {
            color = normal.getColor();
            show = normal.getShow();
            position = normal.getPosition();
            rotate = normal.getRotate();

            formatter = normal.getFormatter();
            if (null == formatter && null != normal.getLabel() && null != normal.getLabel().getFormatter()) {
                formatter = normal.getLabel().getFormatter() + "";
            }

            TextStyle textStyle = normal.getTextStyle();
            if (null != textStyle) {
                labelFontSize = textStyle.getFontSize();
                if (null != textStyle.getColor()) {
                    color = textStyle.getColor();
                }
                Object fontWeight = textStyle.getFontWeight();
                if (null != fontWeight) {
                    labelFontWeight = fontWeight.toString();
                }
            }
        }

        if (StringUtils.isNotBlank(color)) {
            labelColor = PptUtil.parseColor(color);
        }

        this.showLabel = null != show && show;

        if (StringUtils.isNotBlank(position)) {
            this.labelPosition = position;
        }

        if (null != rotate) {
            this.labelRotate = Integer.parseInt(normal.getRotate() + "");
        }

        if (StringUtils.isNotBlank(formatter)) {
            if (StringUtils.contains(formatter, "params.value.toFixed(0)")
                    || StringUtils.contains(formatter, "params.percent.toFixed(0)")) {
                this.labelFormat = "to_fixed_0";
            }
            if (StringUtils.contains(formatter, "params.value.toFixed(1)")
                    || StringUtils.contains(formatter, "params.percent.toFixed(1)")) {
                this.labelFormat = "to_fixed_1";
            }
            if (StringUtils.contains(formatter, "params.value.toFixed(2)")
                    || StringUtils.contains(formatter, "params.percent.toFixed(2)")) {
                this.labelFormat = "to_fixed_2";
            }
            if (StringUtils.contains(formatter, "params.value.toFixed(0)+'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(0)+ '%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(0) +'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(0) + '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(0)+'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(0)+ '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(0) +'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(0) + '%'")) {
                this.labelFormat = "to_fixed_0_%";
            }
            if (StringUtils.contains(formatter, "params.value.toFixed(1)+'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(1)+ '%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(1) +'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(1) + '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(1)+'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(1)+ '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(1) +'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(1) + '%'")) {
                this.labelFormat = "to_fixed_1_%";
            }
            if (StringUtils.contains(formatter, "params.value.toFixed(2)+'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(2)+ '%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(2) +'%'")
                    || StringUtils.contains(formatter, "params.value.toFixed(2) + '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(2)+'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(2)+ '%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(2) +'%'")
                    || StringUtils.contains(formatter, "params.percent.toFixed(2) + '%'")) {
                this.labelFormat = "to_fixed_2_%";
            }
        }

        if (null != labelFontSize) {
            this.labelFontSize = labelFontSize;
        }
        if (null != labelFontWeight) {
            this.labelFontWeight = labelFontWeight;
        }
    }

    private void parseSymbol(Series series) {
        Object symbol = series.getSymbol();
        if (null != symbol) {
            this.symbol = symbol + "";
        }
        Object symbolSize = series.getSymbolSize();
        if (null != symbolSize) {
            if (symbolSize instanceof Integer) {
                this.symbolSize = Integer.parseInt(symbolSize + "");
            }
        }
    }

    private void parseData(Series series) {
        dataList = series.getData();
        if (null == dataList) return;
        scaleMarkList = dataList.stream()
                .map(dataPoint -> null == dataPoint ? "" : dataPoint.getName())
                .collect(Collectors.toList());
    }

    /**
     * 序列数据是否是百分比
     */
    public boolean isPercentage() {
        boolean isPercentage = false;
        String labelFormat = this.getLabelFormat();
        if (StringUtils.isEmpty(labelFormat)) return isPercentage;

        if (labelFormat.contains("%")) {
            isPercentage = true;
        }

        return isPercentage;
    }

    public void clear() {
        seriesParam = null;
        name = null;
        type = null;
        color = null;
        if (null != dataList) {
            dataList.clear();
        }
        if (null != scaleMarkList) {
            scaleMarkList.clear();
        }
        symbol = null;
        symbolSize = null;
        labelPosition = null;
        labelColor = null;
    }
}
