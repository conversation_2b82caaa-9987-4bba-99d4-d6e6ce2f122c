package pub.yuntu.superbrain.domain.model.legacy.msdExam;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.AbstractEntity;
import pub.yuntu.superbrain.domain.model.legacy.exam.LegacyIndex;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 神客问卷单一指标
 * Created by <PERSON><PERSON><PERSON> on 2021/1/22.
 */

@Entity
@Data
@Table(name = "ana_index_exam_msd")
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class MsdExamIndex extends AbstractEntity implements Cloneable {

    @Transient
    private String msdExamIndexID;

    @Column(name = "msd_index_id")
    private String msdIndexId;

    @Column(name = "msd_standard_question_id")
    private String msdStandardQuestionId;

    @Column(name = "msd_exam_question_id")
    private String msdExamQuestionId;

    @Column(name = "customer_id")
    private String customerId;

    @Column(name = "exam_id")
    private String examId;

    private String name;

    private String type;

    @Column(name = "belong_to")
    private String belongTo;

    @Column(name = "standard_value")
    private Double standardValue;

    @Column(name = "index_code")
    private String indexCode;

    @Column(name = "parent_index_code")
    private String parentIndexCode;

    @Column(name = "index_level")
    private String indexLevel;

    @Column(name = "higher_is_better")
    private String higherIsBetter;

    // 是否全局扣分项
    @Column(name = "is_deduction")
    private String isDeduction;

    public String getMsdExamIndexID() {
        return this.getId();
    }

    public MsdExamIndex clone() {
        MsdExamIndex cloned = null;
        try {
            cloned = (MsdExamIndex) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return cloned;
    }

    public boolean globalDeduction() {
        return StringUtils.isNotBlank(isDeduction) && isDeduction.equals("Y");
    }

    public LegacyIndex convertToIndexBean() {
        LegacyIndex index = new LegacyIndex();
        index.set_id(this.get_id());
        index.setId(this.getId());
        index.setCustomerID(customerId);
        index.setName(this.getName());
        index.setType(this.getType());
        index.setQuestionIdList(msdExamQuestionId);
        index.setQuestionList(name);
        index.setMsdSingleBean(true);
        return index;
    }
}