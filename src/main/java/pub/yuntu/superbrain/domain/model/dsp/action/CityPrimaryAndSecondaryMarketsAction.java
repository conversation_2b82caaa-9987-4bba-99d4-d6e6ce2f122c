package pub.yuntu.superbrain.domain.model.dsp.action;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import pub.yuntu.superbrain.domain.dto.FeatureSplitUnitDTO;
import pub.yuntu.superbrain.domain.dto.LegacyIndexDTO;
import pub.yuntu.superbrain.domain.model.dsp.data.KeyParam;
import pub.yuntu.superbrain.domain.model.dsp.param.DspIndexParam;
import pub.yuntu.superbrain.domain.model.dsp.param.IndexTypeEnum;
import pub.yuntu.superbrain.domain.model.dsp.param.SignalTypeEnum;
import pub.yuntu.superbrain.domain.model.dsp.split.UnitHelper;
import pub.yuntu.superbrain.domain.model.dsp.summary.DspTreaty;
import pub.yuntu.superbrain.domain.model.dsp.task.TaskContext;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 城市一二级市场，与满意度处理指标有很大不同，单独用一个 action 来处理
 * 不同处举例：
 * 1. container 中保存数据是三层，满意度是 4 层
 * 2. 多个指标会使用同一个指标 ID，不同 dataType 来区分
 * @createTime 2023年10月08日 11:44:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", defaultImpl = FunAction.class, visible = true)
public class CityPrimaryAndSecondaryMarketsAction extends BaseDspAction {

    String method;
    String operation;
    String order;
    String sortBy;

    @Override
    public void process(TaskContext context) {
        // 可能配置了转换单元
        if (StringUtils.isNotBlank(convertUnit)) {
            List<FeatureSplitUnitDTO> convertUnitDTOS = UnitHelper.convertUnit(context, convertUnit, context.getUnit());
            this.setFeatureSplitUnitDTOS(convertUnitDTOS);
        }

        if (returnValue == null) {
            returnValue = "$" + method;
        }

        FunMethodEnum methodEnum = FunMethodEnum.getEnum(method);
        if (methodEnum == null) {
            throw new IllegalArgumentException("暂不支持的函数方法：" + method);
        }

        DspIndexParam indexParam = context.getDspLine().getIndexParam();
        if (null == indexParam) throw new IllegalArgumentException("indexParam is null");
        if (null != indexParam.getAssistIndexId()) throw new IllegalArgumentException("暂不支持辅助指标");
        if (null != indexParam.getComplexIndexIds()) throw new IllegalArgumentException("暂不支持组合指标");
        if (null == indexParam.getSingleIndexId()) throw new IllegalArgumentException("indexParam.singleIndexId is null");
        if (null == indexParam.getSingleDataTypes()) throw new IllegalArgumentException("indexParam.singleDataTypes is null");

        switch (methodEnum) {
//            case BOOL:
//                computeBool(context);
//                break;
            case SORT:
                computeSort(context);
                break;
            default:
                throw new IllegalArgumentException("暂不支持的函数方法：" + method);
        }
    }

    @Deprecated
    private void computeBool(TaskContext context) {
        if (StringUtils.isBlank(operation)) throw new IllegalArgumentException("请配置 operation 参数指定需要处理的类型");
        DspIndexParam indexParam = context.getDspLine().getIndexParam();
        String indexId = indexParam.getSingleIndexId();
        String[] singleDataTypes = indexParam.getSingleDataTypes();
        String unitDbId = "" + context.getUnit().get_id();
        // 特殊处理 Nebula 取数，指标ID如：cityPrimaryAndSecondaryMarkets，json：城市土地市场扫描.json
        if (indexId.equals("cityPrimaryAndSecondaryMarkets")) {
            unitDbId = context.getUnit().getOrgUnitName();
        }

        Map<String, Boolean> valueMap = Maps.newHashMap();
        for (String indexDataType : singleDataTypes) {
            Boolean evaluate = null;
            if (StringUtils.equalsIgnoreCase(operation, "increaseOneByOne") && StringUtils.equalsIgnoreCase(loop, "periods")) {
                if (StringUtils.isBlank(periods)) {
                    throw new IllegalArgumentException("按分期递增需要指定分期，periods不能为空");
                }
                double lastPeriodValue = -9999;
                for (String period : periods.split(",")) {
                    period = StringUtils.trim(period);
                    Double value = null;
                    // 特殊处理只有三个 key 的取数，一般需要四个 key
                    if (indexId.equals("cityPrimaryAndSecondaryMarkets")) {
                        // value = context.getPointValue(indexDataType, new String[]{indexId, period, unitDbId});
                        KeyParam keyParam = new KeyParam()
                                .setSignalType(indexParam.signalType())
                                .setIndexType(IndexTypeEnum.SATIS_ANA)
                                .setIndex(indexId).setDataPeriod(period).setSplit(unitDbId).setIndexDataType(indexDataType);
                        value = context.findResult(keyParam);
                    }
                    if (value == null) continue; // 没有值的分期不参与比较
                    log.debug("[逐步递增] {} : {}, {}, value: {}", unitDbId, period, indexDataType, value);
                    evaluate = true;
                    if (value < lastPeriodValue) evaluate = false;
                    if (!evaluate) break;
                    lastPeriodValue = value;
                }
            } else if (StringUtils.equalsIgnoreCase(operation, "decreaseOneByOne") && StringUtils.equalsIgnoreCase(loop, "periods")) {
                if (StringUtils.isBlank(periods)) {
                    throw new IllegalArgumentException("按分期递减需要指定分期，periods不能为空");
                }
                double lastPeriodValue = 9999;
                for (String period : periods.split(",")) {
                    period = StringUtils.trim(period);
                    Double value = null;
                    // 特殊处理只有三个 key 的取数，一般需要四个 key
                    if (indexId.equals("cityPrimaryAndSecondaryMarkets")) {
                        // value = context.getPointValue(indexDataType, new String[]{indexId, period, unitDbId});
                        KeyParam keyParam = new KeyParam()
                                .setSignalType(indexParam.signalType())
                                .setIndexType(IndexTypeEnum.SATIS_ANA)
                                .setIndex(indexId).setDataPeriod(period).setSplit(unitDbId).setIndexDataType(indexDataType);
                        value = context.findResult(keyParam);
                    }
                    if (value == null) continue; // 没有值的分期不参与比较
                    log.debug("[逐步递减] {} : {}, {}, value: {}", unitDbId, period, indexDataType, value);
                    evaluate = true;
                    if (value > lastPeriodValue) evaluate = false;
                    if (!evaluate) break;
                    lastPeriodValue = value;
                }
            } else {
                throw new IllegalArgumentException("暂不支持的bool配置, operation：" + operation);
            }
            valueMap.put(indexDataType, evaluate);
            log.debug("[{}] {} : {}, value: {}", operation, unitDbId, indexDataType, evaluate);
        }

        // 保存
        LegacyIndexDTO legacyIndexDTO = new LegacyIndexDTO().setIndexID(indexId).setName(indexId);
        legacyIndexDTO.setId(indexId);
        super.saveResult(context, context.getUnit(), context.getDspLine().getPeriodParam().getMainPeriod()
                , legacyIndexDTO, valueMap);
        context.clearMeta();
    }

    private void computeSort(TaskContext context) {
        DspIndexParam indexParam = context.getDspLine().getIndexParam();
        String indexId = indexParam.getSingleIndexId();
        String[] singleDataTypes = indexParam.getSingleDataTypes();
        String period = context.getDspLine().getPeriodParam().getMainPeriod();

        // order
        if (StringUtils.isBlank(order)) throw new NullPointerException("请配置order参数");
        if (returnValue == null) returnValue = "$sort";

        // 配置了convertUnit
        if (StringUtils.isNotBlank(convertUnit)) {
            List<FeatureSplitUnitDTO> convertUnitDTOS = UnitHelper.convertUnit(context, convertUnit, context.getUnit());
            if (StringUtils.isNotBlank(convertUnitFilter)) {
                convertUnitDTOS = filterUnits(context, convertUnitDTOS, convertUnitFilter);
            }
            this.setFeatureSplitUnitDTOS(convertUnitDTOS);
        }

        Map<FeatureSplitUnitDTO, Map<String, Object>> resultMap = Maps.newHashMap();
        for (String indexDataType : singleDataTypes) {
            List<HashMap> sortDataList = Lists.newArrayList();
            List<HashMap> resultList = Lists.newArrayList();

            // 组装sortDataList
            for (FeatureSplitUnitDTO unit : featureSplitUnitDTOS) {
                String unitDbId = "" + unit.get_id();
                // 特殊处理 Nebula 取数，指标ID如：cityPrimaryAndSecondaryMarkets，json：城市土地市场扫描.json
                IndexTypeEnum indexType = IndexTypeEnum.SATIS_ANA;
                if (indexId.equals("cityPrimaryAndSecondaryMarkets")) {
                    unitDbId = unit.getOrgUnitName();
                    indexType = IndexTypeEnum.CITY_PRIMARY_AND_SECONDARY_MARKETS;
                }
                HashMap unitMap = new HashMap<>();
                // Double value = context.getPointValue(indexDataType, new String[]{indexId, period, unitDbId});
                KeyParam keyParam = new KeyParam()
                        .setSignalType(indexParam.signalType())
                        .setIndexType(indexType)
                        .setIndex(indexId).setDataPeriod(period).setSplit(unitDbId).setIndexDataType(indexDataType);
                Double value = context.findResult(keyParam);
                if (value == null) continue;
                log.info("[排名] {} : {}, {}, value: {}", unitDbId, period, indexDataType, value);

                unitMap.put("value", value);
                unitMap.put("period", period);
                unitMap.put("index", indexId);
                unitMap.put("unit", unit);
                if (StringUtils.isNotBlank(groupBy)) {
                    unitMap.put("groupBy", groupBy);
                }
                if (StringUtils.isNotBlank(unit.cityName())) {
                    unitMap.put("city", unit.cityName());
                }
                if (StringUtils.isNotBlank(unit.customerName())) {
                    unitMap.put("customer", unit.customerName());
                }
                sortDataList.add(unitMap);
            }
            if (sortDataList.isEmpty()) return;

            // 排序
            sortDataList = SortAction.sort(sortDataList, order, true, false);

            // 处理并列问题
            int size = sortDataList.size();
            int sortIndex = 0;
            int sortLength = sortDataList.size(); // 参与排序的数量，放入map
            for (int i = 0; i < size; i++) {
                HashMap result = sortDataList.get(i);
                result.put("sortLength", sortLength);
                // 标记第一名和最后一名
                if (i == 0)
                    result.put("highlight", "first");
                if (i == size - 1)
                    result.put("highlight", "last");
                // 考虑并列排名
                if (i > 0 && StringUtils.equalsIgnoreCase("" + result.get("value"), "" + sortDataList.get(i - 1).get("value"))) {
                    result.put("sort", sortIndex);
                } else {
                    result.put("sort", (i + 1));
                    sortIndex = i + 1;
                }
                resultList.add(result);
            }
            if (resultList.isEmpty()) return;

            // 按排名排序结果集
            resultList.sort(Comparator.comparingInt(o -> (int) o.get("sort")));

            // 每个切分单元保存排名结果到space
            for (int i = 0; i < resultList.size(); i++) {
                HashMap dataMap = resultList.get(i);
                FeatureSplitUnitDTO unit = (FeatureSplitUnitDTO) dataMap.get("unit");
                Map<String, Object> valueMap = resultMap.getOrDefault(unit, Maps.newHashMap());
                valueMap.put(indexDataType, dataMap.get("sort"));

                // 排除第一名和最后一名
                if (i == 0 || i == resultList.size() - 1) {
                    resultMap.put(unit, valueMap);
                    continue;
                }

                // 同时找出比当前切分单元排名靠前和靠后的城市名
                String betterNames = resultList.subList(0, i - 1).stream()
                        .map(map -> ((FeatureSplitUnitDTO) map.get("unit")).getOrgUnitName())
                        .collect(Collectors.joining("、"));
                String worseNames = resultList.subList(i + 1, resultList.size() - 1).stream()
                        .map(map -> ((FeatureSplitUnitDTO) map.get("unit")).getOrgUnitName())
                        .collect(Collectors.joining("、"));
                valueMap.put(indexDataType + "_betterNames", betterNames);
                valueMap.put(indexDataType + "_worseNames", worseNames);
                resultMap.put(unit, valueMap);
            }
        }

        String returnValueStr = (String) returnValue;
        if (!returnValueStr.startsWith("$")) throw new IllegalArgumentException("returnValue请配置为$开头");
        resultMap.forEach((unit, valueMap) -> this.saveResultToSpace(context, unit, period, indexId, returnValueStr, valueMap));
    }

    private List<FeatureSplitUnitDTO> filterUnits(TaskContext context, List<FeatureSplitUnitDTO> units, String filter) {
        FeatureSplitUnitDTO originalUnit = context.getUnit();
        List<FeatureSplitUnitDTO> result = Lists.newArrayList();
        for (FeatureSplitUnitDTO unit : units) {
            context.setUnit(unit);
            DspTreaty treaty = new DspTreaty();
            treaty.setText(filter);
            if (treaty.evaluate(context)) result.add(unit);
            context.clearUnit();
        }
        context.setUnit(originalUnit);
        return result;
    }

    @Override
    public CityPrimaryAndSecondaryMarketsAction copy() {
        CityPrimaryAndSecondaryMarketsAction copy = new CityPrimaryAndSecondaryMarketsAction();
        BeanUtils.copyProperties(this, copy);
        return copy;
    }
}
