package pub.yuntu.superbrain.domain.model.dsp.summary;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.dto.LegacyIndexDTO;
import pub.yuntu.superbrain.domain.model.dsp.chip.TextChip;
import pub.yuntu.superbrain.domain.model.dsp.param.SignalTypeEnum;
import pub.yuntu.superbrain.domain.model.dsp.task.TaskContext;
import pub.yuntu.superbrain.domain.model.dsp.variable.BaseVariable;
import pub.yuntu.superbrain.domain.model.dsp.variable.VariableMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dongdong on 2023/8/14 17:50.
 * 信号逻辑文本，也会包含各种表达式
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Slf4j
@Data
public class DspText extends BaseDspExpression {

    Long indexNid;
    String conclusionType;
    String conclusionName;
    String businessType;
    String dimension;
    String score;
    String dataPeriod; // 如果保存的结论不是默认值，指定值会覆盖默认值
    String indexId; // 如果保存的结论不是默认值，指定值会覆盖默认值
    String attribute; // 如果保存的结论不是默认值，指定值会覆盖默认值
    String splitGroupId; // 如果保存的结论不是默认值，指定值会覆盖默认值
    String splitUnitId; // 如果保存的结论不是默认值，指定值会覆盖默认值
    String splitUnitName; // 如果保存的结论不是默认值，指定值会覆盖默认值

    public String process(TaskContext context) {
        context.setMetaByIndexNid(indexNid);
        if (null == context.getMeta()) {
            VariableMeta meta = new VariableMeta();
            meta.setPeriod(context.getDspLine().getPeriodParam().getMainPeriod());
            List<LegacyIndexDTO> legacyIndexDTOS = context.getDspLine().getIndexParam().translateIndex();
            if (legacyIndexDTOS.size() > 0) {
                LegacyIndexDTO legacyIndexDTO = legacyIndexDTOS.get(0);
                meta.setIndexNid(legacyIndexDTO.getIndexID());
                meta.setIndexDataType(legacyIndexDTO.getComputingType());
            }
            meta.setSignalType(SignalTypeEnum.ANA);
            context.setMeta(meta);
        }
        List<TextChip> chips = super.splitToChips(context);
        List<String> list = new ArrayList<>();
        boolean allVariableSuccess = true; // 是否所有变量都成功取值
        for (TextChip chip : chips) {
            // 处理变量的修饰符\过滤器
            if (chip.hasFilter()) {
                chip.prepareFilter();
            }
            Object compute = chip.compute(context);
            if (compute == null) {
                allVariableSuccess = false;
                break; // 强制要求所有变量都取值成功才能生成结论
            }
            // 可能是一个整数
            compute = BaseVariable.mayBeIsInteger(compute);
            String chipResult = compute.toString();
            if (StringUtils.isBlank(chipResult)) {
                allVariableSuccess = false;
                break;
            }
            list.add(chipResult);
        }

        // 强制要求所有变量都取值成功才能生成结论
        if (!allVariableSuccess) return null;

        String conclusion = String.join("", list);
        log.info("############## 生成结论: {}", conclusion);
//        log.info("############## 结论已生成");
        return conclusion;
    }

    @Override
    protected void destroy() {
        super.destroy();
        setIndexNid(null);
    }
}
