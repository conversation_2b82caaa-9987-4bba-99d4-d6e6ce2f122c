package pub.yuntu.superbrain.domain.model.identity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.legacy.split.AllianceSplitUnit;
import pub.yuntu.superbrain.domain.model.memory.AllianceUnitMemory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 自由切分身份
 * @createTime 2022年09月10日 10:11:53
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class AllianceUnitIdentity extends BaseIdentity implements Comparable {

    Long unitDbId;
    String unitId;
    AllianceSplitUnit unit;
    String city;
    String district;
    String project;
    String customerName;

    Map<String, AllianceSplitUnit> attributeAndUnitMap = new HashMap<>();

    public static AllianceUnitIdentity fromUnit(AllianceSplitUnit unit, String groupName) {
        AllianceUnitIdentity identity = new AllianceUnitIdentity();
        identity.groupName = groupName;
        identity.name = unit.getName();
        identity.source = IdentitySourceEnum.ALLIANCE_UNIT;
        identity.unitId = unit.getId();
        identity.unitDbId = unit.get_id();
        identity.unit = unit;
        identity.memory = new AllianceUnitMemory();
        identity.setInfo();
        return identity;
    }

    private void setInfo() {
        switch (groupName) {
            case "行业城市":
                city = unit.getSplitReferValues();
                break;
            case "开发商+行业城市":
                String[] values = unit.getSplitReferValues().split("_");
                customerName = values[0];
                city = values[1];
                break;
            case "行业全体开发商":
                customerName = unit.getSplitReferValues();
                break;
            case "各区总体":
                values = unit.getSplitReferValues().split("-");
                city = values[0];
                district = values[1];
                break;
            case "行业项目":
                values = unit.getSplitReferValues().split("_");
                customerName = values[0];
                project = values[1];
                if (StringUtils.isNotBlank(unit.getParentUnitName())) {
                    String parent = unit.getParentUnitName().split("_")[1];
                    city = parent.split("-")[0];
                    district = parent.split("-")[1];
                }
                break;
        }
    }

    @Override
    public int compareTo(Object o) {
        AllianceUnitIdentity another = (AllianceUnitIdentity) o;
        return Long.compare(another.getUnitDbId(), this.getUnitDbId());
    }

    @Override
    public Long splitGroupDbId() {
        return unit.getSplitGroupDbId();
    }

    @Override
    public Long splitUnitDbId() {
        return unitDbId;
    }

    @Override
    public String splitUnitId() {
        return unitId;
    }

    @Override
    public String attribute() {
        return unit.getExtensionReferValues();
    }
}
