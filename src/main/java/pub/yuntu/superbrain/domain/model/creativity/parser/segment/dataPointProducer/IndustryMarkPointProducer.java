package pub.yuntu.superbrain.domain.model.creativity.parser.segment.dataPointProducer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.creativity.CreativityParam;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.*;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.ZataSegment;
import pub.yuntu.superbrain.domain.model.legacy.indexGrid.IndexGrid;
import pub.yuntu.superbrain.domain.model.standard.StandardAttributeEnum;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Qi<PERSON>i on 2021/11/12 15:06.
 */
@Slf4j
@Data
public class IndustryMarkPointProducer extends AbstractPointProducer {

    public void loadDataList(CreativityParam param, ZataSegment zataSegment) {
        String[] columnTypes = {"_score", "_sample"};
        List<String> columns = new ArrayList<>();
        columns.add("name");
        columns.add("groupingName");

        StringBuilder ngqlIndexColumns = new StringBuilder();
        for (String indexCode : zataSegment.getIndexCodeList()) {
            if (StringUtils.equalsIgnoreCase(indexCode, "ts")) {
                IndexGrid indexGrid = zataSegment.getIndexCodeAndIndexGridMap().get(indexCode);
                if (StringUtils.equalsIgnoreCase(indexGrid.getIndexGridName(), "销售总分")) {
                    // 销售总分，匹配nebula行业ana字段名称
                    indexCode = "msIndex_9999";
                } else if (StringUtils.equalsIgnoreCase(indexGrid.getIndexGridName(), "物业总分")) {
                    // 物业总分，匹配nebula行业ana字段名称
                    indexCode = "msIndex_9998";
                }
            }

            for (String columnType : columnTypes) {
                columns.add(indexCode + columnType);
                ngqlIndexColumns.append(String.format("v.%s%s,", indexCode, columnType));
            }
        }
        // 去除末尾的逗号
        String ngqlIndexColumnsStr = String.valueOf(ngqlIndexColumns);
        ngqlIndexColumnsStr = ngqlIndexColumnsStr.substring(0, ngqlIndexColumnsStr.length() - 1);

        for (String dataPeriod : zataSegment.getPeriodArray()) {
            if (dataPeriod.endsWith("0112"))
                dataPeriod = dataPeriod.substring(0, dataPeriod.indexOf("-"));

            if (zataSegment.getAttributeList() == null) {
                zataSegment.setAttributeList(new ArrayList<>());
                zataSegment.getAttributeList().add(new String[]{"NONE"});
            }

            for (String[] attributeArray : zataSegment.getAttributeList()) {
                StringBuilder attributeType = new StringBuilder();
                List<String> attributeName = new ArrayList<>();

                for (String attribute : attributeArray) {
                    switch (attribute) {
                        case "OWNER_FOUR":
                            attributeType.append("+4类业主");
                            break;
                        case "OWNER_FOUR_ZHUN":
                        case "OWNER_FOUR_MO":
                        case "OWNER_FOUR_WEN":
                        case "OWNER_FOUR_LAO":
                            StandardAttributeEnum attributeEnum = StandardAttributeEnum.getInstance(attribute);
                            attributeName.add(attributeEnum.getPureOptionName());
                            attributeType.append("+4类业主");
                            break;
                        case "OWNER_SEVEN":
                            attributeType.append("+节点业主");
                            break;
                        case "OWNER_SEVEN_ZHUN1":
                        case "OWNER_SEVEN_ZHUN2":
                        case "OWNER_SEVEN_ZHUN3":
                        case "OWNER_SEVEN_MO1":
                        case "OWNER_SEVEN_MO2":
                        case "OWNER_SEVEN_WEN":
                        case "OWNER_SEVEN_LAO1":
                        case "OWNER_SEVEN_LAO2":
                        case "OWNER_SEVEN_LAO3":
                            attributeEnum = StandardAttributeEnum.getInstance(attribute);
                            attributeName.add(attributeEnum.getPureOptionName());
                            attributeType.append("+节点业主");
                            break;
                        case "HOUSE":
                            attributeType.append("+房屋类型");
                            break;
                        case "HOUSE_TYPE_GAO_CENG":
                        case "HOUSE_TYPE_XIAO_GAO_CENG":
                        case "HOUSE_TYPE_DUO_CENG":
                        case "HOUSE_TYPE_BIE_SHU":
                        case "HOUSE_TYPE_JIU_DIAN":
                        case "HOUSE_TYPE_QI_TA":
                            attributeEnum = StandardAttributeEnum.getInstance(attribute);
                            attributeName.add(attributeEnum.getPureOptionName());
                            attributeType.append("+房屋类型");
                            break;
                        case "DECORATION":
                            attributeType.append("+装修类型");
                            break;
                        case "DECORATION_TYPE_MAO_PI":
                        case "DECORATION_TYPE_ZHUANG_XIU":
                            attributeEnum = StandardAttributeEnum.getInstance(attribute);
                            attributeName.add(attributeEnum.getPureOptionName());
                            attributeType.append("+装修类型");
                            break;
                        case "NONE":
                            // 不需要处理
                            break;
                    }
                }

                // 2017_行业总体，2017_行业总体+4类业主，2017_行业总体+节点业主，2021_行业总体+4类业主+装修类型
                String analysisName = dataPeriod + "_" + zataSegment.getIndustryName() + attributeType;
                String groupingNameCondition = "";
                if (attributeName.size() > 0) {
                    groupingNameCondition = String.format(" and v.groupingName CONTAINS '%s' ", StringUtils.join(attributeName, ";"));
                }

                String ngql = String.format("MATCH (v:industryAnaResult) WHERE v.analysisName=='%s' %s RETURN v.analysisName, v.groupingName, %s",
                        analysisName, groupingNameCondition, ngqlIndexColumnsStr);

                List<HashMap<String, Object>> aDataList = param.getNgqlAndDataListCache().get(ngql);
                if (aDataList == null) {
                    aDataList = param.getKanbanFramework().getCommandParam().getNebulaLoader().loadNebulaDataList(ngql, columns.toArray(new String[]{}));
                    param.getNgqlAndDataListCache().put(ngql, aDataList);
                }
                if (aDataList == null)
                    continue;
                String finalDataPeriod = dataPeriod;
                aDataList.forEach(x -> x.put("dataPeriod", finalDataPeriod));

                if (zataSegment.getDataList() == null) {
                    zataSegment.setDataList(new ArrayList<>());
                }
                zataSegment.getDataList().addAll(aDataList);
            }
        }
    }

    public List<AbstractDataPoint> changeDataToPoint(CreativityParam param, ZataSegment zataSegment) {
        List<AbstractDataPoint> result = new ArrayList<>();
        if (zataSegment.getDataList() == null)
            return result;

        for (HashMap<String, Object> data : zataSegment.getDataList()) {
            for (String indexCode : zataSegment.getIndexCodeList()) {
                IndexGrid indexGrid = zataSegment.getIndexCodeAndIndexGridMap().get(indexCode);
                if (indexGrid == null)
                    continue;

                if (StringUtils.equalsIgnoreCase(indexCode, "ts")) {
                    if (StringUtils.equalsIgnoreCase(indexGrid.getIndexGridName(), "销售总分")) {
                        // 销售总分，匹配nebula行业ana字段名称
                        indexCode = "msIndex_9999";
                    } else if (StringUtils.equalsIgnoreCase(indexGrid.getIndexGridName(), "物业总分")) {
                        // 物业总分，匹配nebula行业ana字段名称
                        indexCode = "msIndex_9998";
                    }
                }
                Double value = null;
                if (data.get(indexCode + "_score") != null) {
                    value = Double.parseDouble(data.get(indexCode + "_score") + "") * 100D;
                }

                if (value == null)
                    continue;

                String groupingName = data.get("groupingName") + "";
                String attribute = StringUtils.isBlank(groupingName) || StringUtils.equalsIgnoreCase(groupingName, "null") || StringUtils.equalsIgnoreCase(groupingName, "NONE") ? "总体" : groupingName;

                DataPoint dataPoint = DataPointBuilder.aDataPoint()
                        .splitName(data.get("name") + "")
                        .indexName(indexGrid.getIndexGridName())
                        .type("dataPoint")
                        .value(value)
                        .mark(zataSegment.getMark())
                        .dataPeriod(data.get("dataPeriod") + "")
                        .attribute(attribute)
                        .higherIsBetter(StringUtils.equalsIgnoreCase(indexGrid.getHigherIsBetter(), "Y"))
                        .planarCode(indexGrid.getPlanarIndexCode())
                        .indexLevel(indexGrid.getIndexLevel())
                        .build();
                setPointName(param, zataSegment, dataPoint);
                result.add(dataPoint);
            }
        }

        return result;
    }

}
