package pub.yuntu.superbrain.domain.model.creativity.echart.visualMap;

import lombok.Data;
import pub.yuntu.superbrain.domain.model.creativity.echart.Basic;
import pub.yuntu.superbrain.domain.model.creativity.echart.style.TextStyle;

/**
 * 视觉映射组件，用于进行『视觉编码』，也就是将数据映射到视觉元素（视觉通道）
 * Created by <PERSON><PERSON><PERSON> on 2021/9/24 17:21.
 */
@Data
public class VisualMap extends Basic<VisualMap> {
    /**
     * 类型
     */
    private String type;
    /**
     * 最小值
     */
    private Integer min;
    /**
     * 最大值
     */
    private Integer max;
    /**
     * 是否启用值域漫游
     */
    private Boolean calculable;
    /**
     * 拖拽时，是否实时更新
     */
    private Boolean realtime;
    /**
     * 是否反转
     */
    private Boolean inverse;
    /**
     * 数据展示的小数精度，默认为0，无小数点
     */
    private Integer precision;
    /**
     * 图形的宽度，即长条的宽度
     */
    private Integer itemWidth;
    /**
     * 图形的高度，即长条的高度
     */
    private Integer itemHeight;
    /**
     * none, auto,
     * circle, rect, roundRect, rectangle, triangle, diamond,
     * emptyCircle, emptyRectangle, emptyTriangle, emptyDiamond,
     * heart, droplet, pin, arrow, star,
     * emptyheart, emptydroplet, emptypin, emptyarrow, emptystar
     */
    private String itemSymbol;
    /**
     * 指定组件中手柄和文字的摆放关系，可选为：auto, left, right, top, bottom
     */
    private String align;
    /**
     * handle 指『拖拽手柄』。handlePosition 指定了手柄的位置，可选为：auto, left, right, top, bottom
     */
    private String handlePosition;
    /**
     * 指定用数据的『哪个维度』，映射到视觉元素上
     */
    private Object dimension;
    /**
     * 指定取哪个系列的数据，即哪个系列的 series.data
     */
    private Integer seriesIndex;
    /**
     * 定义 在选中范围中 的视觉元素，可选为
     * symbol,//图形类别
     * symbolSize,//图形大小
     * color,//颜色
     * colorAlpha,//颜色透明度
     * colorLightness,//颜色明暗度
     * colorSaturation,//颜色饱和度
     * colorHue//色调
     */
    private VisualMapType inRange;
    /**
     * 定义 在选中范围外 的视觉元素
     */
    private VisualMapType outOfRange;
    /**
     * 水平（'horizontal'）或者竖直（'vertical'）
     */
    private String orient;
    /**
     * 标签的格式化工具
     */
    private String formatter;
    /**
     * 对于连续型数据，自动平均切分成几段
     */
    private Integer splitNumber;
    /**
     * single, multiple
     */
    private String selectedMode;
    private TextStyle textStyle;
    private Object[] color;
    private Object[] text;
    private Object[] textGap;
    private Object[] pieces;
    private Object[] categories;
}
