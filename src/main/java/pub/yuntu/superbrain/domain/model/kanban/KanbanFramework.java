package pub.yuntu.superbrain.domain.model.kanban;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.superbrain.domain.model.AbstractEntity;
import pub.yuntu.superbrain.domain.model.brain.BrainInstance;
import pub.yuntu.superbrain.domain.model.brain.command.CommandParam;
import pub.yuntu.superbrain.domain.model.brain.net.VirtualGroup;
import pub.yuntu.superbrain.domain.model.creativity.CreativityParam;
import pub.yuntu.superbrain.domain.model.creativity.block.AbstractBlock;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicLayout;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicRow;
import pub.yuntu.superbrain.domain.model.identity.AllianceUnitIdentity;
import pub.yuntu.superbrain.domain.model.identity.LegacyOrgIdentity;
import pub.yuntu.superbrain.domain.model.identity.RelatedIdentityGroup;
import pub.yuntu.superbrain.domain.model.kanban.config.KanbanBasicConfig;
import pub.yuntu.superbrain.domain.model.kanban.ppt.KanbanPpt;
import pub.yuntu.superbrain.domain.model.kanban.ppt.KanbanPptPage;
import pub.yuntu.superbrain.domain.model.kanban.temporaryCalculation.KanbanTemporaryCalculation;
import pub.yuntu.superbrain.domain.model.legacy.rh.param.CommercialParam;
import pub.yuntu.superbrain.domain.model.legacy.split.AllianceSplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.stream.task.feature.FeatureSplitUnit;
import pub.yuntu.superbrain.domain.model.neuron.BaseNeuron;
import pub.yuntu.superbrain.domain.model.stencil.BrainStencil;
import pub.yuntu.superbrain.infrastructure.util.ExceptionUtil;
import pub.yuntu.superbrain.infrastructure.util.JsonUtil;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 看板框架
 * @createTime 2022年11月14日 09:45:05
 */
@Component
@Slf4j
@Data
public class KanbanFramework {
    public static final Pattern expressionPattern = Pattern.compile("\\{branch:[\\w,:;\\-\\[\\]]+\\}");
    public static final Pattern brainPattern = Pattern.compile("\\{brain:[\\u4e00-\\u9fa5A-Za-z0-9]+");

    static ExecutorService kanbanTaskPool = Executors.newFixedThreadPool(10);
    Map<String, Set<String>> brainModuleMapping = Maps.newHashMap();
    Map<String, Set<String>> moduleBrainMapping = Maps.newHashMap();

    @Autowired
    CommandParam commandParam;
    // key: kanbanId, value: KanbanParam
    Map<String, KanbanParam> kanbanParamCache = Maps.newHashMap();

    public void build(String kanbanId, String user) {
        log.info("{} 开始构建看板, 看板ID：{}", user, kanbanId);
        long start = System.currentTimeMillis();
        KanbanParam param = KanbanFrameworkHelper.kanbanParam(this.getCommandParam(), kanbanId, kanbanParamCache, user);

        // 清理之前保存的结果
        clearData(param);
        // 从模块中整理用到的大脑
        initBrainFromModules(param);
        // build data net work
        // buildDataNetwork(param);
        buildDataNetwork(param);
        // 填充数据，已有DataPoints，从内存中取数填充
        fillDataPoints(param);
        // 后计算
        postCalculate(param);
        // scan visualization
        // scanVisualization(param);
        // 缓存构建完成的内容
        kanbanParamCache.put(kanbanId, param);

        log.info("{} 构建看板完成，看板[{}], 用时：{}", user, param.getKanban().getName(), DateUtil.calPassedTime(start));
    }

    private void clearData(KanbanParam param) {
        param.getCommercialParam().getPointNetworkMap_0().clear();
        param.getCommercialParam().getPointNetworkMap_1().clear();
        param.getCommercialParam().getPointNetworkMap_2().clear();
        param.getCommercialParam().getPointNetworkMap_3().clear();
        param.getCommercialParam().getPointNetworkMap_4().clear();
        param.getCommercialParam().getPointNetworkMap_5().clear();
        param.getCommercialParam().getPointNetworkMap_6().clear();
        param.getCommercialParam().getPointNetworkMap_7().clear();
        param.getCommercialParam().getPointNetworkMap_8().clear();
        param.getCommercialParam().getPointNetworkMap_9().clear();
        param.getCommercialParam().getPointNetworkMap_10().clear();
        param.getCommercialParam().getPointNetworkMap_11().clear();
        param.getCommercialParam().getPointNetworkMap_12().clear();
        param.getCommercialParam().getPointNetworkMap_13().clear();

        if (null == param.getLayoutCache()) return;

        param.getModuleList().forEach(kanbanModule -> {
            DynamicLayout layout = param.getLayoutCache().get(kanbanModule.getId());
            if (null == layout) return;

            layout.collectBlock().forEach(block -> {
                block.get_pointNetworkMap().clear();
            });
        });
    }

    public void buildModule(Long moduleNid, String user) {
        log.info("{} 开始构建模块, 模块ID：{}", user, moduleNid);
        long start = System.currentTimeMillis();

        List<KanbanModule> list = commandParam.getKanbanModuleRepository().findBy_id(moduleNid);
        KanbanModule kanbanModule = list.get(0);
        String kanbanId = kanbanModule.getKanbanId();

        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        buildDataNetworkByModule(kanbanParam, kanbanModule);
        fillModuleDataPoints(kanbanParam, kanbanModule);
        postCalculate(kanbanParam, moduleBrainMapping.get(kanbanModule.getId()));
        kanbanParamCache.put(kanbanId, kanbanParam);

        log.info("{} 构建模块完成，模块[{}], 用时：{}", user, kanbanModule.getName(), DateUtil.calPassedTime(start));
    }

    public DynamicLayout buildAndRefreshTemporaryCalculation(String temporaryCalculationId, String unitId, String user) {
        log.info("{} [临时计算] 开始执行", user);
        long start = System.currentTimeMillis();

        KanbanTemporaryCalculation kanbanTemporaryCalculation = commandParam
                .getTemporaryCalculationRepository()
                .findById(temporaryCalculationId)
                .orElseThrow(() -> new NullPointerException("临时计算不存在，ID=" + temporaryCalculationId));
        String kanbanId = kanbanTemporaryCalculation.getKanbanId();

        // 1.准备看板参数
        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            kanbanParam = KanbanParam.build(commandParam, kanbanId, user);
            kanbanParamCache.put(kanbanId, kanbanParam);
        }

        // 2.提取相关大脑：从布局json配置中提取相关的大脑
        String layoutJson = kanbanTemporaryCalculation.getJsonModule();
        layoutJson = replaceMarks(kanbanParam, layoutJson);
        Set<String> brainNames = KanbanService.parseBrainNamesFromJson(layoutJson, kanbanParam.getDefaultBrainName());

        // 3.处理不在看板中的大脑，补充信息 （看板中指定的大脑在准备看板参数时已处理完成）
        //<editor-fold desc="临时计算使用到的brain，可能看板中没有，所以看板初始化时 CommercialParam 中的groups不会包含，需要在这里补充">
        // 临时计算使用到的brain，可能看板中没有，所以看板初始化时 CommercialParam 中的groups不会包含，需要在这里补充
        // 多次执行，不会重复添加Grouping
        List<String> brainNotInKanban = Lists.newArrayList();
        for (String brainName : brainNames) {
            if (kanbanParam.getBrainNames().contains(brainName)) continue;
            brainNotInKanban.add(brainName);
        }
        CommercialParam commercialParam = kanbanParam.getCommercialParam();
        commercialParam.initGroupForBrainsNotInKanban(brainNotInKanban, kanbanParam);

        // 同原因处理NeuronContainer
        for (String brainName : brainNotInKanban) {
            KanbanParam.buildNeuronIndex(kanbanParam, brainName);
        }
        //</editor-fold>

        // 4.使用临时计算布局模拟看板模块
        DynamicLayout layout = JsonUtil.jsonMapper.fromJson(layoutJson, DynamicLayout.class);
        KanbanModule kanbanModule = KanbanModuleBuilder.aKanbanModule()
                .id("临时计算模拟模块")
                .name("临时计算模拟模块")
                .jsonConfig(layoutJson)
                .kanbanId(kanbanId) // 临时计算需要绑定看板，看板配置了基本参数
                .build();
        kanbanModule.set_id(-1L);
        kanbanParam.getModuleList().add(kanbanModule);
        defaultBrainExpression(kanbanParam.getDefaultBrainName(), kanbanModule);
        moduleBrainMapping.put(kanbanModule.getId(), brainNames);

        // 5.模块构建
        buildDataNetworkByLayoutWithUnitId(kanbanParam, kanbanModule, layout, unitId); // 缓存里面可能没有临时计算的Layout，不能从缓存里面取
        pointNetworkByModule(kanbanParam, kanbanModule);
        fillModuleDataPoints(kanbanParam, kanbanModule);
        postCalculate(kanbanParam, brainNames);

        log.info("{} [临时计算] 构建可视化完成，模块[{}], 用时：{}", user, kanbanModule.getName(), DateUtil.calPassedTime(start));

        // 6.刷新可视化
        layout = refreshModule(kanbanId, kanbanModule.getId(), unitId, user);
        layout.removeConfig();

        String unitName = KanbanFrameworkHelper.unitName(kanbanParam, unitId);
        log.info("{} [临时计算] 刷新可视化完成，模块[{}]，切分：[{}]", user, kanbanModule.getName(), unitName);
        log.info("{} [临时计算] 处理完成，切分：[{}]，共用时：{}", user, unitName, DateUtil.calPassedTime(start));
        return layout;
    }

    private void initBrainFromModules(KanbanParam kanbanParam) {
        // 1.生成两个mapping
        kanbanParam.getModuleList().forEach(kanbanModule -> {
            String moduleId = kanbanModule.getId();
            Set<String> brainNames = brainNamesOfModule(moduleId);
            brainNames.forEach(brainName -> {
                brainModuleMapping.putIfAbsent(brainName, Sets.newHashSet());
                brainModuleMapping.get(brainName).add(moduleId);
                moduleBrainMapping.putIfAbsent(moduleId, Sets.newHashSet());
                moduleBrainMapping.get(moduleId).add(brainName);
            });
        });
        // 2.没有指定大脑的表达式，补充默认表达式
        String defaultBrainName = kanbanParam.getDefaultBrainName();
        kanbanParam.getModuleList().forEach(kanbanModule -> defaultBrainExpression(defaultBrainName, kanbanModule));
    }

    private static void defaultBrainExpression(String defaultBrainName, KanbanModule kanbanModule) {
        String moduleJsonConfig = kanbanModule.getJsonConfig();
        List<String> lines = Arrays.stream(moduleJsonConfig.split("\n"))
                .collect(Collectors.toList());

        StringBuilder builder = new StringBuilder();
        for (int lineNo = 0; lineNo < lines.size(); lineNo++) {
            String line = lines.get(lineNo);
            if (!line.contains("branch")) {
                builder.append(line).append("\n");
                continue;
            }

            if (line.contains("{brain")) {
                builder.append(line).append("\n");
                continue;
            }

            Matcher matcher = expressionPattern.matcher(line);
            while (matcher.find()) {
                String expression = matcher.group();
                log.debug("brain: {}, module: {}, expression: {}", kanbanModule.getBrainName(), kanbanModule.getName(), expression);
                // 没有配置brain的表达式，使用默认大脑，并在表达式中补充brain，统一解析
                String tempExp = expression.replaceFirst("\\{", "");
                String newExpression = String.format("{brain:%s,%s", defaultBrainName, tempExp);
                line = line.replace(expression, newExpression);
                log.debug("New Expression: {}", newExpression);
                builder.append(line).append("\n");
            }
        }

        kanbanModule.setJsonConfig(builder.toString());
    }

    private Set<String> brainNamesOfModule(String moduleId) {
        List<BrainStencil> brainStencils = commandParam.getKanbanApplication().brainOfModule(moduleId);
        return brainStencils.stream()
                .map(BrainStencil::getStencilName).collect(Collectors.toSet());
    }

    private void buildDataNetwork(KanbanParam kanbanParam) {
        long start = System.currentTimeMillis();
        String kanbanName = kanbanParam.getKanban().getName();
        log.info("{} 开始构建看板[{}]的DataNetwork", kanbanParam.getUser(), kanbanName);

        List<KanbanModule> moduleList = kanbanParam.getModuleList();
        for (KanbanModule kanbanModule : moduleList) {

            String jsonString = kanbanModule.getJsonConfig();
            jsonString = replaceMarks(kanbanParam, jsonString);
            DynamicLayout layout = JsonUtil.jsonMapper.fromJson(jsonString, DynamicLayout.class);

            buildDataNetworkByLayout(kanbanParam, kanbanModule, layout);
        }
        for (KanbanModule kanbanModule : moduleList) {
            pointNetworkByModule(kanbanParam, kanbanModule);
        }

        log.info("{} 构建看板[{}]的DataNetwork完成，用时：{}", kanbanParam.getUser(), kanbanName,
                DateUtil.calPassedTime(start));
    }

    private static String replaceMarks(KanbanParam kanbanParam, String jsonString) {
        Map<Integer, String> colorMap = KanbanFrameworkHelper.colorMap(kanbanParam);
        // 色卡替换
        jsonString = KanbanFrameworkHelper.transformColor(colorMap, jsonString);
        // baseline
        // jsonString = transformBaseline(jsonString, orgViewPoint.getBaselines(), stencil.getDisplayOrder());
        // 图的小数位数
        // jsonString = transformDecimal(jsonString, commercialParam.getDecimalParams(), stencil.getDisplayOrder());
        jsonString = KanbanFrameworkHelper.transformDecimal(jsonString, kanbanParam.getCommercialParam().getDecimalParams());
        return jsonString;
    }

    private void buildDataNetworkByModule(KanbanParam kanbanParam, KanbanModule kanbanModule) {
        DynamicLayout layout = kanbanParam.getLayoutCache().get(kanbanModule.getId());
        buildDataNetworkByLayout(kanbanParam, kanbanModule, layout);
    }

    private void buildDataNetworkByLayout(KanbanParam kanbanParam, KanbanModule kanbanModule, DynamicLayout layout) {
        String moduleId = kanbanModule.getId();
        Set<String> brainNames = moduleBrainMapping.get(moduleId);

        List<FeatureSplitUnit> unitList = KanbanFrameworkHelper.unitsOfBrains(kanbanParam, brainNames);
        int batch = unitList.size();
        log.info("看板模块[{}]，batch: {}", kanbanModule.getName(), batch);

        CountDownLatch countDownLatch = new CountDownLatch(batch);

        ThreadLocal<DynamicLayout> layoutThreadLocal = ThreadLocal.withInitial(() -> layout);
        unitList.forEach(unit -> {
            CreativityParam creativityParam = KanbanFrameworkHelper.creativityParam(this, kanbanParam, unit.getBrainName(), unit);
            DataNetworkCallable2 callable = new DataNetworkCallable2();
            callable.setKanbanParam(kanbanParam);
            callable.setLayoutThreadLocal(layoutThreadLocal);
            callable.setCreativityParam(creativityParam);
            callable.setKanbanModule(kanbanModule);
            callable.setCountDownLatch(countDownLatch);
            kanbanTaskPool.submit(callable);
        });
        // 等待计算任务完成
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("countDownLatch error", e);
        }

        unitList.clear();

        if (null == kanbanParam.getLayoutCache()) {
            kanbanParam.setLayoutCache(Maps.newHashMap());
        }
        kanbanParam.getLayoutCache().put(moduleId, layout);
    }

    private void buildDataNetworkByLayoutWithUnitId(KanbanParam kanbanParam, KanbanModule kanbanModule, DynamicLayout layout, String unitId) {
        String moduleId = kanbanModule.getId();
        Set<String> brainNames = moduleBrainMapping.get(moduleId);

        CommercialParam commercialParam = kanbanParam.getCommercialParam();
        FeatureSplitUnit splitUnit = commercialParam.getFullSplitMap().get(unitId);
        if (null == splitUnit) {
            log.warn("没有找到组织结构切分单元，ID={}", unitId);
            splitUnit = commercialParam.getFullAllianceSplitMap().get(unitId);
            if (null == splitUnit) {
                log.error("没有找到切分单元，ID={}", unitId);
                return;
            }
        }
        List<FeatureSplitUnit> unitList = Collections.singletonList(splitUnit);
        int batch = unitList.size();
        log.info("看板模块[{}]，batch: {}", kanbanModule.getName(), batch);

        CountDownLatch countDownLatch = new CountDownLatch(batch);

        ThreadLocal<DynamicLayout> layoutThreadLocal = ThreadLocal.withInitial(() -> layout);
        unitList.forEach(unit -> {
            CreativityParam creativityParam = KanbanFrameworkHelper.creativityParam(this, kanbanParam, unit.getBrainName(), unit);
            DataNetworkCallable2 callable = new DataNetworkCallable2();
            callable.setKanbanParam(kanbanParam);
            callable.setLayoutThreadLocal(layoutThreadLocal);
            callable.setCreativityParam(creativityParam);
            callable.setKanbanModule(kanbanModule);
            callable.setCountDownLatch(countDownLatch);
            kanbanTaskPool.submit(callable);
        });
        // 等待计算任务完成
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("countDownLatch error", e);
        }

        unitList = null;

        if (null == kanbanParam.getLayoutCache()) {
            kanbanParam.setLayoutCache(Maps.newHashMap());
        }
        kanbanParam.getLayoutCache().put(moduleId, layout);
    }

    private void pointNetworkByModule(KanbanParam kanbanParam, KanbanModule kanbanModule) {
        long start = System.currentTimeMillis();
        log.info("开始填充 CommercialParam().pointsMapping");

        DynamicLayout layout = kanbanParam.getLayoutCache().get(kanbanModule.getId());
        List<AbstractBlock> blockList = layout.collectBlock();
        for (AbstractBlock block : blockList) {
            block.setPointFromNetwork(true);
            block.get_pointNetworkMap().forEach((expression, unitDataPointsMap) -> {
                if (null == unitDataPointsMap) return;
                unitDataPointsMap.forEach((unitId, dataPoints) -> {
                    if (null == dataPoints) return;
                    dataPoints.forEach(dataPoint -> kanbanParam.getCommercialParam().pointsMapping(dataPoint));
                });
            });
        }
        log.info("填充 CommercialParam().pointsMapping 完成，用时：{}", DateUtil.calPassedTime(start));
    }

    private void fillDataPoints(KanbanParam kanbanParam) {
        fillDataPoints(kanbanParam, brainModuleMapping.keySet());
    }

    // 仅填充一个模块涉及的数据
    private void fillModuleDataPoints(KanbanParam kanbanParam, KanbanModule module) {
        Set<String> brainNames = moduleBrainMapping.get(module.getId());
        fillDataPoints(kanbanParam, brainNames);
    }

    private void fillDataPoints(KanbanParam kanbanParam, Set<String> brainNames) {
        long start = System.currentTimeMillis();
        log.info("{} 开始从内存填充看板[{}]的数据", kanbanParam.getUser(), kanbanParam.getKanban().getName());

        int batch = 0;
        for (String brainName : brainNames) {
            List<BaseNeuron> neuronListOfBrain = KanbanFrameworkHelper.neuronListOfBrain(kanbanParam, brainName);
            batch += neuronListOfBrain.size();
        }
        log.info("batch: {}", batch);

        CountDownLatch countDownLatch = new CountDownLatch(batch);
        for (String brainName : brainNames) {
            List<BaseNeuron> neuronListOfBrain = KanbanFrameworkHelper.neuronListOfBrain(kanbanParam, brainName);
            for (BaseNeuron baseNeuron : neuronListOfBrain) {
                FillDataPointsCallable callable = new FillDataPointsCallable();
                callable.setKanbanParam(kanbanParam);
                callable.setBaseNeuron(baseNeuron);
                callable.setCountDownLatch(countDownLatch);
                kanbanTaskPool.submit(callable);
            }
        }
        // 等待计算任务完成
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("countDownLatch error", e);
        }

        log.info("{} 从内存填充看板[{}]的数据完成，用时：{}", kanbanParam.getUser(), kanbanParam.getKanban().getName(),
                DateUtil.calPassedTime(start));
    }

    private void postCalculate(KanbanParam kanbanParam) {
        postCalculate(kanbanParam, brainModuleMapping.keySet());
    }

    private void postCalculate(KanbanParam kanbanParam, Set<String> brainNames) {
        long start = System.currentTimeMillis();
        log.info("{} 开始执行看板[{}]的后计算", kanbanParam.getUser(), kanbanParam.getKanban().getName());

        brainNames.forEach(brainName -> {
            List<BaseNeuron> neuronListOfBrain = KanbanFrameworkHelper.neuronListOfBrain(kanbanParam, brainName);
            neuronListOfBrain.forEach(baseNeuron -> baseNeuron._postCalculate(kanbanParam.getCommercialParam()));
        });

        log.info("{} 执行看板[{}]的后计算完成，用时：{}", kanbanParam.getUser(), kanbanParam.getKanban().getName(),
                DateUtil.calPassedTime(start));
    }

    @Deprecated
    private void scanVisualization(KanbanParam kanbanParam) {
        long start = System.currentTimeMillis();
        log.info("{} 开始构建并缓存看板[{}]的可视化", kanbanParam.getUser(), kanbanParam.getKanban().getName());

        String kanbanId = kanbanParam.getKanban().getId();
        for (KanbanModule kanbanModule : kanbanParam.getModuleList()) {
            Set<String> brainNames = moduleBrainMapping.get(kanbanModule.getId());
            for (String brainName : brainNames) {
                List<BaseNeuron> neuronList = KanbanFrameworkHelper.neuronListOfBrain(kanbanParam, brainName);
                // log.info("Test-scanVisualization-1. brainName {}, module {}", brainName, kanbanModule.getName());

                neuronList.forEach(baseNeuron -> {
                    // log.info("Test-scanVisualization-2. baseNeuron {}", baseNeuron.getPrimaryName());
                    baseNeuron.unitList().forEach(mainUnit -> {
                        // log.info("Test-scanVisualization-3. mainUnit {}", mainUnit.name());
                        CreativityParam creativityParam = KanbanFrameworkHelper.creativityParam(this, kanbanParam, brainName, mainUnit);
                        // YT-Intelligence ViewPointDataMatrixInstance._scanVisualization()
                        HashMap<String, DynamicLayout> visualLayoutMap = baseNeuron.getVisualLayoutMap();
                        if (null == visualLayoutMap) return;

                        DynamicLayout layout = visualLayoutMap.get(kanbanModule.getId());
                        // FeatureSplitUnit mainUnit = mainUnit(baseNeuron);
                        if (null == mainUnit) {
                            log.warn("没有切分, 神经元：{}", baseNeuron.getPrimaryName());
                            return;
                        }
                        layout.parse(creativityParam);
                        layout.removeConfig();

//                        moduleCache.putIfAbsent(kanbanId, Maps.newHashMap());
//                        moduleCache.get(kanbanId).put(KanbanFrameworkHelper.moduleCacheKey(kanbanModule.getId(), mainUnit), layout);
                        log.debug("Cache 切分：{}, 看板模块：{}", mainUnit.name(), kanbanModule.getName());
                    });
                });
            }
        }

        log.info("{} 构建并缓存看板[{}]的可视化完成，用时：{}", kanbanParam.getUser(), kanbanParam.getKanban().getName(),
                DateUtil.calPassedTime(start));
    }

    public DynamicLayout refreshKanban(String kanbanId, String unitId, String user) {
        long start = System.currentTimeMillis();
        Kanban kanban = getCommandParam().getKanbanRepository().findById(kanbanId)
                .orElseThrow(() -> new NullPointerException("看板不存在，ID=" + kanbanId));
        String kanbanName = kanban.getName();

        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            log.error("请先构建看板: {}", kanbanId);
            return null;
        }
        String unitName = KanbanFrameworkHelper.unitName(kanbanParam, unitId);
        log.info("{} 开始刷新看板[{}]，切分：[{}]", user, kanbanName, unitName);

        List<KanbanModule> modules = getCommandParam().getKanbanModuleRepository().findByKanbanId(kanbanId);
        Map<Long, KanbanModule> moduleMapping = modules.stream()
                .collect(Collectors.toMap(AbstractEntity::get_id, module -> module));

        String jsonLayout = kanban.getJsonLayout();
        if (StringUtils.isBlank(jsonLayout)) {
            String message = "看板[" + kanbanName + "]没有布局，需要先编辑布局";
            log.warn(message);
            throw new IllegalStateException(message);
        }
        DynamicLayout layout = JsonUtil.jsonMapper.fromJson(jsonLayout, DynamicLayout.class);
        if (null == layout) {
            String message = "看板[" + kanbanName + "]没有布局，需要先编辑布局";
            log.warn(message);
            throw new IllegalStateException(message);
        }

        refreshLayout(kanbanId, unitId, user, moduleMapping, layout);

        log.info("{} 刷新看板[{}]完成，切分：[{}]，用时：{}", user, kanbanName, unitName, DateUtil.calPassedTime(start));
        return layout;
    }

    public DynamicLayout refreshPublishKanban(String kanbanId, String unitId, String user) {
        long start = System.currentTimeMillis();
        Kanban kanban = getCommandParam().getKanbanRepository().findById(kanbanId)
                .orElseThrow(() -> new NullPointerException("看板不存在，ID=" + kanbanId));
        String kanbanName = kanban.getName();

        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            log.error("请先构建看板: {}", kanbanId);
            return null;
        }
        String unitName = KanbanFrameworkHelper.unitName(kanbanParam, unitId);
        log.info("{} 开始使用发布数据刷新看板[{}]，切分：[{}]", user, kanbanName, unitName);

        DynamicLayout layout = KanbanFrameworkHelper.readKanbanVisual(kanbanParam, unitId);
        if (null == layout) {
            String message = "看板[" + kanbanName + "]没有布局，需要先编辑布局";
            log.warn(message);
            throw new IllegalStateException(message);
        }

        log.info("{} 使用发布数据刷新看板[{}]完成，切分：[{}]，用时：{}", user, kanbanName, unitName, DateUtil.calPassedTime(start));
        return layout;
    }

    private void refreshLayout(String kanbanId, String unitId, String user, Map<Long, KanbanModule> moduleMapping, DynamicLayout layout) {
        layout.getRows().forEach(row -> refreshRow(kanbanId, unitId, user, moduleMapping, row));
    }

    private void refreshRow(String kanbanId, String unitId, String user, Map<Long, KanbanModule> moduleMapping, DynamicRow row) {
        row.getColumns().forEach(column -> {
            Long moduleId = column.getModuleId();
            if (null != moduleId) {
                KanbanModule kanbanModule = moduleMapping.get(moduleId);
                if (null == kanbanModule) {
                    ExceptionUtil.throwNullPointerException(String.format("看板中没有ID为 %s 的模块", moduleId));
                }
                DynamicLayout moduleLayout = refreshModule(kanbanId, kanbanModule.getId(), unitId, user);
                column.setRows(moduleLayout.getRows());
            }

            if (null != column.getRows()) {
                column.getRows().forEach(subRow -> refreshRow(kanbanId, unitId, user, moduleMapping, subRow));
            }
        });
    }

    public DynamicLayout refreshModule(String kanbanId, String moduleId, String unitId, String user) {
        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            log.error("请先构建看板: {}", kanbanId);
            return null;
        }
        Kanban kanban = kanbanParam.getKanban();
        String kanbanName = kanban.getName();
        Optional<KanbanModule> moduleOptional = kanbanParam.getModuleList().stream()
                .filter(module -> module.getId().equals(moduleId)).findFirst();
        if (!moduleOptional.isPresent()) {
            ExceptionUtil.throwNullPointerException(String.format("[%s] 看板模块不存在, 看板[%s], 模块ID[%s]", user, kanbanName, moduleId));
        }
        KanbanModule kanbanModule = moduleOptional.get();
        String moduleName = kanbanModule.getName();
        String unitName = KanbanFrameworkHelper.unitName(kanbanParam, unitId);

        log.info("[{}] 开始刷新看板模块, 看板[{}], 模块[{}], 切分[{}]", user, kanbanName, moduleName, unitName);
        DynamicLayout layout = kanbanParam.getLayoutCache().get(kanbanModule.getId());
        if (null == layout) {
            log.error("看板没有构建，看板ID：{}", kanbanId);
            return null;
        }

        FeatureSplitUnit unit = kanbanParam.getCommercialParam().getFullSplitMap().get(unitId);
        Set<String> brainNames = moduleBrainMapping.get(moduleId);
        for (String brainName : brainNames) {
            // 传入的切分ID是默认大脑的组织结构结构ID
            // 特殊情况，行业开发商图表，需要指向行业开发商切分，如：传入切分是招商蛇口集团，实际需要的切分是 行业开发商 招商 的切分
            if (StringUtils.equalsIgnoreCase(brainName, "行业开发商")) {
                unit = kanbanParam.getIndustryCustomerUnit();
            }
            CreativityParam creativityParam = KanbanFrameworkHelper.creativityParam(this, kanbanParam, brainName, unit);
            layout._parse(creativityParam);
        }

        // 插入结论
        layout.removeAssertRows(); // 清除缓存Layout中的已有结论
        KanbanFrameworkHelper.fillModuleAsserts(this, kanbanParam, unitId, kanbanModule);
        KanbanFrameworkHelper.insertAssertsToLayout(kanbanModule, layout);

        // 复制Layout，剔除配置
        DynamicLayout layoutCopy = null;
        try {
            String json = JsonUtil.mapper.writeValueAsString(layout);
            layoutCopy = JsonUtil.jsonMapper.fromJson(json, DynamicLayout.class);
            layoutCopy.removeConfig();
        } catch (JsonProcessingException e) {
            log.error("Layout 转 Json 发生错误", e);
            return null;
        }

        log.debug("[{}] 刷新看板模块完成, 看板[{}], 模块[{}]", user, kanbanName, moduleName);
        return layoutCopy;
    }

    public void publish(String kanbanId, String user) {
        log.info("{} 开始发布看板, 看板ID：{}", user, kanbanId);
        long start = System.currentTimeMillis();
        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            ExceptionUtil.throwIllegalArgumentException("请先构建看板: " + kanbanId);
            return;
        }

        /*
        需要两次处理
        1. 前端展示的Layout，整个看板一个Layout，存一个可视化文件
        2. 生成PPT使用的可视化文件，一个PPT页面一个Layout存一个可视化文件
         */
        // 处理前端展示的Layout
        publishKanbanLayout(kanbanParam, user);
        // 处理生成PPT的Layout
        publishPptLayout(kanbanParam, user);

        log.info("{} 发布看板完成，看板[{}], 用时：{}", user, kanbanParam.getKanban().getName(), DateUtil.calPassedTime(start));
    }

    private void publishKanbanLayout(KanbanParam kanbanParam, String user) {
        // 遍历默认大脑神经元网络中的身份
        // Set<String> brainNames = kanbanParam.getBrainNames();
        Set<String> brainNames = Sets.newHashSet();
        brainNames.add(kanbanParam.getDefaultBrainName());
//        List<FeatureSplitUnit> unitList = KanbanFrameworkHelper.unitsOfBrains(kanbanParam, brainNames);
        List<FeatureSplitUnit> unitList = orgUnitsOfDefaultBrain(kanbanParam);

        int total = unitList.size();
        for (int i = 0; i < total; i++) {
            FeatureSplitUnit featureSplitUnit = unitList.get(i);
            String unitId = featureSplitUnit.get_id() + "";
            DynamicLayout layout = refreshKanban(kanbanParam.getKanban().getId(), unitId, user);
            KanbanFrameworkHelper.saveKanbanVisual(kanbanParam, featureSplitUnit, unitId, layout);
            layout.clear();

            if (i % 20 == 0) {
                log.info("发布看板Layout finished, total={}, now={}, name {} @ split {}", total, i, kanbanParam.getKanban().getName(), featureSplitUnit.name());
            }
        }
    }

    private List<FeatureSplitUnit> orgUnitsOfDefaultBrain(KanbanParam kanbanParam) {
        List<FeatureSplitUnit> unitList = Lists.newArrayList();
        // 默认大脑的神经元
        List<BaseNeuron> neurons = KanbanFrameworkHelper.neuronsOfDefaultBrain(kanbanParam);
        neurons.forEach(baseNeuron -> {
            // 只处理组织结构切分
            LegacyOrgIdentity orgIdentity = baseNeuron.getIdentityGroup().getOrgIdentity();
            if (null == orgIdentity) return;
            unitList.add(orgIdentity.getUnit());
        });
        return unitList;
    }

    private void publishPptLayout(KanbanParam kanbanParam, String user) {
        long start = System.currentTimeMillis();
        String kanbanId = kanbanParam.getKanban().getId();
        Kanban kanban = getCommandParam().getKanbanRepository().findById(kanbanId)
                .orElseThrow(() -> new NullPointerException("看板不存在，ID=" + kanbanId));
        log.info("{} 开始发布看板 PPT Layout：{}", user, kanban.getName());

        List<KanbanModule> modules = getCommandParam().getKanbanModuleRepository().findByKanbanId(kanbanId);
        Map<Long, KanbanModule> moduleMapping = modules.stream()
                .collect(Collectors.toMap(AbstractEntity::get_id, module -> module));

        String pptLayout = kanban.getPptLayout();
        if (StringUtils.isBlank(pptLayout)) {
            String message = "看板[" + kanban.getName() + "]没有PPT布局，需要先编辑PPT布局";
            ExceptionUtil.throwNullPointerException(message);
            return;
        }

        List<FeatureSplitUnit> unitList = orgUnitsOfDefaultBrain(kanbanParam);
        KanbanPpt kanbanPpt = JsonUtil.jsonMapper.fromJson(pptLayout, KanbanPpt.class);
        List<KanbanPptPage> pages = kanbanPpt.getPages();
        int total = unitList.size();
        for (int i = 0; i < total; i++) {
            FeatureSplitUnit featureSplitUnit = unitList.get(i);
            String unitId = featureSplitUnit.get_id() + "";
            for (KanbanPptPage page : pages) {
                DynamicLayout layout = page.getLayout();
                refreshLayout(kanbanId, unitId, user, moduleMapping, layout);
                // 保存PPT页面的Layout到可视化文件
                KanbanFrameworkHelper.savePptPageVisual(kanbanParam, featureSplitUnit, unitId, page, layout);
            }

            if (i % 20 == 0) {
                log.info("发布看板 PPT Layout finished, total={}, now={}, name {} @ split {}", total, i, kanbanParam.getKanban().getName(), featureSplitUnit.name());
            }
        }

        log.info("{} 发布看板 PPT Layout 完成，用时：{}", user, DateUtil.calPassedTime(start));
    }

    public List<String> virtualGroups(String kanbanId) {
        BrainInstance defaultBrain = defaultBrainOfKanbanConfig(kanbanId);
        if (null == defaultBrain) {
            ExceptionUtil.throwIllegalStateException("大脑没有构建");
        }
        HashMap<String, VirtualGroup> virtualGroupMap = defaultBrain.getNeuronNet().getVirtualGroupMap();
        KanbanBasicConfig kanbanBasicConfig = getKanbanBasicConfig(kanbanId);
        String[] viewpoints = kanbanBasicConfig.getViewpoints();
        List<String> list = Lists.newArrayList();
        if (null != viewpoints && viewpoints.length > 0) {
            for (String viewpoint : viewpoints) {
                if (virtualGroupMap.containsKey(viewpoint)) {
                    list.add(viewpoint);
                }
            }
        }
        return list.stream()
                .filter(name -> !StringUtils.contains(name, "-组合身份"))
                .filter(name -> !StringUtils.contains(name, "-全体"))
                .filter(name -> !StringUtils.contains(name, "全部"))
                .collect(Collectors.toList());
    }

    public List<HashMap<String, String>> identities(String kanbanId, String groupName) {
        BrainInstance defaultBrain = defaultBrainOfKanbanConfig(kanbanId);
        if (null == defaultBrain) {
            ExceptionUtil.throwIllegalStateException("大脑没有构建");
        }
        HashMap<String, VirtualGroup> virtualGroupMap = defaultBrain.getNeuronNet().getVirtualGroupMap();
        VirtualGroup virtualGroup = virtualGroupMap.get(groupName);
        List<BaseNeuron> neuronList = virtualGroup.getGroupingNeuronList();
        return neuronList.stream().map(baseNeuron -> {
            HashMap<String, String> data = Maps.newHashMap();
            RelatedIdentityGroup identityGroup = baseNeuron.getIdentityGroup();
            LegacyOrgIdentity orgIdentity = identityGroup.getOrgIdentity();
            if (null != orgIdentity) {
                SplitUnit unit = orgIdentity.getUnit();
                data.put("id", unit.getId());
                data.put("_id", unit.get_id() + "");
                data.put("name", unit.getName());
            }
            if (!data.isEmpty()) {
                return data;
            }
            AllianceUnitIdentity allianceUnitIdentity = identityGroup.getAllianceUnitIdentity();
            if (null != allianceUnitIdentity) {
                AllianceSplitUnit unit = allianceUnitIdentity.getUnit();
                data.put("id", unit.getId());
                data.put("_id", unit.get_id() + "");
                data.put("name", unit.getName());
            }
            if (!data.isEmpty()) {
                return data;
            }
            return null;
        }).collect(Collectors.toList());
    }

    private BrainInstance defaultBrainOfKanbanConfig(String kanbanId) {
        KanbanBasicConfig basicConfig = getKanbanBasicConfig(kanbanId);
        String defaultBrainName = basicConfig.getDefaultBrain();
        return commandParam.getBrainCenter().getInstance(defaultBrainName);
    }

    private KanbanBasicConfig getKanbanBasicConfig(String kanbanId) {
        Kanban kanban = commandParam.getKanbanRepository().findById(kanbanId)
                .orElseThrow(() -> new NullPointerException("看板不存在, ID=" + kanbanId));
        return JsonUtil.jsonMapper.fromJson(kanban.getJsonConfig(), KanbanBasicConfig.class);
    }

    public KanbanParam kanbanParam(String kanbanId) {
        KanbanParam kanbanParam = kanbanParamCache.get(kanbanId);
        if (null == kanbanParam) {
            ExceptionUtil.throwIllegalArgumentException("请先构建看板: " + kanbanId);
            return null;
        }
        return kanbanParam;
    }
}
