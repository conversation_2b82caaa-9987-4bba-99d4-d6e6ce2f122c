package pub.yuntu.superbrain.domain.model.legacy.net.grouping;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.BrainDataSegment;
import pub.yuntu.superbrain.domain.model.legacy.stream.MsdType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by dongdong on 2021/11/2 11:17.
 */
@Data
public class DataBag {
    HashMap<String, Object> pureMap;
    HashMap<String, Object> owner4_zhun_Map;
    HashMap<String, Object> owner4_mo_Map;
    HashMap<String, Object> owner4_wen_Map;
    HashMap<String, Object> owner4_lao_Map;
    HashMap<String, Object> owner7_zhun1_Map;
    HashMap<String, Object> owner7_zhun2_Map;
    HashMap<String, Object> owner7_zhun3_Map;
    HashMap<String, Object> owner7_mo1_Map;
    HashMap<String, Object> owner7_mo2_Map;
    HashMap<String, Object> owner7_wen_Map;
    HashMap<String, Object> owner7_lao1_Map;
    HashMap<String, Object> owner7_lao2_Map;
    HashMap<String, Object> owner7_lao3_Map;
    HashMap<String, Object> decoration_mao_pi_Map;
    HashMap<String, Object> decoration_zhuang_xiu_Map;
    HashMap<String, Object> house_gao_ceng_Map;
    HashMap<String, Object> house_xiao_gao_ceng_Map;
    HashMap<String, Object> house_duo_ceng_Map;
    HashMap<String, Object> house_bie_shu_Map;
    HashMap<String, Object> house_jiu_dian_Map;
    HashMap<String, Object> house_qi_ta_Map;
    HashMap<String, Object> textPureMap;
    HashMap<String, Object> text_owner4_zhun_Map;
    HashMap<String, Object> text_owner4_mo_Map;
    HashMap<String, Object> text_owner4_wen_Map;
    HashMap<String, Object> text_owner4_lao_Map;
    HashMap<String, Object> text_owner7_zhun1_Map;
    HashMap<String, Object> text_owner7_zhun2_Map;
    HashMap<String, Object> text_owner7_zhun3_Map;
    HashMap<String, Object> text_owner7_mo1_Map;
    HashMap<String, Object> text_owner7_mo2_Map;
    HashMap<String, Object> text_owner7_wen_Map;
    HashMap<String, Object> text_owner7_lao1_Map;
    HashMap<String, Object> text_owner7_lao2_Map;
    HashMap<String, Object> text_owner7_lao3_Map;
    HashMap<String, Object> text_decoration_mao_pi_Map;
    HashMap<String, Object> text_decoration_zhuang_xiu_Map;
    HashMap<String, Object> text_house_gao_ceng_Map;
    HashMap<String, Object> text_house_xiao_gao_ceng_Map;
    HashMap<String, Object> text_house_duo_ceng_Map;
    HashMap<String, Object> text_house_bie_shu_Map;
    HashMap<String, Object> text_house_jiu_dian_Map;
    HashMap<String, Object> text_house_qi_ta_Map;
    HashMap<String, Object> msdSales_visit_open_Map; // 销售神客：明查
    HashMap<String, Object> msdSales_visit_secret_Map; // 销售神客：暗访
    HashMap<String, Object> msdSales_competitor_this_Map; // 销售神客：本品
    HashMap<String, Object> msdSales_competitor_competing_Map; // 销售神客：竞品
    HashMap<String, Object> msdSales_week_weekday_Map; // 销售神客：非周末
    HashMap<String, Object> msdSales_week_weekend_Map; // 销售神客：周末
    HashMap<String, Object> msdSales_investigation_first_Map; // 销售神客：首访
    HashMap<String, Object> msdSales_investigation_again_Map; // 销售神客：复访
    HashMap<String, Object> msdProperty_visit_open_Map; // 物业神客：明查
    HashMap<String, Object> msdProperty_visit_secret_Map; // 物业神客：暗访
    HashMap<String, Object> msdProperty_competitor_this_Map; // 物业神客：本品
    HashMap<String, Object> msdProperty_competitor_competing_Map; // 物业神客：竞品
    HashMap<String, Object> msdProperty_week_weekday_Map; // 物业神客：非周末
    HashMap<String, Object> msdProperty_week_weekend_Map; // 物业神客：周末
    HashMap<String, Object> msdProperty_investigation_first_Map; // 物业神客：首访
    HashMap<String, Object> msdProperty_investigation_again_Map; // 物业神客：复访
    List<HashMap<String, Object>> dataList;
    HashMap<String, HashMap<String, Object>> mixedAttributeMap; // 满意度组合属性

    public void clearData() {
        clearMap(pureMap);
        clearMap(owner4_zhun_Map);
        clearMap(owner4_mo_Map);
        clearMap(owner4_wen_Map);
        clearMap(owner4_lao_Map);
        clearMap(owner7_zhun1_Map);
        clearMap(owner7_zhun2_Map);
        clearMap(owner7_zhun3_Map);
        clearMap(owner7_mo1_Map);
        clearMap(owner7_mo2_Map);
        clearMap(owner7_wen_Map);
        clearMap(owner7_lao1_Map);
        clearMap(owner7_lao2_Map);
        clearMap(owner7_lao3_Map);
        clearMap(decoration_mao_pi_Map);
        clearMap(decoration_zhuang_xiu_Map);
        clearMap(house_gao_ceng_Map);
        clearMap(house_xiao_gao_ceng_Map);
        clearMap(house_duo_ceng_Map);
        clearMap(house_bie_shu_Map);
        clearMap(house_jiu_dian_Map);
        clearMap(house_qi_ta_Map);
        clearMap(textPureMap);
        clearMap(text_owner4_zhun_Map);
        clearMap(text_owner4_mo_Map);
        clearMap(text_owner4_wen_Map);
        clearMap(text_owner4_lao_Map);
        clearMap(text_owner7_zhun1_Map);
        clearMap(text_owner7_zhun2_Map);
        clearMap(text_owner7_zhun3_Map);
        clearMap(text_owner7_mo1_Map);
        clearMap(text_owner7_mo2_Map);
        clearMap(text_owner7_wen_Map);
        clearMap(text_owner7_lao1_Map);
        clearMap(text_owner7_lao2_Map);
        clearMap(text_owner7_lao3_Map);
        clearMap(text_decoration_mao_pi_Map);
        clearMap(text_decoration_zhuang_xiu_Map);
        clearMap(text_house_gao_ceng_Map);
        clearMap(text_house_xiao_gao_ceng_Map);
        clearMap(text_house_duo_ceng_Map);
        clearMap(text_house_bie_shu_Map);
        clearMap(text_house_jiu_dian_Map);
        clearMap(text_house_qi_ta_Map);
        clearMap(msdSales_visit_open_Map);
        clearMap(msdSales_visit_secret_Map);
        clearMap(msdSales_competitor_this_Map);
        clearMap(msdSales_competitor_competing_Map);
        clearMap(msdSales_week_weekday_Map);
        clearMap(msdSales_week_weekend_Map);
        clearMap(msdSales_investigation_first_Map);
        clearMap(msdSales_investigation_again_Map);
        clearMap(msdProperty_visit_open_Map);
        clearMap(msdProperty_visit_secret_Map);
        clearMap(msdProperty_competitor_this_Map);
        clearMap(msdProperty_competitor_competing_Map);
        clearMap(msdProperty_week_weekday_Map);
        clearMap(msdProperty_week_weekend_Map);
        clearMap(msdProperty_investigation_first_Map);
        clearMap(msdProperty_investigation_again_Map);
        if (dataList != null) {
            for (HashMap dataMap : dataList) {
                clearMap(dataMap);
                dataMap = null;
            }
            dataList.clear();
        }
        // todo mixedAttributeMap 是否需要清理？
        if (mixedAttributeMap != null) {
            for (HashMap dataMap : mixedAttributeMap.values()) {
                clearMap(dataMap);
                dataMap = null;
            }
            mixedAttributeMap.clear();
        }
    }

    private void clearMap(HashMap dataMap) {
        if (dataMap != null)
            dataMap.clear();
    }

    public HashMap<String, Object> attributeDataMap(String attribute) {
        if (StringUtils.contains(attribute, "-")) {
            return mixedAttributeDataMap(attribute);
        }

        HashMap<String, Object> result = null;
        switch (attribute) {
            case "准业主":
                result = owner4_zhun_Map;
                break;
            case "磨合期":
                result = owner4_mo_Map;
                break;
            case "稳定期":
                result = owner4_wen_Map;
                break;
            case "老业主":
                result = owner4_lao_Map;
                break;
            case "准业主1":
                result = owner7_zhun1_Map;
                break;
            case "准业主2":
                result = owner7_zhun2_Map;
                break;
            case "准业主3":
                result = owner7_zhun3_Map;
                break;
            case "磨合期1":
                result = owner7_mo1_Map;
                break;
            case "磨合期2":
                result = owner7_mo2_Map;
                break;
            case "老业主1":
                result = owner7_lao1_Map;
                break;
            case "老业主2":
                result = owner7_lao2_Map;
                break;
            case "老业主3":
                result = owner7_lao3_Map;
                break;
            case "毛坯房":
                result = decoration_mao_pi_Map;
                break;
            case "装修房":
                result = decoration_zhuang_xiu_Map;
                break;
            case "高层":
                result = house_gao_ceng_Map;
                break;
            case "小高层":
                result = house_xiao_gao_ceng_Map;
                break;
            case "多层/花园洋房":
                result = house_duo_ceng_Map;
                break;
            case "别墅":
                result = house_bie_shu_Map;
                break;
            case "酒店式公寓/SOHO公寓":
                result = house_jiu_dian_Map;
                break;
            case "其他":
                result = house_qi_ta_Map;
                break;
            case "磨稳装修":
                // todo
                break;
            default:
                result = pureMap;
                break;
        }
        return result;
    }

    public HashMap<String, Object> mixedAttributeDataMap(String attribute) {
        if (mixedAttributeMap == null) {
            mixedAttributeMap = new HashMap<>();
        }

        Set<String> allAttributes = new LinkedHashSet<>();
        allAttributes.addAll(Arrays.asList(BrainDataSegment.OWNER_FOUR_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.OWNER_SEVEN_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.HOUSE_TYPE_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.DECORATION_TYPE_ARRAY));

        String[] attributeArray = attribute.split("-");
        List<String> sortAttributes = allAttributes.stream()
                .filter(x -> Arrays.asList(attributeArray).contains(x))
                .collect(Collectors.toList());

        return mixedAttributeMap.computeIfAbsent(StringUtils.join(sortAttributes, "-"), k -> new HashMap<>());
    }

    public HashMap<String, Object> textAttributeDataMap(String attribute) {
        HashMap<String, Object> result = null;
        switch (attribute) {
            case "准业主":
                result = text_owner4_zhun_Map;
                break;
            case "磨合期":
                result = text_owner4_mo_Map;
                break;
            case "稳定期":
                result = text_owner4_wen_Map;
                break;
            case "老业主":
                result = text_owner4_lao_Map;
                break;
            case "准业主1":
                result = text_owner7_zhun1_Map;
                break;
            case "准业主2":
                result = text_owner7_zhun2_Map;
                break;
            case "准业主3":
                result = text_owner7_zhun3_Map;
                break;
            case "磨合期1":
                result = text_owner7_mo1_Map;
                break;
            case "磨合期2":
                result = text_owner7_mo2_Map;
                break;
            case "老业主1":
                result = text_owner7_lao1_Map;
                break;
            case "老业主2":
                result = text_owner7_lao2_Map;
                break;
            case "老业主3":
                result = text_owner7_lao3_Map;
                break;
            case "毛坯房":
                result = text_decoration_mao_pi_Map;
                break;
            case "装修房":
                result = text_decoration_zhuang_xiu_Map;
                break;
            case "高层":
                result = text_house_gao_ceng_Map;
                break;
            case "小高层":
                result = text_house_xiao_gao_ceng_Map;
                break;
            case "多层/花园洋房":
                result = text_house_duo_ceng_Map;
                break;
            case "别墅":
                result = text_house_bie_shu_Map;
                break;
            case "酒店式公寓/SOHO公寓":
                result = text_house_jiu_dian_Map;
                break;
            case "其他":
                result = text_house_qi_ta_Map;
                break;
            case "磨稳装修":
                // todo
                break;
            default:
                result = textPureMap;
                break;
        }
        return result;
    }

    public void putDataMap(HashMap<String, Object> dataMap, String attribute) {
        switch (attribute) {
            case "准业主":
                setOwner4_zhun_Map(dataMap);
                break;
            case "磨合期":
                setOwner4_mo_Map(dataMap);
                break;
            case "稳定期":
                setOwner4_wen_Map(dataMap);
                setOwner7_wen_Map(dataMap);
                break;
            case "老业主":
                setOwner4_lao_Map(dataMap);
                break;
            case "准业主1":
                setOwner7_zhun1_Map(dataMap);
                break;
            case "准业主2":
                setOwner7_zhun2_Map(dataMap);
                break;
            case "准业主3":
                setOwner7_zhun3_Map(dataMap);
                break;
            case "磨合期1":
                setOwner7_mo1_Map(dataMap);
                break;
            case "磨合期2":
                setOwner7_mo2_Map(dataMap);
                break;
            case "老业主1":
                setOwner7_lao1_Map(dataMap);
                break;
            case "老业主2":
                setOwner7_lao2_Map(dataMap);
                break;
            case "老业主3":
                setOwner7_lao3_Map(dataMap);
                break;
            case "毛坯房":
                setDecoration_mao_pi_Map(dataMap);
                break;
            case "装修房":
                setDecoration_zhuang_xiu_Map(dataMap);
                break;
            case "高层":
                setHouse_gao_ceng_Map(dataMap);
                break;
            case "小高层":
                setHouse_xiao_gao_ceng_Map(dataMap);
                break;
            case "多层/花园洋房":
                setHouse_duo_ceng_Map(dataMap);
                break;
            case "别墅":
                setHouse_bie_shu_Map(dataMap);
                break;
            case "酒店式公寓/SOHO公寓":
                setHouse_jiu_dian_Map(dataMap);
                break;
            case "其他":
                setHouse_qi_ta_Map(dataMap);
                break;
            default:
                setPureMap(dataMap);
                break;
        }
    }

    public void putTextMap(HashMap<String, Object> dataMap, String attribute) {
        switch (attribute) {
            case "准业主":
                setText_owner4_zhun_Map(dataMap);
                break;
            case "磨合期":
                setText_owner4_mo_Map(dataMap);
                break;
            case "稳定期":
                setText_owner4_wen_Map(dataMap);
                setText_owner7_wen_Map(dataMap);
                break;
            case "老业主":
                setText_owner4_lao_Map(dataMap);
                break;
            case "准业主1":
                setText_owner7_zhun1_Map(dataMap);
                break;
            case "准业主2":
                setText_owner7_zhun2_Map(dataMap);
                break;
            case "准业主3":
                setText_owner7_zhun3_Map(dataMap);
                break;
            case "磨合期1":
                setText_owner7_mo1_Map(dataMap);
                break;
            case "磨合期2":
                setText_owner7_mo2_Map(dataMap);
                break;
            case "老业主1":
                setText_owner7_lao1_Map(dataMap);
                break;
            case "老业主2":
                setText_owner7_lao2_Map(dataMap);
                break;
            case "老业主3":
                setText_owner7_lao3_Map(dataMap);
                break;
            case "毛坯房":
                setText_decoration_mao_pi_Map(dataMap);
                break;
            case "装修房":
                setText_decoration_zhuang_xiu_Map(dataMap);
                break;
            case "高层":
                setText_house_gao_ceng_Map(dataMap);
                break;
            case "小高层":
                setText_house_xiao_gao_ceng_Map(dataMap);
                break;
            case "多层/花园洋房":
                setText_house_duo_ceng_Map(dataMap);
                break;
            case "别墅":
                setText_house_bie_shu_Map(dataMap);
                break;
            case "酒店式公寓/SOHO公寓":
                setText_house_jiu_dian_Map(dataMap);
                break;
            case "其他":
                setText_house_qi_ta_Map(dataMap);
                break;
            default:
                setTextPureMap(dataMap);
                break;
        }
    }

    public HashMap<String, Object> msdAttributeDataMap(MsdType msdType, String attribute) {
        HashMap<String, Object> result = null;
        switch (msdType) {
            case MSD_SALES:
                switch (attribute) {
                    case "明查":
                        result = msdSales_visit_open_Map;
                        break;
                    case "暗访":
                        result = msdSales_visit_secret_Map;
                        break;
                    case "本品":
                        result = msdSales_competitor_this_Map;
                        break;
                    case "竞品":
                        result = msdSales_competitor_competing_Map;
                        break;
                    case "非周末":
                        result = msdSales_week_weekday_Map;
                        break;
                    case "周末":
                        result = msdSales_week_weekend_Map;
                        break;
                    case "首访":
                        result = msdSales_investigation_first_Map;
                        break;
                    case "复访":
                        result = msdSales_investigation_again_Map;
                        break;
                    default:
                        result = pureMap;
                        break;
                }
                break;
            case MSD_PROPERTY:
                switch (attribute) {
                    case "明查":
                        result = msdProperty_visit_open_Map;
                        break;
                    case "暗访":
                        result = msdProperty_visit_secret_Map;
                        break;
                    case "本品":
                        result = msdProperty_competitor_this_Map;
                        break;
                    case "竞品":
                        result = msdProperty_competitor_competing_Map;
                        break;
                    case "非周末":
                        result = msdProperty_week_weekday_Map;
                        break;
                    case "周末":
                        result = msdProperty_week_weekend_Map;
                        break;
                    case "首访":
                        result = msdProperty_investigation_first_Map;
                        break;
                    case "复访":
                        result = msdProperty_investigation_again_Map;
                        break;
                    default:
                        result = pureMap;
                        break;
                }
                break;
        }

        return result;
    }

    public void putMsdSalesDataMap(HashMap<String, Object> dataMap, String attribute) {
        switch (attribute) {
            case "明查":
                setMsdSales_visit_open_Map(dataMap);
                break;
            case "暗访":
                setMsdSales_visit_secret_Map(dataMap);
                break;
            case "本品":
                setMsdSales_competitor_this_Map(dataMap);
                break;
            case "竞品":
                setMsdSales_competitor_competing_Map(dataMap);
                break;
            case "非周末":
                setMsdSales_week_weekday_Map(dataMap);
                break;
            case "周末":
                setMsdSales_week_weekend_Map(dataMap);
                break;
            case "首访":
                setMsdSales_investigation_first_Map(dataMap);
                break;
            case "复访":
                setMsdSales_investigation_again_Map(dataMap);
                break;
        }
    }

    public void putMsdPropertyDataMap(HashMap<String, Object> dataMap, String attribute) {
        switch (attribute) {
            case "明查":
                setMsdProperty_visit_open_Map(dataMap);
                break;
            case "暗访":
                setMsdProperty_visit_secret_Map(dataMap);
                break;
            case "本品":
                setMsdProperty_competitor_this_Map(dataMap);
                break;
            case "竞品":
                setMsdProperty_competitor_competing_Map(dataMap);
                break;
            case "非周末":
                setMsdProperty_week_weekday_Map(dataMap);
                break;
            case "周末":
                setMsdProperty_week_weekend_Map(dataMap);
                break;
            case "首访":
                setMsdProperty_investigation_first_Map(dataMap);
                break;
            case "复访":
                setMsdProperty_investigation_again_Map(dataMap);
                break;
        }
    }

    public void putMsdDataMap(HashMap<String, Object> dataMap, MsdType msdType, String attribute) {
        switch (msdType) {
            case MSD_SALES:
                switch (attribute) {
                    case "明查":
                        setMsdSales_visit_open_Map(dataMap);
                        break;
                    case "暗访":
                        setMsdSales_visit_secret_Map(dataMap);
                        break;
                    case "本品":
                        setMsdSales_competitor_this_Map(dataMap);
                        break;
                    case "竞品":
                        setMsdSales_competitor_competing_Map(dataMap);
                        break;
                    case "非周末":
                        setMsdSales_week_weekday_Map(dataMap);
                        break;
                    case "周末":
                        setMsdSales_week_weekend_Map(dataMap);
                        break;
                    case "首访":
                        setMsdSales_investigation_first_Map(dataMap);
                        break;
                    case "复访":
                        setMsdSales_investigation_again_Map(dataMap);
                        break;
                    default:
                        setPureMap(dataMap);
                        break;
                }
                break;
            case MSD_PROPERTY:
                switch (attribute) {
                    case "明查":
                        setMsdProperty_visit_open_Map(dataMap);
                        break;
                    case "暗访":
                        setMsdProperty_visit_secret_Map(dataMap);
                        break;
                    case "本品":
                        setMsdProperty_competitor_this_Map(dataMap);
                        break;
                    case "竞品":
                        setMsdProperty_competitor_competing_Map(dataMap);
                        break;
                    case "非周末":
                        setMsdProperty_week_weekday_Map(dataMap);
                        break;
                    case "周末":
                        setMsdProperty_week_weekend_Map(dataMap);
                        break;
                    case "首访":
                        setMsdProperty_investigation_first_Map(dataMap);
                        break;
                    case "复访":
                        setMsdProperty_investigation_again_Map(dataMap);
                        break;
                    default:
                        setPureMap(dataMap);
                        break;
                }
                break;
        }
    }
}
