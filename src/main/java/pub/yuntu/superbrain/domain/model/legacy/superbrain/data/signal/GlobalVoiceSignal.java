package pub.yuntu.superbrain.domain.model.legacy.superbrain.data.signal;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import pub.yuntu.superbrain.domain.model.legacy.stream.datasource.DataFinder;

/**
 * Created by <PERSON><PERSON><PERSON> on 2022/3/30 17:05.
 */
@Slf4j
@Data
public class GlobalVoiceSignal extends Signal {

    @Override
    public DataFinder city() {
        return null;
    }

    @Override
    public DataFinder categoryId() {
        return null;
    }

    @Override
    public DataFinder lastOrgCode() {
        return null;
    }

    @Override
    public String uniqueField() {
        return null;
    }

    @Override
    public DataFinder ownerFour() {
        return null;
    }

    @Override
    public DataFinder ownerSeven() {
        return null;
    }

    @Override
    public DataFinder decoration() {
        return null;
    }

    @Override
    public DataFinder district() {
        return null;
    }

    @Override
    public DataFinder customerName() {
        return null;
    }

    @Override
    public DataFinder splitUnitId() {
        return new DataFinder(true, "-10101");
    }
}
