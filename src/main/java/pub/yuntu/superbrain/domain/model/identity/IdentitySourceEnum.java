package pub.yuntu.superbrain.domain.model.identity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description 身份类型枚举
 * @createTime 2022年09月14日 15:42:56
 */
@AllArgsConstructor
@Getter
public enum IdentitySourceEnum {

    SPLIT_UNIT("SplitUnit"),
    ALLIANCE_UNIT("AllianceSplitUnit"),
    INDUSTRY("industry"),
    KNOWLEDGE("KnowledgePoint"),
    LIANJIA("LianJia"),
    STAT("Stat"),
    UNIQUE("unique");

    private final String value;

    public static IdentitySourceEnum getByValue(String value) {
        for (IdentitySourceEnum type : values()) {
            if (StringUtils.equalsIgnoreCase(type.getValue(), StringUtils.trim(value))) {
                return type;
            }
        }
        return null;
    }

}
