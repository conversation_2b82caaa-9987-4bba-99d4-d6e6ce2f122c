package pub.yuntu.superbrain.domain.model.keyPath.path;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import pub.yuntu.superbrain.domain.model.keyPath.router.Branch;
import pub.yuntu.superbrain.domain.model.keyPath.router.Router;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 路径工具类
 * @createTime 2024年01月02日 15:53:46
 */
@Slf4j
public class PathHelper {

    public static void checkPaths(List<Path> paths) {
        // 处理 Path 名称
        pathNameCheck(paths);
        // 检查是否存在重名 Path
        duplicatePathCheck(paths);
    }

    // 检查路径列表中的每个路径的名称
    private static void pathNameCheck(List<Path> paths) {
        // 使用forEach方法迭代paths列表中的每个路径
        paths.forEach(path -> {
            // 获取路径的名称
            String pathName = path.getName();
            // 使用Objects.requireNonNull方法检查pathName是否为空，如果为空则抛出NullPointerException，同时传递错误信息"Path没有设置名称"
            Objects.requireNonNull(pathName, "Path没有设置名称");
            // 使用trim方法清理pathName中的前后空白字符，并将清理后的名称设置回路径对象
            path.setName(pathName.trim());
        });
    }

    /**
     * 检查是否存在名称重复的路径，要求路径名称必须唯一
     */
    private static void duplicatePathCheck(List<Path> paths) {
        Iterator<Path> iterator = paths.iterator();
        while (iterator.hasNext()) {
            Path path = iterator.next();
            while (iterator.hasNext()) {
                Path next = iterator.next();
                if (path.getName().equals(next.getName())) {
                    throw new NullPointerException("存在重名 Path 路径：" + path.getName());
                }
            }
        }
    }

    public static void parsePaths(List<Path> paths) {
        // 将 Path 整理成 Map，key 为 Path 的 name 属性
        Map<String, Path> pathMap = collectPathMap(paths);

        // 遍历paths集合来初始化Path对象的路由信息
        for (int i = 0; i < paths.size(); i++) {
            Path path = paths.get(i);
            Router router = path.getRouter();
            // 默认设置第一个路径为起点
            if (i == 0) {
                router.setPrev(new StartPath());
            }
            parsePath(paths, pathMap, path);
        }
    }

    public static void parsePath(List<Path> paths, Map<String, Path> pathMap, Path path) {
        Router router = path.getRouter();
        // 如果 Path 没有设置 router，则路由到结束点
        if (null == router) {
            router = new Router();
            router.setPrev(new StartPath());
            router.setNext(new EndPath());
            path.setRouter(router);
            return;
        }
        // 找来源路径，paths 中有 to 指向当前路径的第一个路径，则设置 fromPath
        if (null == router.getPrev()) {
            Path fromPath = findToPathByName(paths, path.getName());
            if (null != fromPath) {
                router.setPrev(fromPath);
            }
        }
        // 如果 Path 设置了直接的 to，直接设置 toPath
        if (StringUtils.isNotBlank(router.getTo())) {
            Path toPath = pathMap.get(router.getTo().trim());
            router.setNext(toPath);
            return;
        }
        // 否则处理分支 Branches
        List<Branch> branches = router.getBranches();
        if (null != branches && !branches.isEmpty()) {
            branches.forEach(branch -> {
                String toPathName = branch.getTo();
                if (StringUtils.isBlank(toPathName)) {
                    throw new IllegalArgumentException("branch 中没有定义 to 路径名称");
                }
                if (StringUtils.equalsIgnoreCase(toPathName, "end")) {
                    branch.setToPath(new EndPath());
                    return;
                }
                Path toPath = Optional.ofNullable(pathMap.get(toPathName.trim()))
                        .orElseThrow(() -> new IllegalArgumentException("branch 中 to 路径名称不存在: " + toPathName));
                branch.setToPath(toPath);
            });
            return;
        }

        // 没有配置路由，结束
        router.setNext(new EndPath());
    }

    @NotNull
    public static Map<String, Path> collectPathMap(List<Path> paths) {
        Map<String, Path> pathMap = Maps.newHashMap();
        paths.forEach(path -> pathMap.put(path.getName().trim(), path));
        return pathMap;
    }

    public static void printPaths(List<Path> paths) {
        final Map<String, Path> pathMap = collectPathMap(paths);
        final Path startPath = getStartPath(paths);
        pathMap.remove(startPath.getName());

        int level = 0;
        int repeatTime = 4;
        final String indentation = " ";

        log.info("Router Start: " + indentation + startPath.getName());

        Path secondPath = startPath.getRouter().getNext();
        if (isEndPath(secondPath)) {
            log.info(indentation + " -> End");
        } else {
            for (Map.Entry<String, Path> entry : pathMap.entrySet()) {
                level++;
                String pathName = entry.getKey();
                Path path = entry.getValue();
                if (path == null) {
                    log.info("Path " + pathName + " is null");
                } else {
                    Router router = path.getRouter();
                    if (router == null) {
                        log.info("Path " + pathName + " does not have router");
                    } else {
                        Path toPath = router.getNext();
                        if (toPath != null) {
                            log.info(StringUtils.repeat(indentation, level * repeatTime) + " -> " + pathName + ": " + toPath.getName());
                        } else {
                            List<Branch> branches = router.getBranches();
                            if (branches != null && !branches.isEmpty()) {
                                for (Branch branch : branches) {
                                    log.info(StringUtils.repeat(indentation, level * repeatTime) + " -> " + pathName + ": " + branch.getToPath().getName());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 打印路径的路由信息
     * @param path 路径对象
     */
    public static void printPathRoute(Path path) {
        Router router = path.getRouter();
        String from = (router.getPrev() != null) ? router.getPrev().getName() : "-";
        String to = (router.getNext() != null) ? router.getNext().getName() : "";

        if (router.getNext() == null && router.getBranches() != null && !router.getBranches().isEmpty()) {
            // 当toPath为空，但存在分支时，打印所有分支的toPath名称
            List<String> list = Lists.newArrayList();
            for (Branch branch : router.getBranches()) {
                list.add(String.format("Branch#%s", branch.getToPath().getName()));
            }
            to = String.join(", ", list);
        }

        // 现在将起点、终点和分支打印出来
//        log.info("-->> Path:{}, from:{} -> to:{}", path.getName(), from, to);

        printPathRouteChain(path, to);
    }

    /**
     * 打印路径的路由链
     *
     * @param path 路径对象
     * @param to
     */
    private static void printPathRouteChain(Path path, String to) {
        if (null == path || null == path.getRouter()) return;

        Path prev = path.getRouter().getPrev();
        StringBuilder builder = new StringBuilder();
        while (null != prev) {
            builder.insert(0, " -> " + prev.getName());
            Router prevRouter = prev.getRouter();
            if (null == prevRouter) break;
            prev = prevRouter.getPrev();
        }
        builder.append(" -> ").append(path.getName());
        builder.insert(0, "[" + path.getName() + "] ## ");
        log.info("-->> Path Route Chain: {}, to: {}", builder, to);
    }

    private static boolean isEndPath(Path path) {
        Router router = path.getRouter();
        if (null == router) return false;
        Path toPath = router.getNext();
        if (null == toPath) return false;
        return StringUtils.equalsIgnoreCase(toPath.getName(), "end");
    }

    public static Path findPathByName(List<Path> paths, String pathName) {
        return paths.stream()
                .filter(path -> StringUtils.equalsIgnoreCase(path.getName(), pathName))
                .findFirst()
                .orElseThrow(() -> new NullPointerException("没有找到Path: " + pathName));
    }

    public static Path findToPathByName(List<Path> paths, String pathName) {
        return paths.stream()
                .filter(path -> {
                    Router router = path.getRouter();
                    if (null == router) return false;
                    Path toPath = router.getNext();
                    if (null == toPath) return false;
                    return StringUtils.equalsIgnoreCase(toPath.getName(), pathName);
                })
                .findFirst()
                .orElse(null);
    }

    public static Path getStartPath(List<Path> paths) {
        return paths.stream()
                .filter(path -> StringUtils.equalsIgnoreCase(path.getRouter().getPrev().getName(), "start"))
                .findFirst()
                .orElseThrow(() -> new NullPointerException("没有找到起点 Path"));
    }

}
