package pub.yuntu.superbrain.domain.model.creativity.parser.segment.brainPointProducer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.creativity.CreativityParam;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPointBuilder;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.BrainDataSegment;
import pub.yuntu.superbrain.domain.model.legacy.kg.GraphInteraction;
import pub.yuntu.superbrain.domain.model.legacy.net.grouping.DataBag;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.KMP;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.MatchingGroup;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.MatchingNode;
import pub.yuntu.superbrain.domain.model.legacy.nlp.shorttext.PersonInfo;
import pub.yuntu.superbrain.domain.model.legacy.rh.param.CommercialParam;
import pub.yuntu.superbrain.domain.model.legacy.rh.param.TextParseResult;
import pub.yuntu.superbrain.domain.model.legacy.split.SplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.stream.task.feature.FeatureSplitUnit;
import pub.yuntu.superbrain.domain.model.net.feature.constants.AssemblyEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Qianyi on 2022/3/29 14:43.
 */
@Slf4j
@Data
public class VoiceBrainPointProducer extends AbstractBrainPointProducer {

    List<String> textColumnNames = Arrays.asList("openTextGood1", "openTextGood2", "openTextGood3", "openTextBad1", "openTextBad2", "openTextBad3");

    public List<AbstractDataPoint> produce(CreativityParam param, BrainDataSegment brainDataSegment) {
        List<AbstractDataPoint> result = new ArrayList<>();

        CommercialParam commercialParam = param.getCommercialParam();
        if (commercialParam == null)
            return result;

        List<String> periodList = new ArrayList<>();
        brainDataSegment.getPeriods().forEach(x -> periodList.addAll(getSubPeriods(x)));

        List<HashMap<String, Object>> allVoiceList = new ArrayList<>();
        for (Long splitUnitDbId : brainDataSegment.getSplitUnitDbIdList()) {
            SplitUnit splitUnit = commercialParam.getFullSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                continue;
            List<HashMap<String, Object>> voiceList = voiceOfUnit(commercialParam, periodList, splitUnit);
            allVoiceList.addAll(voiceList);
        }

        List<HashMap<String, Object>> filterVoiceList = filterByAttribute(brainDataSegment, allVoiceList);
        result = producePoint(param, brainDataSegment, filterVoiceList);

        return result;
    }

    public List<AbstractDataPoint> buildDataNetwork(CreativityParam param, BrainDataSegment brainDataSegment) {
        return this.buildDataNetwork(param, brainDataSegment, "voice");
    }

    public List<AbstractDataPoint> buildDataNetwork(CreativityParam param, BrainDataSegment brainDataSegment, String type) {
        List<AbstractDataPoint> result = new ArrayList<>();

        CommercialParam commercialParam = param.getCommercialParam();
        if (commercialParam == null
                || brainDataSegment.getSplitUnitDbIdList() == null
                || brainDataSegment.getPeriods() == null
                || brainDataSegment.getAttributeList() == null
                || brainDataSegment.getPlanarCodeAndGraphInteractionMap() == null
                || StringUtils.isBlank(brainDataSegment.getDataType()))
            return result;

        for (Long splitUnitDbId : brainDataSegment.getSplitUnitDbIdList()) {
            FeatureSplitUnit splitUnit = commercialParam.getFullSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                splitUnit = commercialParam.getFullAllianceSplitMap().get(splitUnitDbId + "");
            if (splitUnit == null)
                continue;

            String orgUnitCode = null;
            if (splitUnit instanceof SplitUnit)
                orgUnitCode = ((SplitUnit) splitUnit).getOrgUnitCode();

            for (String period : brainDataSegment.getPeriods()) {
                List<String> subPeriods = getSubPeriods(period);

                for (String[] attributeArray : brainDataSegment.getAttributeList()) {
                    String attribute = StringUtils.join(attributeArray, "-");

                    for (String planarCode : brainDataSegment.getPlanarCodeAndGraphInteractionMap().keySet()) {
                        GraphInteraction interaction = brainDataSegment.getPlanarCodeAndGraphInteractionMap().get(planarCode);
                        if (interaction == null)
                            continue;

                        List<String> shouldContainCodes = new ArrayList<>(Collections.singletonList(interaction.getCode()));
                        String combinationType = interaction.getCombinationType();
                        AssemblyEnum assemblyEnum = AssemblyEnum.getByValue(combinationType);
                        String childCodes = interaction.getChildCodes();
                        if (StringUtils.isNotBlank(combinationType) && StringUtils.isNotBlank(childCodes)) {
                            // 文本合并触点
                            List<String> finalChildCodes = param.getChildCodesMap().get(interaction.getCode());
                            if (finalChildCodes == null) {
                                finalChildCodes = new ArrayList<>();
                                codesOfCombinationInteraction(param, childCodes, finalChildCodes);
                                param.getChildCodesMap().put(interaction.getCode(), finalChildCodes);
                            }
                            shouldContainCodes = finalChildCodes;
                        }

                        DataPoint dataPoint = DataPointBuilder.aDataPoint()
                                .splitName(splitUnit.name())
                                .orgUnitCode(orgUnitCode)
                                .attribute(attribute)
                                .indexName(interaction.getName())
                                .dataPeriod(period)
                                .subPeriods(subPeriods)
                                .planarCode(planarCode)
                                .interactionId(interaction.getId())
                                .dataType(brainDataSegment.getDataType())
                                .assemblyEnum(assemblyEnum)
                                .shouldContainCodes(shouldContainCodes)
                                .nameType(brainDataSegment.getNameType())
                                .filterVoiceConditions(brainDataSegment.getFilterVoiceConditions())
                                .networkType(type)
                                .build();
                        setPointName(param, brainDataSegment, dataPoint);
                        result.add(dataPoint);
                    }
                }
            }
        }
        return result;
    }

    public List<HashMap<String, Object>> voiceOfUnit(CommercialParam commercialParam,
                                                     List<String> periodList,
                                                     SplitUnit splitUnit) {
        List<HashMap<String, Object>> result = new ArrayList<>();

        List<SplitUnit> childUnits = commercialParam.getFullSplitMap().values().stream()
                .filter(x -> StringUtils.startsWith(x.getOrgUnitCode(), splitUnit.getOrgUnitCode()))
                .collect(Collectors.toList());
        if (childUnits.isEmpty())
            return result;

        for (SplitUnit childUnit : childUnits) {
            for (String period : periodList) {
                DataBag dataBag = childUnit.getDataCar().getZataMap().get(period);
                if (dataBag == null)
                    continue;
                List<HashMap<String, Object>> dataList = dataBag.getDataList();
                if (dataList == null)
                    continue;
                result.addAll(dataList);
            }
        }
        return result;
    }

    public List<HashMap<String, Object>> filterByAttribute(BrainDataSegment brainDataSegment,
                                                           List<HashMap<String, Object>> voiceList) {
        List<HashMap<String, Object>> result = new ArrayList<>();
        Set<String> existVidSet = new HashSet<>();

        for (String[] attributeArray : brainDataSegment.getAttributeList()) {
            List<HashMap<String, Object>> filter = new ArrayList<>(voiceList);

            for (String attribute : attributeArray) {
                String columnName = null;

                switch (attribute) {
                    case "准业主":
                    case "磨合期":
                    case "稳定期":
                    case "老业主":
                        columnName = "dOwnerTypeFour";
                        break;
                    case "准业主1":
                    case "准业主2":
                    case "准业主3":
                    case "磨合期1":
                    case "磨合期2":
                    case "老业主1":
                    case "老业主2":
                    case "老业主3":
                        columnName = "dOwnerTypeSevenPlus";
                        break;
                    case "毛坯房":
                    case "装修房":
                        columnName = "dDecorationType";
                        break;
                    case "高层":
                    case "小高层":
                    case "多层/花园洋房":
                    case "别墅":
                    case "酒店式公寓/SOHO公寓":
                    case "其他":
                        columnName = "dHouseType";
                        break;
                    default:
                        break;
                }

                if (StringUtils.isNotBlank(columnName)) {
                    String finalColumnName = columnName.toUpperCase();
                    filter = filter.stream()
                            .filter(x -> StringUtils.equalsIgnoreCase(x.get(finalColumnName) + "", attribute))
                            .collect(Collectors.toList());
                }
            }
            filter.stream()
                    .filter(x -> !existVidSet.contains(x.get("VID") + ""))
                    .forEach(x -> {
                        result.add(x);
                        existVidSet.add(x.get("VID") + "");
                    });
        }

        return result;
    }

    public List<AbstractDataPoint> producePoint(CreativityParam param,
                                                BrainDataSegment brainDataSegment,
                                                List<HashMap<String, Object>> voiceList) {
        List<AbstractDataPoint> result = new ArrayList<>();
        Set<String> existTexts = new HashSet<>();  // 防止有重复数据

        for (HashMap<String, Object> data : voiceList) {
            List<String> columns = textColumnNames.stream().map(String::toUpperCase).collect(Collectors.toList());
            switch (brainDataSegment.getDataType().toUpperCase()) {
                case "ALL_VOICE":
                    break;
                case "GOOD_VOICE":
                    columns = columns.stream().filter(x -> StringUtils.containsIgnoreCase(x, "good")).collect(Collectors.toList());
                    break;
                case "BAD_VOICE":
                    columns = columns.stream().filter(x -> StringUtils.containsIgnoreCase(x, "bad")).collect(Collectors.toList());
                    break;
            }
            String text = getVoiceByColumnName(columns, data);
            if (StringUtils.isBlank(text) || existTexts.contains(text))
                continue;
            existTexts.add(text);

            List<String> codeList = codesOfText(param, data, text);
            if (codeList == null || codeList.isEmpty())
                continue;

            for (String planarCode : brainDataSegment.getPlanarCodeAndGraphInteractionMap().keySet()) {
                GraphInteraction interaction = brainDataSegment.getPlanarCodeAndGraphInteractionMap().get(planarCode);
                if (interaction == null)
                    continue;

                List<String> shouldContainCodes = Collections.singletonList(interaction.getCode());
                String combinationType = interaction.getCombinationType();
                AssemblyEnum assemblyEnum = AssemblyEnum.getByValue(combinationType);
                String childCodes = interaction.getChildCodes();
                if (StringUtils.isNotBlank(combinationType) && StringUtils.isNotBlank(childCodes)) {
                    // 文本合并触点
                    List<String> finalChildCodes = param.getChildCodesMap().get(interaction.getCode());
                    if (finalChildCodes == null) {
                        finalChildCodes = new ArrayList<>();
                        codesOfCombinationInteraction(param, childCodes, finalChildCodes);
                        param.getChildCodesMap().put(interaction.getCode(), finalChildCodes);
                    }
                    shouldContainCodes = finalChildCodes;
                }

                boolean valid = false;
                if (assemblyEnum == AssemblyEnum.MULTIPLE2) {
                    // 同一段话同时包含全部childCodes才算有效
                    if (codeList.containsAll(shouldContainCodes)) {
                        valid = true;
                    }
                } else {
                    for (String code : shouldContainCodes) {
                        if (codeList.contains(code)) {
                            valid = true;
                            break;
                        }
                    }
                }

                if (valid) {
                    DataPoint dataPoint = DataPointBuilder.aDataPoint()
                            .id(data.get("VID") + "")
                            .splitName(data.get("LASTORG") + "")
                            .indexName(interaction.getName())
                            .dataPeriod(data.get("CAPTUREPERIOD") + "")
                            .planarCode(planarCode)
                            .interactionId(interaction.getId())
                            .richText(text)
                            .build();
                    setPointName(param, brainDataSegment, dataPoint);
                    result.add(dataPoint);
                    break;
                }
            }
        }

        return result;
    }

    public List<String> codesOfText(CreativityParam param,
                                    HashMap<String, Object> data,
                                    String text) {
        List<String> codeList = param.getTextParseCache().get(text);
        if (codeList != null)
            return codeList;

        codeList = new ArrayList<>();
        String dOwnerTypeFour = data.get(("dOwnerTypeFour").toUpperCase()) + "";
        PersonInfo personInfo = StringUtils.isNotBlank(dOwnerTypeFour) ? new PersonInfo(dOwnerTypeFour) : null;
        List<MatchingGroup> list = param.getKanbanFramework().getCommandParam().getTextParser().parse(text, true, personInfo, KMP.OPEN_TYPE_TEXT, false);
        if (list == null || list.isEmpty()) {
            param.getTextParseCache().put(text, codeList);
            return codeList;
        }

        for (MatchingGroup mGroup : list) {
            for (MatchingNode mNode : mGroup.getNodeList()) {
                codeList.add(mNode.getInteraction().getCode());
            }
        }
        param.getTextParseCache().put(text, codeList);

        return codeList;
    }

    public TextParseResult _codesOfText(CommercialParam commercialParam,
                                        HashMap data,
                                        String text) {
        TextParseResult result = commercialParam.getTextParseCache().get(text);
        if (result != null)
            return result;

        result = new TextParseResult();
        String dOwnerTypeFour = getDataValue(data, "dOwnerTypeFour");
        PersonInfo personInfo = StringUtils.isNotBlank(dOwnerTypeFour) ? new PersonInfo(dOwnerTypeFour) : null;
        List<MatchingGroup> list = commercialParam.getCommandParam().getTextParser().parse(text, true, personInfo, KMP.OPEN_TYPE_TEXT, false);
        if (list == null || list.isEmpty()) {
            commercialParam.getTextParseCache().put(text, result);
            return result;
        }

        for (MatchingGroup mGroup : list) {
            for (MatchingNode mNode : mGroup.getNodeList()) {
                result.getList().add(mNode.getInteraction().getCode());
                result.addWordCount(mNode.getFullKeyword());
            }
        }
        commercialParam.getTextParseCache().put(text, result);

        return result;
    }

    public void setInstanceList(CommercialParam commercialParam,
                                HashMap mapData,
                                List<AbstractDataPoint> points,
                                List<AbstractDataPoint> wordCloudPoints,
                                boolean isIndustry) {
        List<String> goodCodeList = new ArrayList<>();
        List<String> badCodeList = new ArrayList<>();
        HashMap<String, Integer> goodWordCountMap = new HashMap<>();
        HashMap<String, Integer> badWordCountMap = new HashMap<>();
        String goodText = "";
        String badText = "";

        List<AbstractDataPoint> allPoints = new ArrayList<>();
        if (points != null)
            allPoints.addAll(points);
        if (wordCloudPoints != null)
            allPoints.addAll(wordCloudPoints);
        Set<String> dataTypes = allPoints.stream().map(AbstractDataPoint::getDataType).collect(Collectors.toSet());

        if (dataTypes.contains("GOOD_VOICE") || dataTypes.contains("ALL_VOICE")) {
            List<String> columns = textColumnNames.stream()
                    .filter(x -> StringUtils.containsIgnoreCase(x, "good"))
                    .map(String::toUpperCase)
                    .collect(Collectors.toList());
            goodText = getVoiceByColumnName(columns, mapData);
            if (StringUtils.isNotBlank(goodText)) {
                TextParseResult result = _codesOfText(commercialParam, mapData, goodText);
                goodCodeList = result.getList();
                goodWordCountMap = result.getWordCountMap();
            }
        }
        if (dataTypes.contains("BAD_VOICE") || dataTypes.contains("ALL_VOICE")) {
            List<String> columns = textColumnNames.stream()
                    .filter(x -> StringUtils.containsIgnoreCase(x, "bad"))
                    .map(String::toUpperCase)
                    .collect(Collectors.toList());
            badText = getVoiceByColumnName(columns, mapData);
            if (StringUtils.isNotBlank(badText)) {
                TextParseResult result = _codesOfText(commercialParam, mapData, badText);
                badCodeList = result.getList();
                badWordCountMap = result.getWordCountMap();
            }
        }

        if (points != null) {
            for (AbstractDataPoint point : points) {
                List<String> dataCodeList = new ArrayList<>();
                String text = "";
                switch (point.getDataType().toUpperCase()) {
                    case "ALL_VOICE":
                        dataCodeList.addAll(goodCodeList);
                        dataCodeList.addAll(badCodeList);
                        text = goodText + badText;
                        break;
                    case "GOOD_VOICE":
                        dataCodeList.addAll(goodCodeList);
                        text = goodText;
                        break;
                    case "BAD_VOICE":
                        dataCodeList.addAll(badCodeList);
                        text = badText;
                        break;
                }
                if (dataCodeList.isEmpty() || StringUtils.isBlank(text))
                    continue;

                boolean valid = false;
                if (StringUtils.equals(point.getPlanarCode(), "all")) {
                    valid = true;
                } else if (point.getAssemblyEnum() == AssemblyEnum.MULTIPLE2) {
                    // 同一段话同时包含全部childCodes才算有效
                    if (dataCodeList.containsAll(point.getShouldContainCodes())) {
                        valid = true;
                    }
                } else {
                    for (String code : point.getShouldContainCodes()) {
                        if (dataCodeList.contains(code)) {
                            valid = true;
                            break;
                        }
                    }
                }

                if (valid) {
                    // D7-48 [取数]行业开放题取数后可视化界面模糊开发商名称
//                    text = CustomerNickname.replaceCustomerName(text);
                    DataPoint dataPoint = DataPointBuilder.aDataPoint()
                            .id(getDataValue(mapData, "vid"))
                            .splitName(isIndustry ? point.getSplitName() : getDataValue(mapData, "lastOrg"))
                            .orgUnitCode(getDataValue(mapData, "lastOrgCode"))
                            .attribute(point.getAttribute())
                            .indexName(point.getIndexName())
                            .dataPeriod(getDataValue(mapData, "capturePeriod"))
                            .planarCode(point.getPlanarCode())
                            .interactionId(point.getInteractionId())
                            .richText(text)
                            .age(getDataValue(mapData, "dAge"))
                            .gender(getDataValue(mapData, "dGender"))
                            .mobile(getDataValue(mapData, "dMobile"))
                            .nameType(point.getNameType())
                            .networkType("voice")
                            .build();
                    setPointName(dataPoint);
                    point.addInstance(dataPoint);
                }
            }
        }

        if (wordCloudPoints != null) {
            for (AbstractDataPoint point : wordCloudPoints) {
                List<String> dataCodeList = new ArrayList<>();
                switch (point.getDataType().toUpperCase()) {
                    case "ALL_VOICE":
                        dataCodeList.addAll(goodCodeList);
                        dataCodeList.addAll(badCodeList);
                        break;
                    case "GOOD_VOICE":
                        dataCodeList.addAll(goodCodeList);
                        break;
                    case "BAD_VOICE":
                        dataCodeList.addAll(badCodeList);
                        break;
                }
                if (dataCodeList.isEmpty())
                    continue;

                boolean valid = false;
                if (StringUtils.equals(point.getPlanarCode(), "all")) {
                    valid = true;
                } else if (point.getAssemblyEnum() == AssemblyEnum.MULTIPLE2) {
                    // 同一段话同时包含全部childCodes才算有效
                    if (dataCodeList.containsAll(point.getShouldContainCodes())) {
                        valid = true;
                    }
                } else {
                    for (String code : point.getShouldContainCodes()) {
                        if (dataCodeList.contains(code)) {
                            valid = true;
                            break;
                        }
                    }
                }

                if (valid) {
                    DataPoint dataPoint = DataPointBuilder.aDataPoint()
                            .id(getDataValue(mapData, "vid"))
                            .splitName(isIndustry ? point.getSplitName() : getDataValue(mapData, "lastOrg"))
                            .orgUnitCode(getDataValue(mapData, "lastOrgCode"))
                            .attribute(point.getAttribute())
                            .indexName(point.getIndexName())
                            .dataPeriod(getDataValue(mapData, "capturePeriod"))
                            .planarCode(point.getPlanarCode())
                            .interactionId(point.getInteractionId())
                            .networkType("wordCloud")
                            .build();
                    dataPoint.addWordCountMap(goodWordCountMap);
                    dataPoint.addWordCountMap(badWordCountMap);
                    point.addInstance(dataPoint);
                }
            }
        }
    }
}
