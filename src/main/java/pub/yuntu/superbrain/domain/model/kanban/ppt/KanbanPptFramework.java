package pub.yuntu.superbrain.domain.model.kanban.ppt;

import com.aspose.slides.*;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.superbrain.application.kanban.KanbanApplication;
import pub.yuntu.superbrain.config.YmlConfig;
import pub.yuntu.superbrain.domain.component.SlidesEngine;
import pub.yuntu.superbrain.domain.model.creativity.block.TableBlock;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPoint;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicColumn;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicLayout;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicRow;
import pub.yuntu.superbrain.domain.model.kanban.KanbanFrameworkHelper;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.ComponentV6Painter;
import pub.yuntu.superbrain.domain.model.kanban.ppt.layout.LayoutV6Calculator;
import pub.yuntu.superbrain.domain.model.kanban.ppt.layout.LayoutV6ForSnapshotCalculator;
import pub.yuntu.superbrain.domain.model.kanban.ppt.template.Template;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.RhViewpointMappingService;
import pub.yuntu.superbrain.infrastructure.persistence.kanban.KanbanPptLogRepository;
import pub.yuntu.superbrain.infrastructure.persistence.kanban.KanbanPptTaskRepository;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.RhViewpointMappingRepository;
import pub.yuntu.superbrain.infrastructure.persistence.legacy.ViewConfigRepository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 看板 PPT Framework
 * @createTime 2022年12月13日 16:11:59
 */
@Component
@Slf4j
@Data
public class KanbanPptFramework {

    @Autowired
    KanbanPptTaskRepository pptTaskRepository;
    @Autowired
    YmlConfig ymlConfig;
    @Autowired
    ViewConfigRepository viewConfigRepository;
    @Autowired
    KanbanPptTaskService taskService;
    @Autowired
    KanbanApplication kanbanApplication;
    @Autowired
    KanbanPptLogRepository pptLogRepository;
    @Autowired
    RhViewpointMappingService viewpointMappingService;
    @Autowired
    RhViewpointMappingRepository viewpointMappingRepository;

    public String generate(KanbanPptParam param, String unitId) {
        List<KanbanPptPage> pages = param.getPpt().getPages();
        return generate(param, unitId, pages);
    }

    /**
     * 生成指定页码的PPT
     */
    public String generate(KanbanPptParam param, String unitId, int pageNo) {
        List<KanbanPptPage> pages = param.getPpt().getPages();
        KanbanPptPage kanbanPptPage = pages.stream()
                .filter(page -> page.getPageNo() == pageNo)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(String.format("没有页码为%d的PPT页面", pageNo)));
        return generate(param, unitId, Collections.singletonList(kanbanPptPage));
    }

    /**
     * 生成PPT，从YT-Intelligence RhPptService 迁移
     */
    public String generate(KanbanPptParam param, String unitId, List<KanbanPptPage> pages) {
        final long start = System.currentTimeMillis();

        SlidesEngine.initLicense();
        Template template = new Template(5);
        final Presentation ppt = template.load(param.toPresentationParam());
        // 创建绘制器
        ComponentV6Painter painter = new ComponentV6Painter();

        this.readVisual(param, unitId, pages);
        param.setTotalPages(pages.size());
        this.draw(param, ppt, painter, template, pages); // 绘制内容
        PresentationUtil.setFont(ppt, param.getPptFont()); // 统一处理PPT字体
        this.save(param, ppt); // 保存结果
        this.clear(ppt); // PPT文件已写入磁盘，清理内存对象
        this.log(param); // 更新PPT日志
        this.dispose(ppt, template); // 销毁资源

        painter.clear();

        log.info("[{}] Kanban PPT finished，用时：{}", param.getUser(), DateUtil.calPassedTime(start));
        return param.getOutputPathFile();
    }

    public void readVisual(KanbanPptParam param, String unitId, List<KanbanPptPage> pages) {
        YmlConfig ymlConfig = param.getFramework().getYmlConfig();
        String kanbanId = param.getKanban().getId();
        pages.forEach(kanbanPptPage -> {
            DynamicLayout layout = KanbanFrameworkHelper.readPptPageVisual(ymlConfig, kanbanId, unitId, kanbanPptPage.getPageNo());
            kanbanPptPage.setLayout(layout);
        });
    }

    private void draw(KanbanPptParam param, Presentation ppt, ComponentV6Painter painter,
                      Template template, List<KanbanPptPage> pages) {
        // 创建节
        createSections(param, ppt, pages);
        this.drawCoverPage(param, ppt, template);
        this.drawContentPages(param, ppt, painter, template, pages);
        this.drawBackCoverPage(param, ppt, template);
    }

    private static void createSections(KanbanPptParam param, Presentation ppt, List<KanbanPptPage> pages) {
        // 提取页面中配置的"节"
        List<String> sectionNames = pages.stream().map(KanbanPptPage::getSection)
                .collect(Collectors.toList());
        if (sectionNames.isEmpty()) return;

        // 创建节
        ISectionCollection sections = ppt.getSections();
        param.setSectionMap(Maps.newHashMap());
        // 封面，第一个Section里面必须要一个页面，后面会删掉
        ISection coverSection = ppt.getSections().addSection("封面", ppt.getSlides().addEmptySlide(ppt.getLayoutSlides().get_Item(0)));
        // 删除空白的页面
        sections.get_Item(0).getSlidesListOfSection().get_Item(0).remove();
        param.getSectionMap().put("封面", coverSection);
        for (String sectionName : sectionNames) {
            if (param.getSectionMap().containsKey(sectionName)) continue;
            ISection section = sections.appendEmptySection(sectionName); // 前面有封面，所以 +1
            param.getSectionMap().put(sectionName, section);
        }
        // 封底
        ISection backCoverSection = sections.appendEmptySection("封底");
        param.getSectionMap().put("封底", backCoverSection);
    }

    private void drawCoverPage(KanbanPptParam param, Presentation ppt, Template template) {
        final ISlide coverSlide = param.hasSection()
                ? ppt.getSlides().addClone(template.getStyleTemplate().getCoverSlide(), param.sectionMap.get("封面"))
                : ppt.getSlides().addClone(template.getStyleTemplate().getCoverSlide());
        log.info(" >> [{}] Kanban PPT 写入封面", param.getUser());

        if (StringUtils.isBlank(param.getTitle())) return;
        if (null == coverSlide.getShapes() || coverSlide.getShapes().size() == 0) return;

        coverSlide.getShapes().forEach(iShape -> {
            if (iShape instanceof AutoShape) {
                AutoShape titleShape = (AutoShape) iShape;
                PresentationUtil.handleAutoShapeFonts(titleShape, param.getPptFont()); // 设置字体
                final ITextFrame textFrame = titleShape.getTextFrame();
                String textInTemplate = textFrame.getText();
                if (StringUtils.isNotBlank(textInTemplate)) {
                    textInTemplate = textInTemplate.toUpperCase();
                    if (StringUtils.contains(textInTemplate, "TITLE")
                            || StringUtils.contains(textInTemplate, "TEXT")) {
                        textFrame.getTextFrameFormat().setWrapText(NullableBool.True);
                        textFrame.setText(param.getTitle());
                    }
                }
            }
        });
        log.info(" >> [{}] Kanban PPT 写入封面：自定义标题", param.getUser());
    }

    private void drawBackCoverPage(KanbanPptParam param, Presentation ppt, Template template) {
        if (param.hasSection()) {
            ppt.getSlides().addClone(template.getStyleTemplate().getBackCoverSlide(), param.sectionMap.get("封底"));
        } else {
            ppt.getSlides().addClone(template.getStyleTemplate().getBackCoverSlide());
        }
        log.info(" >> [{}] Kanban PPT 写入封底", param.getUser());
    }

    private void drawContentPages(KanbanPptParam param, Presentation ppt, ComponentV6Painter painter,
                                  Template template, List<KanbanPptPage> pages) {
        // 应用自定义剧本可视化配置 6.0 PPT TODO : barCompareColorConfig 应该放入剧本PPT中
        barCompareColorConfig(param, pages);

        for (int i = 0; i < pages.size(); i++) {
            param.setCurrentPage(i + 1);
            KanbanPptPage page = pages.get(i);
            try {
                if (StringUtils.equalsIgnoreCase(page.getPagedTableInPpt(), "true")) {
                    drawPagedTable(param, ppt, painter, page);
                } else {
                    drawOneContentPage(param, ppt, painter, template, page);
                }
            } catch (Exception e) {
                // 当前页面出错，不影响其他页面绘制，同时记录日志
                log.error("{}", e.getMessage(), e);
                param.logDetail(page, e); // 记录错误日志，前台展示
            }
        }
    }

    private void barCompareColorConfig(KanbanPptParam param, List<KanbanPptPage> pages) {
        KanbanPpt ppt = param.getPpt();
        if (StringUtils.isNotBlank(ppt.getBarCompareColorConfig())) {
            List<String[]> barCompareColorList = Arrays.stream(ppt.getBarCompareColorConfig().split(";"))
                    .map(x -> x.split("\\|\\|\\|"))
                    .collect(Collectors.toList());
            pages.forEach(page -> page.setBarCompareColorList(barCompareColorList));
        }
    }

    private void drawOneContentPage(KanbanPptParam param, Presentation ppt, ComponentV6Painter painter,
                                    Template template, KanbanPptPage page) {
        log.info("START PAGE --------------------------------------------------------------------");
        log.info("[{}] Kanban PPT 正在处理Slide页面[{}/{}], 页面：{}, 页面No: {}"
                , param.getUser(), param.getCurrentPage(), param.getTotalPages()
                , page.getName(), page.getPageNo());
        long start = System.currentTimeMillis();

        // 取数，默认取快照
        DynamicLayout dynamicLayout = dynamicLayout(param, page);
        // 计算布局
        this.calculateLayout(ppt, dynamicLayout);
        // 创建新页面
        final ISlide slide = page.belongToSection()
                ? SlidesEngine.createBlankSlideWithSection(ppt, param.getSectionMap().get(page.getSection()))
                : SlidesEngine.createBlankSlide(ppt);
        // final ISlide slide = SlidesEngine.createBlankSlide(ppt, section);
        // 绘制标题
        this.drawTitle(param, slide, painter, dynamicLayout, page);
        // 绘制内容
        this.drawLayout(param, slide, painter, dynamicLayout, template, page);

        log.info("[{}] Kanban PPT Slide页面[{}/{}]绘制完成，用时：{}"
                , param.getUser(), param.getCurrentPage(), param.getTotalPages()
                , DateUtil.calPassedTime(start));
        log.info("FINISH PAGE --------------------------------------------------------------------");
    }

    private void drawPagedTable(KanbanPptParam param, Presentation ppt, ComponentV6Painter painter, KanbanPptPage page) {
        log.info("START PAGED TABLE --------------------------------------------------------------------");
        log.info("[{}] Kanban PPT 正在处理Slide页面[{}/{}], 页面：{}, 页面No: {}"
                , param.getUser(), param.getCurrentPage(), param.getTotalPages()
                , page.getName(), page.getPageNo());
        long start = System.currentTimeMillis();

        // 取数，默认取快照
        DynamicLayout dynamicLayout = dynamicLayout(param, page);
        // 计算布局
        this.calculateLayout(ppt, dynamicLayout);

        DynamicRow row = dynamicLayout.getRows().get(0);
        if (!row.hasColumns())
            return;
        DynamicColumn column = row.getColumns().get(0);
        TableBlock tableBlock = column.getTable();
        if (tableBlock == null)
            return;
//        tableBlock.setLayout(column.getLayoutV5());
        tableBlock.setLayoutV6(column.getLayoutV6());

        List<DataPoint> rowNames = tableBlock.getRowNames();
        AbstractDataPoint[][] dataPointArray = tableBlock.getDataPointArray();
        if (rowNames == null || dataPointArray == null)
            return;

        int rowSize = dataPointArray.length;
        int pageSize = 24;
        double pages = Math.ceil(1D * rowSize / pageSize);
        for (int i = 0; i < pages; i++) {
            int fromIndex = i * pageSize;
            int toIndex = (i + 1) * pageSize;
            if (toIndex > rowNames.size()) {
                toIndex = rowNames.size();
            }
            // 创建新页面
            final ISlide slide = page.belongToSection()
                    ? SlidesEngine.createBlankSlideWithSection(ppt, param.getSectionMap().get(page.getSection()))
                    : SlidesEngine.createBlankSlide(ppt);
            // 绘制标题
            this.drawTitle(param, slide, painter, dynamicLayout, page);
            // 绘制内容
            List<DataPoint> pageRowNames = rowNames.subList(fromIndex, toIndex);
            AbstractDataPoint[][] pageDataPointArray = Arrays.copyOfRange(dataPointArray, fromIndex, toIndex);
            TableBlock pageTableBlock = tableBlock.clone();
            pageTableBlock.setRowNames(pageRowNames);
            pageTableBlock.setDataPointArray(pageDataPointArray);
            painter.drawPagedTable(param, slide, pageTableBlock);
        }

        log.info("[{}] Kanban PPT Slide页面[{}/{}]绘制完成，用时：{}"
                , param.getUser(), param.getCurrentPage(), param.getTotalPages()
                , DateUtil.calPassedTime(start));
        log.info("FINISH PAGED TABLE --------------------------------------------------------------------");
    }

    private DynamicLayout dynamicLayout(KanbanPptParam param, KanbanPptPage page) {
        // Snapshot改造后，快照中没有可视化
        // DynamicLayout dynamicLayout = param.getFramework().dynamicLayoutFromSnapshot(param, page);
//        DynamicLayout dynamicLayout = param.getFramework().dynamicLayoutFromVisual(param, page);
        DynamicLayout dynamicLayout = page.getLayout();
        if (null == dynamicLayout) {
            String message = String.format("没有找到页面指定的可视化信息，Kanban PPT：[%s]，页面：[%d - %s]"
                    , param.getTask().getPptName(), page.getPageNo(), page.getName());
            log.error(message);
            throw new IllegalStateException(message);
        }
        return dynamicLayout;
    }

    /**
     * 计算布局，行列转换为PPT中的position，position信息保留在DynamicLayout中
     */
    private void calculateLayout(Presentation ppt, DynamicLayout dynamicLayout) {
        // PPT页面总体的尺寸
        ISlideSize slideSize = ppt.getSlideSize();
        LayoutV6Calculator.calculate(slideSize, dynamicLayout); // 6.0 PPT
        // 为前端截图计算布局
        LayoutV6ForSnapshotCalculator.calculate(dynamicLayout); // 6.0 PPT TODO
    }

    /**
     * 绘制页面标题
     */
    private void drawTitle(KanbanPptParam param, ISlide slide, ComponentV6Painter painter,
                           DynamicLayout dynamicLayout, KanbanPptPage page) {
        // 一个PPT模板中模板中的每个页面title是固定的，当title含有切分单元的名称时，不能适用PPT模板分裂出的每个切分单元PPT文件
        // 这里需要通过PPT页面找到对应的可视化，提取可视化中的OfficialName，title中的切分单元名称才准确
//        Optional<Map<String, String>> visualItem = param.getFramework().getViewpointMappingService()
//                .findVisual(param.framework.getViewpointMappingRepository(), param.getPpt().getMapId(),
//                        param.getPpt().getShotId(), param.getSplitUnitId(), page.getNodeId(), page.getVisualId());
//        if (visualItem.isPresent()) {
//            Map<String, String> visual = visualItem.get();
//            page.setOfficialName(visual.get("officialName"));
//        }

        painter.drawPageTitle(param, slide, dynamicLayout, page);
    }

    /**
     * 绘制页面内容
     */
    private void drawLayout(KanbanPptParam param, ISlide slide, ComponentV6Painter painter,
                            DynamicLayout dynamicLayout, Template template, KanbanPptPage page) {
        log.info("[{}] Kanban PPT drawLayout，可视化ID：{}", param.getUser(), dynamicLayout.getMatrixVisualizationStencilId());
        dynamicLayout.getRows().forEach(row -> drawRow(param, slide, painter, row, template, page));
    }

    private void drawRow(KanbanPptParam param, ISlide slide, ComponentV6Painter painter,
                         DynamicRow row, Template template, KanbanPptPage page) {
        if (!row.hasColumns()) return;
        row.getColumns().forEach(column -> drawColumn(param, slide, painter, column, template, page));
    }

    private void drawColumn(KanbanPptParam param, ISlide slide, ComponentV6Painter painter,
                            DynamicColumn column, Template template, KanbanPptPage page) {
//        Position position = column.getLayoutV6().getPosition();
//        Size size = column.getLayoutV6().getSize();
//        slide.getShapes().addAutoShape(ShapeType.Rectangle, (int) position.getX(), (int) position.getY(), size.getWidth(), size.getHeight());

        // 绘制组件
        drawComponent(param, slide, painter, column, template, page);

        if (!column.hasRows()) return;
        column.getRows().forEach(row -> drawRow(param, slide, painter, row, template, page));
    }

    private void drawComponent(KanbanPptParam param, ISlide slide, ComponentV6Painter painter,
                               DynamicColumn column, Template template, KanbanPptPage page) {
        if (!column.isShowInPpt()) return;
        // painter.drawColumn(slide, column);
        painter.drawChart(param, slide, column, template, page);
        painter.drawTable(param, slide, column, template, page);
        // painter.drawThesis(param, slide, column, template, page);
        painter.drawAssert(param, slide, column, template, page);
        painter.drawMedia(param, slide, column, template, page);
        painter.drawText(slide, column);
    }

    private void save(KanbanPptParam param, Presentation ppt) {
        SlidesEngine.writeSlides(ppt, param.getOutputPathFile());
    }

    private void clear(Presentation ppt) {
        ISlide[] slides = ppt.getSlides().toArray();
        for (ISlide slide : slides) {
            if (null != slide.getShapes()) {
                IShape[] arrays = slide.getShapes().toArray();
                for (IShape shape : arrays) {
                    slide.getShapes().remove(shape);
                    // shape = null;
                }
            }
            slide.remove();
        }
    }

    private void log(KanbanPptParam param) {
        List<KanbanPptLog> logList = param.getPptLogList();
        if (null == logList || logList.isEmpty()) return;

        param.getFramework().getPptLogRepository().saveAll(logList);
        log.info("保存PPT生成过程错误信息");
    }

    private void dispose(Presentation presentation, Template template) {
        try {
            if (presentation != null) {
                presentation.dispose();
                //presentation = null;
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        try {
            if (null != template) {
                template.clear();
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        }
        log.info("销毁Presentation完成");
    }

}
