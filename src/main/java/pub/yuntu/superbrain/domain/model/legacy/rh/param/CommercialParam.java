package pub.yuntu.superbrain.domain.model.legacy.rh.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.constants.YtConstants;
import pub.yuntu.foundation.json.JsonMapper;
import pub.yuntu.superbrain.domain.mapstruct.StandardParamMapper;
import pub.yuntu.superbrain.domain.model.brain.BrainInstance;
import pub.yuntu.superbrain.domain.model.brain.command.CommandParam;
import pub.yuntu.superbrain.domain.model.brain.param.input.AbstractInput;
import pub.yuntu.superbrain.domain.model.creativity.block.AbstractBlock;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPointBuilder;
import pub.yuntu.superbrain.domain.model.creativity.layout.DynamicLayout;
import pub.yuntu.superbrain.domain.model.creativity.param.DecimalParam;
import pub.yuntu.superbrain.domain.model.creativity.param.PeriodParam;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.AbstractSegment;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.BrainDataSegment;
import pub.yuntu.superbrain.domain.model.creativity.parser.segment.brainPointProducer.*;
import pub.yuntu.superbrain.domain.model.identity.*;
import pub.yuntu.superbrain.domain.model.kanban.KanbanModule;
import pub.yuntu.superbrain.domain.model.kanban.KanbanParam;
import pub.yuntu.superbrain.domain.model.kanban.config.BrainAdditionalConfig;
import pub.yuntu.superbrain.domain.model.legacy.customer.Customer;
import pub.yuntu.superbrain.domain.model.legacy.exam.MLDataSetExam;
import pub.yuntu.superbrain.domain.model.legacy.exam.SatisfactionSurvyExam;
import pub.yuntu.superbrain.domain.model.legacy.indexGrid.IndexGrid;
import pub.yuntu.superbrain.domain.model.legacy.kg.GraphInteraction;
import pub.yuntu.superbrain.domain.model.legacy.net.data.DataChip;
import pub.yuntu.superbrain.domain.model.legacy.net.grouping.*;
import pub.yuntu.superbrain.domain.model.legacy.net.interaction.NetInteraction;
import pub.yuntu.superbrain.domain.model.legacy.org.OrgCategory;
import pub.yuntu.superbrain.domain.model.legacy.planar.data.LegacyDataReader;
import pub.yuntu.superbrain.domain.model.legacy.planar.data.sort.SortPositionInfo;
import pub.yuntu.superbrain.domain.model.legacy.planar.index.IndexTeam;
import pub.yuntu.superbrain.domain.model.legacy.rh.commerce.CommercialMatrixInstance;
import pub.yuntu.superbrain.domain.model.legacy.rh.mathmatrix.MathMatrix;
import pub.yuntu.superbrain.domain.model.legacy.rh.matrix.DataMatrixInstance;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.AbstractViewPoint;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.LegacyOrgViewPoint;
import pub.yuntu.superbrain.domain.model.legacy.rh.viewpoint.ViewPointDataMatrixInstance;
import pub.yuntu.superbrain.domain.model.legacy.split.*;
import pub.yuntu.superbrain.domain.model.legacy.standard.MsdDataCapturePeriod;
import pub.yuntu.superbrain.domain.model.legacy.standard.StandardParam;
import pub.yuntu.superbrain.domain.model.legacy.stream.MsdType;
import pub.yuntu.superbrain.domain.model.legacy.stream.datasource.DataFinder;
import pub.yuntu.superbrain.domain.model.legacy.stream.group.StreamGroup;
import pub.yuntu.superbrain.domain.model.legacy.stream.stat.LianJiaHouseStat;
import pub.yuntu.superbrain.domain.model.legacy.stream.stat.SameBatchStat;
import pub.yuntu.superbrain.domain.model.legacy.stream.task.feature.FeatureSplitUnit;
import pub.yuntu.superbrain.domain.model.legacy.superbrain.data.signal.*;
import pub.yuntu.superbrain.domain.model.neuron.BaseNeuron;
import pub.yuntu.superbrain.domain.model.standard.StandardAttributeEnum;
import pub.yuntu.superbrain.infrastructure.util.ExceptionUtil;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Types;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static pub.yuntu.superbrain.domain.model.legacy.split.MockUnitEnum.INDUSTRY_AVERAGE;
import static pub.yuntu.superbrain.domain.model.legacy.split.MockUnitEnum.INDUSTRY_BENCHMARK;

/**
 * Created by dongdong on 2022/3/2 22:50.
 */
@Data
@Slf4j
public class CommercialParam {
    // 统一的spring能力获取入口
    @JsonIgnore
    CommandParam commandParam;
    //    RightHemisphereFramework framework;
//    CommercialMap map;
//    CommercialMapShot shot;
    String user;
    String commercialMapId;
    String commercialShotId;
    // json参数
    String customerId;
    String standardParamId;
    String steps;
    String targetVisuals;
    PeriodParam periods;
    List<StreamGroup> groupings; // 所有待选 group 列表
    int totalViewPoints = 0;
    List<ViewPointParam> viewpoints;
    List<AbstractInput> dafeStreams;
    boolean mainProcessFlag;
    boolean mathMatrixFlag;
    List<DecimalParam> decimalParams;
    KanbanModule kanbanModule;

    // 运行过程参数
    Customer customer;
    String transformCustomerName;
    StandardParam standardParam;
    List<StandardParam> standardParams;
    OrgCategory category;
    CommercialMatrixInstance commercialMatrixInstance;
    HashMap<String, DataMatrixInstance> matrixInstanceMap = new HashMap<>();
    HashMap<Long, String> indexLongAndStrIdMap = new HashMap<>();
    HashMap<String, IndexGrid> indexCodeGridMap = new HashMap<>();
    HashMap<String, IndexGrid> indexIdGridMap = new HashMap<>();
    HashMap<String, String> msdExamIndexIdAndStandardIdMap = new HashMap<>();
    HashMap<String, String> msdExamUniteIndexIdAndStandardIdMap = new HashMap<>();
    HashMap<String, NetInteraction> idAndNetInteractionMap = new HashMap<>();
    HashMap<String, GraphInteraction> planarCodeAndGraphInteractionMap = new HashMap<>();
    HashMap<String, SplitUnit> fullSplitMap = new HashMap<>();
    HashMap<String, SplitUnit> fullSplitStrIdMap = new HashMap<>();
    HashMap<String, AllianceSplitUnit> fullAllianceSplitMap = new HashMap<>();
    DualHashBidiMap<String, Long> fullSplitMapping = new DualHashBidiMap<>(); // key=组织结构切分id+属性，value=混合切分id
    DualHashBidiMap<String, Long> fullAllianceSplitMapping = new DualHashBidiMap<>(); // key=组织结构切分id+属性，value=混合切分id
    MathMatrix mathMatrix;
    HashMap<String, List<String>> expAndPostProcessMap = new HashMap<>();
    List<SplitMapping> splitMappingOfCustomer = new ArrayList<>();
    List<SplitGroup> splitGroupOfCustomer = new ArrayList<>();
    Map<String, TextParseResult> textParseCache = new HashMap<>();
    Map<Long, MLDataSetExam> examDbIdAndMLDataSetExam = new HashMap<>();
    List<MsdDataCapturePeriod> msdDataCapturePeriods = new ArrayList<>();
    @JsonIgnore
    Map<String, List<HashMap<String, Object>>> sqlAndDataListCache = new HashMap<>();
    // brainName -> networkType -> splitUnitDbId -> dataPeriod -> indexCode -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>>> pointNetworkMap_0 = new ConcurrentHashMap<>();
    // networkType -> splitUnitDbId -> dataPeriod -> indexCode -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> pointNetworkMap_1 = new ConcurrentHashMap<>();
    // networkType -> splitUnitDbId -> dataPeriod -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> pointNetworkMap_2 = new ConcurrentHashMap<>();
    // networkType -> splitUnitDbId -> dataPeriod -> planarCode -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> pointNetworkMap_3 = new ConcurrentHashMap<>();
    // networkType -> List<AbstractDataPoint>
    ConcurrentHashMap<String, List<AbstractDataPoint>> pointNetworkMap_4 = new ConcurrentHashMap<>();
    // networkType -> dataPeriod -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> pointNetworkMap_5 = new ConcurrentHashMap<>();
    // networkType -> knowledgePointType -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> pointNetworkMap_6 = new ConcurrentHashMap<>();
    // networkType -> splitName -> dataPeriod -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> pointNetworkMap_7 = new ConcurrentHashMap<>();
    // networkType -> splitGroupId -> dataPeriod -> indexCode -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> pointNetworkMap_8 = new ConcurrentHashMap<>();
    // networkType -> louPanType -> splitName -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> pointNetworkMap_9 = new ConcurrentHashMap<>();
    // networkType -> analysisType -> dataPeriod -> splitName -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> pointNetworkMap_10 = new ConcurrentHashMap<>();
    // networkType -> splitName -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> pointNetworkMap_11 = new ConcurrentHashMap<>();
    // networkType -> analysisType -> dataPeriod -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> pointNetworkMap_12 = new ConcurrentHashMap<>();
    // networkType -> analysisType -> splitUnitDbId -> dataPeriod -> indexCode -> List<AbstractDataPoint>
    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>>> pointNetworkMap_13 = new ConcurrentHashMap<>();
    // splitUnitDbId -> fromCode -> toCode -> List<CommercialMatrixLink>
//    ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<CommercialMatrixLink>>>> relationMatrixLinkMap = new ConcurrentHashMap<>();
    // global sort map
    HashMap<String, SplitUnit> globalMap = new HashMap<>();
    HashMap<String, Long> globalIdMap = new HashMap<>();
    // global alliance sort map
    HashMap<String, AllianceSplitUnit> globalAllianceMap = new HashMap<>();
    HashMap<String, Long> globalAllianceIdMap = new HashMap<>();
    HashMap<String, String> categoryIdAndCustomerNameMap = new HashMap<>();
    HashMap<String, Set<String>> productLabelAndProjectNamesMap = new HashMap<>();
    HashMap<String, String> louPanCodeAndProjectNameMap = new HashMap<>();
    // 工具类
    @JsonIgnore
    JsonMapper mapper = JsonMapper.nonEmptyMapper();
    @JsonIgnore
    Runtime run = Runtime.getRuntime();
    @JsonIgnore
    HashSet<String> fullIndexCodeSet = new HashSet<>(); // 本商业地图用到的所有指标
    @JsonIgnore
    DataCar dataCar = new DataCar();
    @JsonIgnore
    HouseBrainPointProducer houseBrainPointProducer = new HouseBrainPointProducer();
    @JsonIgnore
    VoiceBrainPointProducer voiceBrainPointProducer = new VoiceBrainPointProducer();
    @JsonIgnore
    SaleScaleBrainPointProducer saleScaleBrainPointProducer = new SaleScaleBrainPointProducer();
    @JsonIgnore
    GlobalVoiceBrainPointProducer globalVoiceBrainPointProducer = new GlobalVoiceBrainPointProducer();
    @JsonIgnore
    GlobalBrainPointProducer globalBrainPointProducer = new GlobalBrainPointProducer();
    @JsonIgnore
    SameBatchBrainPointProducer sameBatchBrainPointProducer = new SameBatchBrainPointProducer();
    @JsonIgnore
    KnowledgePointBrainPointProducer knowledgePointBrainPointProducer = new KnowledgePointBrainPointProducer();
    @JsonIgnore
    SalesTypeStatBrainPointProducer salesTypeStatBrainPointProducer = new SalesTypeStatBrainPointProducer();
    @JsonIgnore
    LianJiaHouseLayoutBrainPointProducer lianJiaHouseLayoutBrainPointProducer = new LianJiaHouseLayoutBrainPointProducer();
    @JsonIgnore
    SurroundingsBrainPointProducer surroundingsBrainPointProducer = new SurroundingsBrainPointProducer();
    @JsonIgnore
    LianJiaAlbumBrainPointProducer lianJiaAlbumBrainPointProducer = new LianJiaAlbumBrainPointProducer();
    @JsonIgnore
    PostCalculateBrainPointProducer postCalculateBrainPointProducer = new PostCalculateBrainPointProducer();
    @JsonIgnore
    LabelBrainPointProducer labelBrainPointProducer = new LabelBrainPointProducer();
    @JsonIgnore
    ConclusionBrainPointProducer conclusionBrainPointProducer = new ConclusionBrainPointProducer();
    @JsonIgnore
    CustomStatBrainPointProducer customStatBrainPointProducer = new CustomStatBrainPointProducer();
    @JsonIgnore
    List<String> targetVisualList;

    public void postInit(/*CommercialMap map,
                         CommercialMapShot shot*/KanbanParam kanbanParam) {
//        this.map = map;
//        this.shot = shot;
//        this.commercialMapId = map.getId();
//        this.commercialShotId = this.shotId(shot);
        if (StringUtils.isBlank(steps)) {
            mainProcessFlag = true;
            mathMatrixFlag = true;
        } else {
//            List<String> flagList = Arrays.asList(steps.split(","));
//            if (flagList.contains("main")) {
//                mainProcessFlag = true;
//            } else {
//                mainProcessFlag = false;
//            }
//            if (flagList.contains("math")) {
//                mathMatrixFlag = true;
//            } else {
//                mathMatrixFlag = false;
//            }
        }

        List<BrainAdditionalConfig> brainAdditionalConfigs = kanbanParam.getBrainAdditionalConfigs();

        groupings = Lists.newArrayList();
        viewpoints = Lists.newArrayList();
        List<String> groupNames = Lists.newArrayList();

        for (String brainName : kanbanParam.getBrainNames()) {
            BrainInstance brain = kanbanParam.getBrain(brainName);
            BrainAdditionalConfig brainAdditionalConfig = null;
            if (null != brainAdditionalConfigs && !brainAdditionalConfigs.isEmpty()) {
                brainAdditionalConfig = brainAdditionalConfigs.stream()
                        .filter(additionalConfig -> StringUtils.equalsIgnoreCase(additionalConfig.getBrainName(), brainName))
                        .findFirst().orElse(null);
            }

            List<BaseNeuron> neuronList = brain.getNeuronNet().getNeuronList();
            for (BaseNeuron baseNeuron : neuronList) {
                initGroupForKanban(groupNames, baseNeuron, brainAdditionalConfig);
                // 初始化FullSplitMap
                registerUnitsForKanban(baseNeuron);
            }
        }
        groupNames.clear();

        for (ViewPointParam viewPointParam : viewpoints) {
//            viewPointParam.setFramework(framework);
            viewPointParam.setKanbanParam(kanbanParam);
            viewPointParam.setViewPoints(new ArrayList<>());
        }

        this.initRequired(kanbanParam);

        // init all stream group
        for (StreamGroup group : groupings) {
            group.init(kanbanParam, StandardParamMapper.INSTANCE.toDto(this.getStandardParam()));
            if (group.getGrouping() instanceof LegacyOrgGrouping) {
                fullSplitMapping.putAll(group.getGrouping().getSplitMapping());
            } else if (group.getGrouping() instanceof LegacyIndustryGrouping) {
                fullAllianceSplitMapping.putAll(group.getGrouping().getSplitMapping());
            }
        }

        CommandParam commandParam = kanbanParam.getCommandParam();
        // 提前准备好所有的数据矩阵实例，TODO 并计算好实例之间的父子关系
//        initDataMatrix();
        initIndex(commandParam);
        initIndexGrid(commandParam);
        initNetInteraction(commandParam);
        initGraphInteraction(commandParam);
        initMsdDataCapturePeriod(commandParam);
    }

    public void initGroupForBrainsNotInKanban(List<String> brainNames, KanbanParam kanbanParam) {
        List<String> groupNames = getGroupings().stream().map(StreamGroup::getName).collect(Collectors.toList());
        List<String> groupNameBackups = Lists.newArrayList();
        groupNameBackups.addAll(groupNames);
        for (String brainName : brainNames) {
            BrainInstance brain = commandParam.getBrainCenter().getInstance(brainName);
            if (null == brain) {
                ExceptionUtil.throwIllegalStateException("大脑没有构建：" + brainName);
                continue;
            }
            List<BaseNeuron> neuronList = brain.getNeuronNet().getNeuronList();
            for (BaseNeuron baseNeuron : neuronList) {
                initGroupForKanban(groupNames, baseNeuron, null);
                // 初始化FullSplitMap
                registerUnitsForKanban(baseNeuron);
            }
        }

        groupNames.forEach(groupName -> {
            if (groupNameBackups.contains(groupName)) return;

            List<StreamGroup> collect = groupings.stream().filter(group -> StringUtils.equals(group.getName(), groupName)).collect(Collectors.toList());
            for (StreamGroup group : collect) {
                group.init(kanbanParam, StandardParamMapper.INSTANCE.toDto(this.getStandardParam()));
                if (group.getGrouping() instanceof LegacyOrgGrouping) {
                    fullSplitMapping.putAll(group.getGrouping().getSplitMapping());
                } else if (group.getGrouping() instanceof LegacyIndustryGrouping) {
                    fullAllianceSplitMapping.putAll(group.getGrouping().getSplitMapping());
                }
            }
        });

        groupNames.clear();
    }

    private void registerUnitsForKanban(BaseNeuron baseNeuron) {
        List<BaseIdentity> identities = baseNeuron.getIdentityGroup().toList();
        identities.forEach(baseIdentity -> {
            if (baseIdentity instanceof LegacyOrgIdentity) {
                LegacyOrgIdentity orgIdentity = (LegacyOrgIdentity) baseIdentity;
                this.registerSplitUnit(orgIdentity);
            }
            if (baseIdentity instanceof IndustryIdentity) {
                IndustryIdentity industryIdentity = (IndustryIdentity) baseIdentity;
                this.registerSplitUnit(industryIdentity);
            }
            if (baseIdentity instanceof AllianceUnitIdentity) {
                AllianceUnitIdentity allianceUnitIdentity = (AllianceUnitIdentity) baseIdentity;

                Map<String, AllianceSplitUnit> attributeAndUnitMap = allianceUnitIdentity.getAttributeAndUnitMap();
                AllianceSplitUnit pureUnit = attributeAndUnitMap.get("总体"); // 找到总体不带属性的切分
                Collection<AllianceSplitUnit> values = attributeAndUnitMap.values();
                values.forEach(unit -> {
                    String key = "" + unit.get_id();
                    fullAllianceSplitMap.putIfAbsent(key, pureUnit);
                });
                // List<AllianceSplitUnit> list = Lists.newArrayList();
                // list.addAll(values);
                // this.registerAllianceSplitUnit(list);
            }
        });
    }

    private void initGroupForKanban(List<String> groupNames, BaseNeuron baseNeuron, BrainAdditionalConfig brainAdditionalConfig) {
        String groupName = baseNeuron.getPrimaryGroupName();
        if (StringUtils.isBlank(groupName)) return;

        if (groupNames.contains(groupName)) return;
        groupNames.add(groupName);

        StreamGroup streamGroup = new StreamGroup();
        streamGroup.setName(groupName);
        RelatedIdentityGroup identityGroup = baseNeuron.getIdentityGroup();
        CommandParam commandParam = this.getCommandParam();
        if (null != identityGroup.getOrgIdentity()) {
            streamGroup.setType("legacyOrg");
            String groupID = identityGroup.getOrgIdentity().getUnit().getGroupID();
            streamGroup.setId(groupID);
            LegacyOrgGrouping orgGrouping = new LegacyOrgGrouping();
            orgGrouping.setId(groupID);
            SplitGroup splitGroup = commandParam.getSplitGroupRepository().findById(groupID)
                    .orElse(null);
            orgGrouping.setSplitGroup(splitGroup);
            streamGroup.setGrouping(orgGrouping);

            List<SplitUnit> units = commandParam.getSplitUnitRepository().findByGroupID(groupID);
            orgGrouping.setLegacyUnitList(units);
        }
        if (null != identityGroup.getAllianceUnitIdentity()) {
            streamGroup.setType("legacyIndustry");
            String groupID = identityGroup.getAllianceUnitIdentity().getUnit().getGroupID();
            streamGroup.setId(groupID);
            LegacyIndustryGrouping industryGrouping = new LegacyIndustryGrouping();
            AllianceSplitGroup allianceSplitGroup = commandParam.getAllianceSplitGroupRepository().findById(groupID).orElse(null);
            industryGrouping.setAllianceSplitGroup(allianceSplitGroup);
            industryGrouping.setId(groupID);
            streamGroup.setGrouping(industryGrouping);

            List<AllianceSplitUnit> units = commandParam.getAllianceSplitUnitRepository().findByGroupID(groupID);
            industryGrouping.setLegacyAllianceUnitList(units);
        }

        if (null != brainAdditionalConfig) {
            List<StreamGroup> groupingsInAdditionalConfig = brainAdditionalConfig.getGroupings();
            if (null != groupingsInAdditionalConfig) {
                groupingsInAdditionalConfig.stream()
                        .filter(grouping -> StringUtils.equalsIgnoreCase(grouping.getName(), groupName))
                        .findFirst()
                        .ifPresent(additionalGroup -> {
                            AbstractGrouping additionalGrouping = additionalGroup.getGrouping();
                            List<GlobalSort> sorts = additionalGrouping.getSorts();
                            streamGroup.getGrouping().setSorts(sorts);

                        });
            }
        }

        groupings.add(streamGroup);

//                ViewPointParam viewPointParam = new ViewPointParam();
//                viewPointParam.setName(groupName);
//                viewPointParam.setGroupingName(groupName);
////                viewPointParam.setScope("ALL"); // 只赋值，不产生实际作用
////                viewPointParam.setCommercialMatrixName("住宅满意度标准版"); // 只赋值，不产生实际作用
//                viewpoints.add(viewPointParam);
    }

    public void postInitForKanban(KanbanParam kanbanParam) {
        // 初始化FullSplitMap
        List<BaseNeuron> neuronList = kanbanParam.getDefaultBrain().getNeuronNet().getNeuronList();
        neuronList.forEach(baseNeuron -> {
            registerUnitsForKanban(baseNeuron);
        });
        // 初始化IndexCodeGridMap
        this.initIndexGrid(kanbanParam.getCommandParam());
        this.initIndex(kanbanParam.getCommandParam());
        // TODO: INIT IdAndNetInteractionMap
        // TODO: INIT PlanarCodeAndGraphInteractionMap
        // TODO: INIT IndexCodeGridMap
        // TODO: INIT ProductLabelAndProjectNamesMap
        // TODO: INIT CategoryIdAndCustomerNameMap
    }

    public void globalActionsPrepare() {
        if (!isMainProcessFlag())
            return;
        for (StreamGroup group : groupings) {
            if (group.getGrouping().getSorts() != null) {
                if (group.getGrouping() instanceof LegacyOrgGrouping) {
                    LegacyOrgGrouping orgGrouping = (LegacyOrgGrouping) group.getGrouping();
                    for (GlobalSort globalSort : group.getGrouping().getSorts()) {
                        for (SplitUnit unit : orgGrouping.getLegacyUnitList()) {
                            unit.setGlobalSortSampleLimit(globalSort.getSampleLimit());
                            globalSort.addSplit(unit);
                            globalMap.put("" + unit.get_id(), unit);
                            globalIdMap.put(unit.getId(), unit.get_id());
                            unit.getGlobalSortCar().addSort(globalSort);
                        }
                    }
                } else if (group.getGrouping() instanceof LegacyIndustryGrouping) {
                    LegacyIndustryGrouping industryGrouping = (LegacyIndustryGrouping) group.getGrouping();
                    for (GlobalSort globalSort : group.getGrouping().getSorts()) {
                        for (AllianceSplitUnit unit : industryGrouping.getLegacyAllianceUnitList()) {
                            globalSort.addAllianceSplit(unit);
                            globalAllianceMap.put("" + unit.get_id(), unit);
                            globalAllianceIdMap.put(unit.getId(), unit.get_id());
                            unit.getGlobalSortCar().addSort(globalSort);
                        }
                    }
                }
            }
        }
    }

    public SortPositionInfo globalPosition(SortPositionInfo info) {
        SplitUnit splitUnit = globalMap.get("" + info.getSplitUnitDbId());
        if (splitUnit == null)
            throw new IllegalStateException("can't find split unit {" + info.getSplitUnitDbId() + "} in sort grouping");
        info = splitUnit.getGlobalSortCar().gettingSort(this, info);
        return info;
    }

    public SortPositionInfo globalAlliancePosition(SortPositionInfo info) {
        AllianceSplitUnit splitUnit = globalAllianceMap.get("" + info.getSplitUnitDbId());
        if (splitUnit == null)
            throw new IllegalStateException("can't find alliance split unit {" + info.getSplitUnitDbId() + "} in sort grouping");
        info = splitUnit.getGlobalSortCar().gettingSort(this, info);
        return info;
    }

    public void initFullIndexCodeSet() {
        for (ViewPointParam viewPointParam : viewpoints) {
            for (AbstractViewPoint point : viewPointParam.getViewPoints()) {
                if (point instanceof LegacyOrgViewPoint) {
                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) point;
                    for (ViewPointDataMatrixInstance viewPointDataMatrixInstance : orgViewPoint.getViewPointDataMatrixInstanceMap().values()) {
                        for (String index : viewPointDataMatrixInstance.getIndices()) {
                            this.fullIndexCodeSet.add(index);
                        }
                    }
                }
            }
        }
    }

//    public void processOneSignal(Signal signal) {
//        // todo 未来根据信号的不同类型和来源，流过不同的切分
//        // todo 增加模拟行业标杆，行业百分位等切分，存储对应的特征或者分析结果
//        // todo 标尺等对应数据的处理
//        // 确保流过每个数据切分
//        if (signal instanceof AnaSignal) {
//            processAnaSignal(signal);
//        } else if (signal instanceof IndustryAnaSignal) {
//            processIndustryAnaSignal(signal);
//        } else if (signal instanceof TextSignal) {
//            processTextSignal(signal);
//        } else if (signal instanceof IndustryTextSignal) {
//            processIndustryTextSignal(signal);
//        } else if (signal instanceof IndustryMarkSignal) {
//            processIndustryMarkSignal(signal);
//        } else if (signal instanceof StatSignal || signal instanceof GlobalSignal) {
//            processStatSignal(signal);
//        } else if (signal instanceof ZataSignal) {
//            processZataSignal(signal);
//        } else if (signal instanceof GlobalVoiceSignal) {
//            processGlobalVoiceSignal(signal);
//        } else if (signal instanceof SaleScaleSignal || signal instanceof KnowledgePointSignal) {
//            processSaleScaleSignal(signal);
//        }
//    }

    public void _processOneSignal(Signal signal, int pageNo) {
        if (signal instanceof AnaSignal) {
            _processAnaSignal(signal, pageNo);
        } else if (signal instanceof IndustryAnaSignal) {
            _processIndustryAnaSignal(signal);
        } else if (signal instanceof TextSignal) {
            _processTextSignal(signal);
        } else if (signal instanceof IndustryTextSignal) {
            _processIndustryTextSignal(signal);
        } else if (signal instanceof IndustryMarkSignal) {
            _processIndustryMarkSignal(signal);
        } else if (signal instanceof NameListSignal) {
            _processNameListSignal(signal);
        } else if (signal instanceof HouseSignal) {
            _processHouseSignal(signal);
        } else if (signal instanceof SameBatchSignal) {
            _processSameBatchSignal(signal);
        } else if (signal instanceof SurroundingsSignal) {
            _processSurroundingsSignal(signal);
        } else if (signal instanceof GlobalSignal) {
            _processGlobalSignal(signal);
        } else if (signal instanceof IndustryZataSignal) {
            _processIndustryZataSignal(signal);
        } else if (signal instanceof UserSignal) {
            _processUserSignal(signal);
        } else if (signal instanceof ZataSignal) {
            _processZataSignal(signal);
        } else if (signal instanceof GlobalVoiceSignal) {
            _processGlobalVoiceSignal(signal);
        } else if (signal instanceof SaleScaleSignal) {
            _processSaleScaleSignal(signal);
        } else if (signal instanceof KnowledgePointSignal) {
            _processKnowledgePointSignal(signal);
        } else if (signal instanceof IndustrySaleScaleSignal) {
            _processIndustrySaleScaleSignal(signal);
        } else if (signal instanceof HighNetWorthClientSignal) {
            _processHighNetWorthClientSignal(signal);
        } else if (signal instanceof LianjiaLoupanSignal) {
            _processLianjiaLoupanSignal(signal);
        } else if (signal instanceof LianJiaHouseLayoutSignal) {
            _processLianJiaHouseLayoutSignal(signal);
        } else if (signal instanceof LianJiaAlbumSignal) {
            _processLianJiaAlbumSignal(signal);
        } else if (signal instanceof CityHousePriceSignal) {
            _processCityHousePriceSignal(signal);
        } else if (signal instanceof CityPrimaryAndSecondaryMarketsSignal) {
            _processCityPrimaryAndSecondaryMarketsSignal(signal);
        } else if (signal instanceof DistrictPrimaryAndSecondaryMarketsSignal) {
            _processDistrictPrimaryAndSecondaryMarketsSignal(signal);
        } else if (signal instanceof SalesTypeStatSignal) {
            _processSalesTypeStatSignal(signal);
        } else if (signal instanceof IndustryProjectSignal) {
            _processIndustryProjectSignal(signal);
        } else if (signal instanceof LabelSignal) {
            _processLabelSignal(signal);
        } else if (signal instanceof ConclusionSignal) {
            _processConclusionSignal(signal);
        } else if (signal instanceof CustomStatSignal) {
            _processCustomStatSignal(signal);
        }
    }

    public void processAnaSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = "" + unitFinder.getValue();
        String attribute = getAttribute(unitId);

        SplitUnit splitUnit = fullSplitMap.get(unitId);
        if (splitUnit != null) {
            /**
             * 从问卷 z_ana_ 中来的分析结果，与Nebula查询回来的分析结果，结构并不相同，转换成一样的结构
             */
            DataFinder periodFinder = signal.dataPeriod();
            if (!periodFinder.isFound())
                return;
            String dataPeriod = "" + periodFinder.getValue();
            DataCar car = splitUnit.getDataCar();
            DataFinder indexFinder = signal.indexId();
            if (!indexFinder.isFound())
                return;
            String indexId = transformIndexId(indexFinder.getValue());
            IndexGrid indexGrid = indexIdGridMap.get(indexId);
            if (indexGrid == null)
                return;
            String indexCode = getIndexCode(indexGrid);
            if (!fullIndexCodeSet.contains(indexCode)) // 指标分析结果不在当前商业地图中
                return;
            HashMap dataMap = car.appendIndexCode(indexCode, signal.getMapData());

            String orgType = this.category.getType();
            switch (orgType) {
                case "地产神客":
                    car.appendMsdData(dataPeriod, dataMap, attribute, MsdType.MSD_SALES);
                    break;
                case "物业神客":
                    car.appendMsdData(dataPeriod, dataMap, attribute, MsdType.MSD_PROPERTY);
                    break;
                default:
                    car.appendZataData(dataPeriod, dataMap, attribute);
                    break;
            }
        }
    }

    public void _processAnaSignal(Signal signal, int pageNo) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String brainName = signal.getBrainName();
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        SplitUnit splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String networkType;
        if (StringUtils.startsWith(signal.getSource(), "y_ms_ana_")) {
            networkType = "msd";
        } else {
            networkType = "satis";
        }

        HashMap mapData = signal.getMapData();
        String quesNo = mapData.get("ANALYSIS_INDEX_NAME") + "";
        String[] source = signal.getSource().split("_");
        String examDbId = source[source.length - 1];
        MLDataSetExam mlDataSetExam = examDbIdAndMLDataSetExam.get(Long.parseLong(examDbId));

        // 质量问题
        if (mlDataSetExam != null && StringUtils.equalsIgnoreCase(quesNo, mlDataSetExam.getHouseQualityQuestionNo())) {
            HashMap<String, String> houseOptionMap = new HashMap<>();
            IndexTeam.prepareHouseIndex(mlDataSetExam.getHouseQualityExpression(), houseOptionMap);
            for (String optionValue : houseOptionMap.keySet()) {
                if (optionValue.endsWith("_id"))
                    continue;
                String indexCode = houseOptionMap.get(optionValue);
                IndexGrid indexGrid = indexCodeGridMap.get(indexCode);

                String c = mapData.get("C" + optionValue) + "";
                String total;
                String sampleOfOptions = mapData.get("SAMPLE_OF_OPTIONS") + "";
                if (StringUtils.isNotBlank(sampleOfOptions) && !sampleOfOptions.equalsIgnoreCase("null")) {
                    total = LegacyDataReader.findAnaDataInMap(mapData, "t" + optionValue, false);
                } else {
                    total = LegacyDataReader.findAnaDataInMap(mapData, "t", false);
                }
                if (StringUtils.isBlank(c) || StringUtils.isBlank(total))
                    continue;
                HashMap newData = new HashMap();
                newData.put(indexCode + "_C1", c);
                newData.put(indexCode + "_TOTAL_SAMPLE", total);

                anaSignalValue(pageNo, brainName, unitId, attribute, dataPeriod, newData, indexGrid, indexCode, networkType);
                addInstanceForPostCalculate(newData, splitUnit, dataPeriod, attribute, indexCode, networkType);
            }

        } else {
            DataFinder indexFinder = signal.indexId();
            if (!indexFinder.isFound())
                return;
            String indexId = transformIndexId(indexFinder.getValue());
            if (StringUtils.equals(indexId, "-9999") && StringUtils.equals(splitUnit.getType(), "物业神客"))
                indexId = "-9998";
            IndexGrid indexGrid = indexIdGridMap.get(indexId);
            if (indexGrid == null)
                return;
            String indexCode = getIndexCode(indexGrid);
            HashMap dataMap = dataCar.appendIndexCode(indexCode, mapData);
            anaSignalValue(pageNo, brainName, unitId, attribute, dataPeriod, dataMap, indexGrid, indexCode, networkType);
            addInstanceForPostCalculate(dataMap, splitUnit, dataPeriod, attribute, indexCode, networkType);
        }
    }

    public void anaSignalValue(int pageNo,
                               String brainName,
                               String unitId,
                               String attribute,
                               String dataPeriod,
                               HashMap dataMap,
                               IndexGrid indexGrid,
                               String indexCode,
                               String networkType) {
        if (pageNo == 0) {
            // fill up 全局排序数据
            SplitUnit sortUnit = globalMap.get(orgUnitId(unitId, attribute));
            if (sortUnit != null) {
                boolean sampleValid = false;
                // 判断样本量是否超过限制条件
                if (sortUnit.getGlobalSortSampleLimit() != null) {
                    DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode, "SAMPLE", false);
                    if (dataChip.isExist()) {
                        if (dataChip.intValue() >= sortUnit.getGlobalSortSampleLimit()) {
                            sampleValid = true;
                        }
                    }
                } else {
                    sampleValid = true;
                }
                if (sampleValid) {
                    DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode, indexGrid.getStrDefaultDataType(), false);
                    if (dataChip.isExist()) {
                        sortUnit.getGlobalSortCar().addValue(dataPeriod, indexCode, attribute, dataChip.doubleValue());
                    }
                    if (indexCode.equals("ra3a5")) {
                        // save nps
                        dataChip = dataCar._zataDataChip(dataMap, indexCode, "NPS", false);
                        if (dataChip.isExist()) {
                            sortUnit.getGlobalSortCar().addValue(dataPeriod, "NPS", attribute, dataChip.doubleValue());
                        }
                    }
                }
            }
        }

        SplitUnit splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        if (pageNo == 0) {
            // 迁移到SuperBrain时注释掉
            log.error("迁移到SuperBrain时注释掉，需要重新取数");
//            // fill up commercial link with correlation values
//            DataChip correlationChip = dataCar._zataDataChip(dataMap, indexCode, "COR", false);
//            if (correlationChip.isExist()) {
//                String toCode = IndexGridService.correlationMap.get(indexCode);
//                if (StringUtils.isNotBlank(toCode)) {
//                    if (relationMatrixLinkMap.get(splitUnit.get_id() + "") != null && relationMatrixLinkMap.get(splitUnit.get_id() + "").get(indexCode) != null
//                            && relationMatrixLinkMap.get(splitUnit.get_id() + "").get(indexCode).get(toCode) != null) {
//                        List<CommercialMatrixLink> linkList = relationMatrixLinkMap.get(splitUnit.get_id() + "").get(indexCode).get(toCode);
//                        for (CommercialMatrixLink link : linkList) {
//                            link.addCorrelationValue(dataPeriod, attribute, correlationChip.doubleValue());
//                        }
//                    }
//                    else if (relationMatrixLinkMap.get(splitUnit.get_id() + "") != null && relationMatrixLinkMap.get(splitUnit.get_id() + "").get(toCode) != null
//                            && relationMatrixLinkMap.get(splitUnit.get_id() + "").get(toCode).get(indexCode) != null) {
//                        List<CommercialMatrixLink> linkList = relationMatrixLinkMap.get(splitUnit.get_id() + "").get(toCode).get(indexCode);
//                        for (CommercialMatrixLink link : linkList) {
//                            link.addCorrelationValue(dataPeriod, attribute, correlationChip.doubleValue());
//                        }
//                    }
//                }
//            }
        }

        // String[] keys = new String[]{brainName, networkType, splitUnit.get_id() + "", dataPeriod, indexCode};
        String[] keys = new String[]{networkType, splitUnit.get_id() + "", dataPeriod, indexCode};
        List<AbstractDataPoint> list = getPointsFromMapping(networkType, keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(Objects::nonNull)
                .filter(x -> StringUtils.isNotBlank(x.getAttribute()))
                .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                .filter(x -> StringUtils.isBlank(x.getStrValue()))
                .collect(Collectors.toList());

        for (AbstractDataPoint point : points) {
            if (StringUtils.equalsIgnoreCase(networkType, "satis")) {
                DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode, point.getDataType(), false);
                if (!dataChip.isExist())
                    continue;
                ((DataPoint) point).setValue(dataChip.doubleValue());
            } else {
                DataChip dataChip = dataCar._msdDataChipByLevel(dataMap, indexCode, point.getDataType(), indexGrid.getIndexLevel());
                if (!dataChip.isExist())
                    continue;
                if (StringUtils.contains(dataChip.getValue(), "/")) {
                    point.setStrValue(dataChip.getValue());
                } else {
                    ((DataPoint) point).setValue(dataChip.doubleValue());
                }
            }
        }
    }

    public void processIndustryAnaSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = "" + unitFinder.getValue();
        String attribute = getAttribute(unitId);

        AllianceSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit != null) {
            DataFinder periodFinder = signal.dataPeriod();
            if (!periodFinder.isFound())
                return;
            String dataPeriod = "" + periodFinder.getValue();
            DataCar car = splitUnit.getDataCar();
            car.appendZataData(dataPeriod, new HashMap<>(signal.getMapData()), attribute);
        }
    }

    public void _processIndustryAnaSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        HashSet<String> indexCodeSet = fullIndexCodeSet;
        HashMap dataMap = signal.getMapData();
        // 如果从MySQL取数，会在数据中补充指标信息，Nebula取数则没有指标信息
        String indexCode_mysql = AbstractBrainPointProducer.getDataValue(dataMap, "source_table_field");
        if (StringUtils.isNotBlank(indexCode_mysql) && !StringUtils.equalsIgnoreCase(indexCode_mysql, "null")) {
            dataMap = dataCar.appendIndexCode(indexCode_mysql.toUpperCase(), dataMap);
            indexCodeSet = new HashSet<>();
            indexCodeSet.add(indexCode_mysql);
        }

        // fill up 全局排序数据
        AllianceSplitUnit sortUnit = globalAllianceMap.get(allianceOrgUnitId(unitId, attribute));
        if (sortUnit != null) {
            for (String indexCode : indexCodeSet) {
                // 应该不存在 NPS 需要特殊处理的情况
                // 行业排序暂不 判断样本量是否超过限制条件
                IndexGrid indexGrid = indexCodeGridMap.get(indexCode);
                if (indexGrid != null) {
                    DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode.toUpperCase(), indexGrid.getStrDefaultDataType(), false);
                    if (dataChip.isExist()) {
                        sortUnit.getGlobalSortCar().addValue(dataPeriod, indexCode, attribute, dataChip.doubleValue());
                    }
                }
                if (indexCode.equals("ra3a5")) {
                    // save nps
                    DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode.toUpperCase(), "NPS", false);
                    if (dataChip.isExist()) {
                        sortUnit.getGlobalSortCar().addValue(dataPeriod, "NPS", attribute, dataChip.doubleValue());
                    }
                }
            }
        }

        AllianceSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        for (String indexCode : indexCodeSet) {
            String[] keys = new String[]{"industryFree", splitUnit.get_id() + "", dataPeriod, indexCode};
            List<AbstractDataPoint> list = getPointsFromMapping("industryFree", keys);
            if (list == null)
                continue;

            List<AbstractDataPoint> points = list.stream()
                    .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());
            if (points.isEmpty()) {
                continue;
            }

            for (AbstractDataPoint point : points) {
                DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode.toUpperCase(), point.getDataType(), false);
                if (!dataChip.isExist())
                    continue;
                ((DataPoint) point).setValue(dataChip.doubleValue());
            }
        }

        for (String indexCode : indexCodeSet) {
            addInstanceForPostCalculate(dataMap, splitUnit, dataPeriod, attribute, indexCode, "industryFree");
        }
    }

    public void addInstanceForPostCalculate(HashMap dataMap,
                                            FeatureSplitUnit splitUnit,
                                            String dataPeriod,
                                            String attribute,
                                            String indexCode,
                                            String type) {
        String[] keys = new String[]{"postCalculate", splitUnit.splitGroupId(), dataPeriod, indexCode};
        List<AbstractDataPoint> list = getPointsFromMapping("postCalculate", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                .collect(Collectors.toList());

        DataChip dataChip = null;
        for (AbstractDataPoint point : points) {
            switch (type) {
                case "satis":
                    dataChip = dataCar._zataDataChip(dataMap, indexCode, point.getDataType(), false);
                    break;
                case "msd":
                    IndexGrid indexGrid = indexCodeGridMap.get(indexCode);
                    dataChip = dataCar._msdDataChipByLevel(dataMap, indexCode, point.getDataType(), indexGrid.getIndexLevel());
                    break;
                case "industryFree":
                    dataChip = dataCar._zataDataChip(dataMap, indexCode.toUpperCase(), point.getDataType(), false);
                    break;
            }

            if (dataChip == null || !dataChip.isExist())
                continue;

            DataPoint dataPoint = DataPointBuilder.aDataPoint()
                    .splitUnitId(splitUnit.getId())
                    .splitName(splitUnit.name())
                    .value(dataChip.doubleValue())
                    .attribute(attribute)
                    .indexName(point.getIndexName())
                    .dataPeriod(point.getDataPeriod())
                    .higherIsBetter(point.getHigherIsBetter())
                    .planarCode(point.getPlanarCode())
                    .indexLevel(point.getIndexLevel())
                    .indexCode(indexCode)
                    .networkType(point.getNetworkType())
                    .build();
            point.addInstance(dataPoint);
        }
    }

    public void processTextSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = "" + unitFinder.getValue();
        String attribute = getAttribute(unitId);
        SplitUnit splitUnit = fullSplitMap.get(unitId);
        appendZataTextData(signal, attribute, splitUnit);
    }

    public void _processTextSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        SplitUnit splitUnit = fullSplitMap.get(unitId);
        textPointValue(signal, attribute, splitUnit);
    }

    public void processIndustryTextSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = "" + unitFinder.getValue();
        String attribute = getAttribute(unitId);
        AllianceSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        appendZataTextData(signal, attribute, splitUnit);
    }

    public void _processIndustryTextSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        AllianceSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        textPointValue(signal, attribute, splitUnit);
    }

    public void appendZataTextData(Signal signal, String attribute, FeatureSplitUnit splitUnit) {
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = "" + periodFinder.getValue();
        DataCar car = splitUnit.getDataCar();
        car.appendZataTextData(dataPeriod, new HashMap<>(signal.getMapData()), attribute);
    }

    public void textPointValue(Signal signal, String attribute, FeatureSplitUnit splitUnit) {
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        /*
          预先设置的指标code，code在生物电流的配置中设置，只能放在signal的数据中暂存
          6.0使用商业矩阵定义，7.0放到wave中指定指标code集合
          构建看板/临时计算时，没有合适的时机将code放入CommercialParam，直接通过数据处理
         */
        Set<String> _fullIndexCodeSet = fullIndexCodeSet;
        if (null != signal.getMapData() && null != signal.getMapData().get("INDEX_CODE_LIST")) {
            String text = signal.getMapData().get("INDEX_CODE_LIST") + "";
            _fullIndexCodeSet = Arrays.stream(text.split(",")).collect(Collectors.toSet());
        }

        for (String planarCode : _fullIndexCodeSet) {
            if (!StringUtils.startsWith(planarCode, "text_"))
                continue;
            planarCode = planarCode.replace("text_", "");

            String[] keys = new String[]{"text", splitUnit.get_id() + "", dataPeriod, planarCode};
            List<AbstractDataPoint> list = getPointsFromMapping("text", keys);
            if (list == null)
                continue;

            List<AbstractDataPoint> points = list.stream()
                    .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());

            for (AbstractDataPoint point : points) {
                DataChip dataChip = dataCar._zataTextDataChip(signal.getMapData(), "C" + planarCode, point.getDataType());
                if (!dataChip.isExist())
                    continue;
                ((DataPoint) point).setValue(dataChip.doubleValue());
            }
        }
    }

    public String getAttribute(String unitId) {
        String attribute = "总体";
        String key = fullSplitMapping.getKey(Long.parseLong(unitId));
        if (StringUtils.isBlank(key)) {
            key = fullAllianceSplitMapping.getKey(Long.parseLong(unitId));
        }
        if (StringUtils.isNotBlank(key)) {
            String[] strings = key.split("_");
            attribute = strings[1].replaceAll("#7", "");
        }
        return attribute;
    }

    public String orgUnitId(String unitId, String attribute) {
        if (attribute.equals("总体"))
            return unitId;
        String key = fullSplitMapping.getKey(Long.parseLong(unitId));
        if (StringUtils.isNotBlank(key)) {
            String[] strings = key.split("_");
            return "" + globalIdMap.get(strings[0]);
        }
        return "-9999L";
    }

    public String allianceOrgUnitId(String unitId, String attribute) {
        if (attribute.equals("总体"))
            return unitId;
        String key = fullAllianceSplitMapping.getKey(Long.parseLong(unitId));
        if (StringUtils.isNotBlank(key)) {
            String[] strings = key.split("_");
            return "" + globalAllianceIdMap.get(strings[0]);
        }
        return "-9999L";
    }

    public String transformIndexId(String indexId) {
        if (StringUtils.startsWith(indexId, "single_")) {
            indexId = msdExamIndexIdAndStandardIdMap.get(indexId.replace("single_", ""));
        } else if (StringUtils.startsWith(indexId, "unite_")) {
            indexId = msdExamUniteIndexIdAndStandardIdMap.get(indexId.replace("unite_", ""));
        }
        return indexId;
    }

    public void processIndustryMarkSignal(Signal signal) {
        String analysisName = signal.getMapData().get("ANALYSIS_NAME") + "";
        if (StringUtils.isBlank(analysisName) || StringUtils.equalsIgnoreCase(analysisName, "null"))
            return;

        String attribute = "总体";
        if (StringUtils.contains(analysisName, "+")) {
            attribute = (signal.getMapData().get("GROUPING_NAME") + "").replaceAll(";", "-");
        }

        Long splitUnitId = null;
        if (StringUtils.contains(analysisName, "行业总体")) {
            splitUnitId = INDUSTRY_AVERAGE.getSplitUnitId();
        } else if (StringUtils.contains(analysisName, "行业标杆")) {
            splitUnitId = INDUSTRY_BENCHMARK.getSplitUnitId();
        }
        SplitUnit splitUnit = fullSplitMap.get(splitUnitId + "");
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String indexId = null;
        DataFinder indexIdFinder = signal.indexId();
        if (indexIdFinder.isFound()) {
            indexId = indexIdFinder.getValue();
        } else {
            DataFinder indexDbIdFinder = signal.indexDbId();
            if (indexDbIdFinder.isFound()) {
                indexId = indexLongAndStrIdMap.get(Long.parseLong(indexDbIdFinder.getValue()));
            }
        }
        if (StringUtils.isBlank(indexId))
            return;

        IndexGrid indexGrid = indexIdGridMap.get(indexId);
        if (indexGrid == null)
            return;
        String indexCode = getIndexCode(indexGrid);

        DataCar car = splitUnit.getDataCar();
        HashMap dataMap = car.appendIndexCode(indexCode, signal.getMapData());
        car.appendZataData(dataPeriod, dataMap, attribute);
    }

    public void _processIndustryMarkSignal(Signal signal) {
        String analysisName = signal.getMapData().get("ANALYSIS_NAME") + "";
        if (StringUtils.isBlank(analysisName) || StringUtils.equalsIgnoreCase(analysisName, "null"))
            return;

        String attribute = "总体";
        if (StringUtils.contains(analysisName, "+")) {
            attribute = (signal.getMapData().get("GROUPING_NAME") + "").replaceAll(";", "-");
        }

        Long splitUnitId = null;
        FeatureSplitUnit splitUnit = null;
        if (StringUtils.contains(analysisName, "行业总体")) {
            splitUnitId = INDUSTRY_AVERAGE.getSplitUnitId();
            splitUnit = fullSplitMap.get(splitUnitId + "");
        } else if (StringUtils.contains(analysisName, "行业标杆")) {
            splitUnitId = INDUSTRY_BENCHMARK.getSplitUnitId();
            splitUnit = fullSplitMap.get(splitUnitId + "");
        } else if (StringUtils.contains(analysisName, "行业分位值")) {
            String computing = signal.getMapData().get("COMPUTING") + "";
            String percent = computing.replace("percentRank-", "");
            String id = "-301" + percent;
            splitUnitId = Long.parseLong(id);
            splitUnit = fullSplitMap.get(splitUnitId + "");
        } else {
            // 2021_top11_20+4类业主_分位值
            // 2021_top11_20_排名
            String splitName = analysisName.substring(analysisName.indexOf("_") + 1)
                    .replaceAll("_分位值", "")
                    .replaceAll("_排名", "");
            if (StringUtils.contains(splitName, "+")) {
                splitName = splitName.substring(0, analysisName.indexOf("+"));
            }
            String finalSplitName = splitName;
            splitUnit = fullAllianceSplitMap.values().stream()
                    .filter(x -> StringUtils.equals(x.getName().split("\\(")[0].replaceAll("-", "_"), finalSplitName))
                    .findAny()
                    .orElse(null);
        }
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String indexId = null;
        DataFinder indexIdFinder = signal.indexId();
        Long longId = -1L;
        if (indexIdFinder.isFound()) {
            indexId = indexIdFinder.getValue();
        } else {
            DataFinder indexDbIdFinder = signal.indexDbId();
            if (indexDbIdFinder.isFound()) {
                longId = Long.parseLong(indexDbIdFinder.getValue());
                indexId = indexLongAndStrIdMap.get(longId);
            }
        }
        if (StringUtils.isBlank(indexId))
            return;
        IndexGrid indexGrid = indexIdGridMap.get(indexId);
        if (indexGrid == null)
            return;
        String indexCode = getIndexCode(indexGrid);
        boolean isNPS = false;
        if (longId == 2L && ("" + signal.getMapData().get("INDEX_NAME")).equalsIgnoreCase("NPS")) {
            isNPS = true;
        }
        HashMap dataMap = dataCar.appendIndexCode(indexCode, signal.getMapData());

        String[] keys = new String[]{"industryMark", splitUnit.get_id() + "", dataPeriod, indexCode};
        List<AbstractDataPoint> list = getPointsFromMapping("industryMark", keys);
        if (list == null)
            return;

        String finalAttribute = attribute;
        List<AbstractDataPoint> points = list.stream()
                .filter(x -> isAttributeEqual(x.getAttribute(), finalAttribute))
                .filter(x -> StringUtils.isBlank(x.getStrValue()))
                .collect(Collectors.toList());

        for (AbstractDataPoint point : points) {
            boolean dataNPS = point.getDataType().equalsIgnoreCase("NPS");
            if ((!isNPS && !dataNPS) || (isNPS && dataNPS)) {
                DataChip dataChip = dataCar._zataDataChip(dataMap, indexCode, point.getDataType(), true);
                if (!dataChip.isExist())
                    continue;
                ((DataPoint) point).setValue(dataChip.doubleValue());
            }
        }
    }

    public void processStatSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        FeatureSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        HashMap stat = mapper.fromJson(statResult, HashMap.class);
        splitUnit.getDataCar().appendZataData(dataPeriod, stat, attribute);
    }

    public void _processNameListSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        FeatureSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        HashMap stat = mapper.fromJson(statResult, HashMap.class);

        String[] keys = new String[]{"nameList", splitUnit.get_id() + "", dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("nameList", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                .filter(x -> StringUtils.isBlank(x.getStrValue()))
                .collect(Collectors.toList());

        for (AbstractDataPoint point : points) {
            DataChip dataChip = dataCar._zataDataChip(stat, null, point.getDataType(), false);
            if (!dataChip.isExist())
                continue;
            ((DataPoint) point).setValue(dataChip.doubleValue());
        }
    }

    public void _processHouseSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        FeatureSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        LianJiaHouseStat houseStat = mapper.fromJson(statResult, LianJiaHouseStat.class);

        String[] keys = new String[]{"house", splitUnit.get_id() + "", dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("house", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                .filter(x -> StringUtils.isBlank(x.getStrValue()))
                .collect(Collectors.toList());

        houseBrainPointProducer.setInstanceList(points, houseStat);
    }

    public void _processSameBatchSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        SameBatchStat sameBatchStat = mapper.fromJson(statResult, SameBatchStat.class);

        String[] keys = new String[]{"sameBatch", unitId, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("sameBatch", keys);
        if (list == null)
            return;
        sameBatchBrainPointProducer.setInstanceList(this, list, sameBatchStat);
    }

    public void _processSurroundingsSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        List<HashMap> dataList = new ArrayList<>();
        String[] keys;
        String networkType = "surroundings";

        if (StringUtils.equals(signal.getSource(), "ds.lj_matching")) {
            HashMap newMap = new HashMap();
            for (Object key : mapData.keySet()) {
                String lowKey = (key + "").toLowerCase();
                newMap.put(lowKey, mapData.get(key));
            }
            dataList.add(newMap);
            keys = new String[]{networkType, "楼盘周边配套数量统计", ""};

        } else {
            DataFinder periodFinder = signal.dataPeriod();
            if (!periodFinder.isFound())
                return;
            String dataPeriod = periodFinder.getValue();

            DataFinder statFinder = signal.statResult();
            if (!statFinder.isFound())
                return;
            String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"").replaceAll("_avg", "_amount");
            dataList = mapper.fromJson(statResult, List.class);

            String type = AbstractBrainPointProducer.getDataValue(mapData, "type");
            keys = new String[]{networkType, type, dataPeriod};
        }

        List<AbstractDataPoint> list = getPointsFromMapping(networkType, keys);
        if (list == null)
            return;
        surroundingsBrainPointProducer.setInstanceList(list, dataList);
    }

    public void _processGlobalSignal(Signal signal) {
        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        HashMap<String, Object> globalStat = mapper.fromJson(statResult, HashMap.class);

        String type = AbstractBrainPointProducer.getDataValue(signal.getMapData(), "type");
        String[] keys = new String[]{"global", type, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("global", keys);
        if (list == null)
            return;
        globalBrainPointProducer.setInstanceList(list, globalStat);
    }

    public void processZataSignal(Signal signal) {
        String lastOrgCode = signal.getMapData().get("LASTORGCODE") + "";
        SplitUnit splitUnit = fullSplitMap.values().stream()
                .filter(x -> StringUtils.equalsIgnoreCase(x.getOrgUnitCode(), lastOrgCode))
                .filter(x -> StringUtils.isBlank(x.getRespondentOption1ID()))
                .findAny()
                .orElse(null);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();
        splitUnit.getDataCar().addDataList(dataPeriod, new HashMap<>(signal.getMapData()));
    }

    public void _processZataSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        String lastOrgCode = AbstractBrainPointProducer.getDataValue(mapData, "lastOrgCode");
        String dataPeriod = AbstractBrainPointProducer.getDataValue(mapData, "capturePeriod");
        String owner4 = AbstractBrainPointProducer.getDataValue(mapData, "dOwnerTypeFour");
        String owner7 = AbstractBrainPointProducer.getDataValue(mapData, "dOwnerTypeSevenPlus");
        String decoration = AbstractBrainPointProducer.getDataValue(mapData, "dDecorationType");
        String house = AbstractBrainPointProducer.getDataValue(mapData, "dHouseType");
        List<String> attributes = Arrays.asList(owner4, owner7, decoration, house);

        String[] keys = new String[]{"voice"};
        List<AbstractDataPoint> list = getPointsFromMapping("voice", keys);
        String[] wordCloudKeys = new String[]{"wordCloud"};
        List<AbstractDataPoint> wordCloudList = getPointsFromMapping("wordCloud", wordCloudKeys);
        if (list == null && wordCloudList == null)
            return;

        List<AbstractDataPoint> points = null;
        if (list != null) {
            points = list.stream()
                    .filter(x -> StringUtils.startsWith(lastOrgCode, x.getOrgUnitCode()))
                    .filter(x -> x.getSubPeriods().contains(dataPeriod))
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getAttribute(), "总体") || attributes.containsAll(Arrays.asList(x.getAttribute().split("-"))))
                    .filter(x -> matchVoiceConditions(x, mapData))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());
        }

        List<AbstractDataPoint> wordCloudPoints = null;
        if (wordCloudList != null) {
            wordCloudPoints = wordCloudList.stream()
                    .filter(x -> StringUtils.startsWith(lastOrgCode, x.getOrgUnitCode()))
                    .filter(x -> x.getSubPeriods().contains(dataPeriod))
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getAttribute(), "总体") || attributes.containsAll(Arrays.asList(x.getAttribute().split("-"))))
                    .filter(x -> matchVoiceConditions(x, mapData))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());
        }

        voiceBrainPointProducer.setInstanceList(this, mapData, points, wordCloudPoints, false);
    }

    private boolean matchVoiceConditions(AbstractDataPoint point, HashMap mapData) {
        Map<String, String> conditions = point.getFilterVoiceConditions();
        if (conditions == null || conditions.isEmpty())
            return true;

        for (String key : conditions.keySet()) {
            String value = AbstractBrainPointProducer.getDataValue(mapData, key);
            if (!StringUtils.equals(value, conditions.get(key)))
                return false;
        }
        return true;
    }

    public void _processIndustryZataSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        String customer = AbstractBrainPointProducer.getDataValue(mapData, "customer_name");
        String city = AbstractBrainPointProducer.getDataValue(mapData, "city");
        String customerProject = AbstractBrainPointProducer.getDataValue(mapData, "customer_project");
        String dataPeriod = AbstractBrainPointProducer.getDataValue(mapData, "capturePeriod");
        String owner4 = AbstractBrainPointProducer.getDataValue(mapData, "dOwnerTypeFour");
        String owner7 = AbstractBrainPointProducer.getDataValue(mapData, "dOwnerTypeSevenPlus");
        String decoration = AbstractBrainPointProducer.getDataValue(mapData, "dDecorationType");
        String house = AbstractBrainPointProducer.getDataValue(mapData, "dHouseType");
        List<String> attributes = Arrays.asList(owner4, owner7, decoration, house);

        String[] keys = new String[]{"voice"};
        List<AbstractDataPoint> list = getPointsFromMapping("voice", keys);
        String[] wordCloudKeys = new String[]{"wordCloud"};
        List<AbstractDataPoint> wordCloudList = getPointsFromMapping("wordCloud", wordCloudKeys);
        if (list == null && wordCloudList == null)
            return;

        List<String> names = new ArrayList<>();
        names.add(customer + "_" + city);
        names.add(customer);
        names.add(city);
        names.add(customerProject);

        List<AbstractDataPoint> points = null;
        if (list != null) {
            points = list.stream()
                    .filter(x -> names.contains(x.getSplitName()))
                    .filter(x -> x.getSubPeriods().contains(dataPeriod))
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getAttribute(), "总体") || attributes.containsAll(Arrays.asList(x.getAttribute().split("-"))))
                    .filter(x -> matchVoiceConditions(x, mapData))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());
        }

        List<AbstractDataPoint> wordCloudPoints = null;
        if (wordCloudList != null) {
            wordCloudPoints = wordCloudList.stream()
                    .filter(x -> names.contains(x.getSplitName()))
                    .filter(x -> x.getSubPeriods().contains(dataPeriod))
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getAttribute(), "总体") || attributes.containsAll(Arrays.asList(x.getAttribute().split("-"))))
                    .filter(x -> matchVoiceConditions(x, mapData))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());
        }

        voiceBrainPointProducer.setInstanceList(this, mapData, points, wordCloudPoints, true);
    }

    public void processGlobalVoiceSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        FeatureSplitUnit splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;
        splitUnit.getDataCar().addDataList("globalVoice", new HashMap<>(signal.getMapData()));
    }

    public void _processGlobalVoiceSignal(Signal signal) {
        String city = AbstractBrainPointProducer.getDataValue(signal.getMapData(), "city");
        if (StringUtils.equalsIgnoreCase(city, "null")) {
            city = "";
        }
        String[] keys = new String[]{"globalVoice", city};
        List<AbstractDataPoint> list = getPointsFromMapping("globalVoice", keys);
        if (list == null)
            return;
        globalVoiceBrainPointProducer.setInstanceList(this, signal.getMapData(), list);
    }

    public void processSaleScaleSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        FeatureSplitUnit splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();
        splitUnit.getDataCar().addDataList(dataPeriod, new HashMap<>(signal.getMapData()));
    }

    public void _processSaleScaleSignal(Signal signal) {
        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String[] keys = new String[]{"saleScale", dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("saleScale", keys);
        if (list == null)
            return;

        // log.info("Test-4. DataPoint List Size {}", list.size());
        saleScaleBrainPointProducer.setInstanceList(signal.getMapData(), list);
    }

    public void _processKnowledgePointSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        String knowledgePointType = AbstractBrainPointProducer.getDataValue(mapData, "knowledgePointType");
        String geoType = AbstractBrainPointProducer.getDataValue(mapData, "geoType");

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String[] keys = new String[]{"knowledgePoint", knowledgePointType};
        List<AbstractDataPoint> list = getPointsFromMapping("knowledgePoint", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> x.getSubPeriods().contains(dataPeriod))
                .filter(x -> StringUtils.isBlank(x.getKnowledgePointSecondType()) || knowledgePointBrainPointProducer.isKnowledgePointMatch(mapData, Arrays.asList(x.getKnowledgePointSecondType().split(",")), "knowledgePointSecondType", true))
                .filter(x -> StringUtils.isBlank(x.getNonStandardCustomer()) || knowledgePointBrainPointProducer.isKnowledgePointMatch(mapData, Arrays.asList(x.getNonStandardCustomer().split(",")), "nonStandardCustomer", true))
                .filter(x -> StringUtils.isBlank(x.getGeoType()) || StringUtils.equalsIgnoreCase(x.getGeoType(), geoType))
                .filter(x -> StringUtils.isBlank(x.getGeoName()) || knowledgePointBrainPointProducer.isKnowledgePointMatch(mapData, Arrays.asList(x.getGeoName().split(",")), "geoName", true))
                .filter(x -> StringUtils.isBlank(x.getNetInteractions()) || knowledgePointBrainPointProducer.isKnowledgePointMatch(mapData, Arrays.asList(x.getNetInteractions().split(",")), "netInteractions", true))
                .filter(x -> StringUtils.isBlank(x.getStrValue()))
                .collect(Collectors.toList());

        AbstractBrainPointProducer.addData(new HashMap(signal.getMapData()), points);
    }

    public void _processIndustrySaleScaleSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        String scope = AbstractBrainPointProducer.getDataValue(mapData, "scope");
        String period = AbstractBrainPointProducer.getDataValue(mapData, "period");

        String[] keys = new String[]{"industrySaleScale", scope, period};
        List<AbstractDataPoint> list = getPointsFromMapping("industrySaleScale", keys);
        if (list == null)
            return;
        for (AbstractDataPoint point : list) {
            DataChip dataChip = dataCar._brainDataChip(mapData, point.getDataType());
            if (!dataChip.isExist())
                continue;
            ((DataPoint) point).setValue(dataChip.doubleValue());
        }
    }

    public void _processHighNetWorthClientSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        FeatureSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            splitUnit = fullSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");

        String[] keys = new String[]{"highNetWorthClient", splitUnit.get_id() + "", dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("highNetWorthClient", keys);
        if (list == null)
            return;

        String statType = AbstractBrainPointProducer.getDataValue(signal.getMapData(), "statType");
        if (StringUtils.equalsIgnoreCase(statType, "highNetWorthClient")) {
            List<HashMap> stat = mapper.fromJson(statResult, List.class);
            HashMap dataMap = keyToUpperCase(stat.get(0));

            List<AbstractDataPoint> points = list.stream()
                    .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                    .filter(x -> StringUtils.isBlank(x.getStrValue()))
                    .collect(Collectors.toList());

            for (AbstractDataPoint point : points) {
                DataChip dataChip = dataCar._brainDataChip(dataMap, point.getDataType());
                if (!dataChip.isExist())
                    continue;
                ((DataPoint) point).setValue(dataChip.doubleValue());
            }

        } else if (StringUtils.containsIgnoreCase(statType, "highNetWorthClientAna")) {
            HashMap stat = mapper.fromJson(statResult, HashMap.class);
            HashMap dataMap = keyToUpperCase(stat);

            for (String code : fullIndexCodeSet) {
                if (StringUtils.startsWith(code, "text_"))
                    continue;
                List<AbstractDataPoint> points = list.stream()
                        .filter(x -> StringUtils.equalsIgnoreCase(x.getIndexCode(), code))
                        .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                        .filter(x -> StringUtils.isBlank(x.getStrValue()))
                        .collect(Collectors.toList());

                for (AbstractDataPoint point : points) {
                    DataChip dataChip = dataCar._zataDataChip(dataMap, code.toUpperCase(), point.getDataType(), false);
                    if (!dataChip.isExist())
                        continue;
                    ((DataPoint) point).setValue(dataChip.doubleValue());
                }
            }

        } else if (StringUtils.containsIgnoreCase(statType, "highNetWorthClientText")) {
            HashMap stat = mapper.fromJson(statResult, HashMap.class);
            HashMap dataMap = keyToUpperCase(stat);

            for (String code : fullIndexCodeSet) {
                if (!StringUtils.startsWith(code, "text_"))
                    continue;
                String planarCode = code.replace("text_", "");

                List<AbstractDataPoint> points = list.stream()
                        .filter(x -> StringUtils.equalsIgnoreCase(x.getPlanarCode(), planarCode))
                        .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                        .filter(x -> StringUtils.isBlank(x.getStrValue()))
                        .collect(Collectors.toList());

                for (AbstractDataPoint point : points) {
                    DataChip dataChip = dataCar._zataTextDataChip(dataMap, "C" + planarCode, point.getDataType());
                    if (!dataChip.isExist())
                        continue;
                    ((DataPoint) point).setValue(dataChip.doubleValue());
                }
            }
        }
    }

    public void _processLianjiaLoupanSignal(Signal signal) {
        initLouPanCodeAndProjectNameMap();

        HashMap mapData = signal.getMapData();
        String batchNo = AbstractBrainPointProducer.getDataValue(mapData, "batch_no");
        String louPanType = AbstractBrainPointProducer.getDataValue(mapData, "type");
        String louPanCode = AbstractBrainPointProducer.getDataValue(mapData, "code");
        String projectName = louPanCodeAndProjectNameMap.get(louPanCode);
        if (StringUtils.isBlank(projectName))
            return;

        String[] keys = new String[]{"lianJia", louPanType, projectName};
        List<AbstractDataPoint> list = getPointsFromMapping("lianJia", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> StringUtils.isBlank(x.getBatchNo()) || batchNo.compareTo(x.getBatchNo()) > 0)
                .collect(Collectors.toList());

        for (AbstractDataPoint point : points) {
            DataChip dataChip = dataCar._brainDataChip(mapData, point.getDataType());
            if (!dataChip.isExist())
                continue;
            point.setBatchNo(batchNo);
            switch (point.getDataType()) {
                case "PRICE":  // 单价
                case "TOTAL_PRICE":  // 总价
                case "PLOT_RATIO":  // 容积率
                case "PLANED_NUMBER_OF_HOUSEHOLDS":  // 规划户数
                    ((DataPoint) point).setValue(dataChip.doubleValue());
                    break;
                case "GREENING_RATE":  // 绿化率 40%
                case "AREA_COVERED":  // 占地面积 77,925㎡
                case "BUILT_UP_AREA":  // 建筑面积 203,458㎡
                case "RIGHT_OF_YEARS":  // 产权年限 70年
                case "PARKING_RATIO":  // 车位配比 1:1.08
                    String strValue = dataChip.getValue();
                    String value = strValue.replaceAll("[%,㎡年]", "").replaceAll("1:", "");
                    point.setStrValue(strValue);
                    ((DataPoint) point).setValue(Double.parseDouble(value));
                    break;
                case "PROPERTY_FEE":  // 物业费 2.7~2.95
                    strValue = dataChip.getValue();
                    if (!StringUtils.contains(strValue, "~")) {
                        ((DataPoint) point).setValue(dataChip.doubleValue());
                    } else {
                        String[] parts = strValue.split("~");
                        double min = Double.parseDouble(parts[0]);
                        double max = Double.parseDouble(parts[1]);
                        double avg = (min + max) / 2;
                        ((DataPoint) point).setValue(avg);
                    }
                    break;
                default:
                    point.setStrValue(dataChip.getValue());
                    break;
            }
        }
    }

    public void initLouPanCodeAndProjectNameMap() {
        if (louPanCodeAndProjectNameMap.isEmpty()) {
            String sql = "select * from (select industry_customer_name,projectname,lj_loupan_code from ds.lj_matching where lj_loupan_code != '' order by id desc) as a group by a.lj_loupan_code;";
            String[] columns = {"industry_customer_name", "projectname", "lj_loupan_code"};
            int[] types = {Types.VARCHAR, Types.VARCHAR, Types.VARCHAR};
            EntityManager manager = getCommandParam().getEntityManager().getEntityManagerFactory().createEntityManager();
            List<HashMap> dataList = getCommandParam().getTableDao().loadAllRecordsWithCustomSql(manager, sql, columns, types);
            for (HashMap data : dataList) {
                String louPanCode = "" + data.get("lj_loupan_code");
                String projectName = data.get("industry_customer_name") + "_" + data.get("projectname");
                louPanCodeAndProjectNameMap.putIfAbsent(louPanCode, projectName);
            }
        }
    }

    public void _processLianJiaHouseLayoutSignal(Signal signal) {
        initLouPanCodeAndProjectNameMap();

        HashMap mapData = signal.getMapData();
        String louPanCode = AbstractBrainPointProducer.getDataValue(mapData, "loupan_code");
        String projectName = louPanCodeAndProjectNameMap.get(louPanCode);
        if (StringUtils.isBlank(projectName))
            return;

        String[] keys = new String[]{"lianJiaHouseLayout", projectName};
        List<AbstractDataPoint> list = getPointsFromMapping("lianJiaHouseLayout", keys);
        if (list == null)
            return;

        lianJiaHouseLayoutBrainPointProducer.setInstanceList(mapData, list);
    }

    public void _processLianJiaAlbumSignal(Signal signal) {
        initLouPanCodeAndProjectNameMap();

        HashMap mapData = signal.getMapData();
        String type = AbstractBrainPointProducer.getDataValue(mapData, "type");
        String louPanCode = AbstractBrainPointProducer.getDataValue(mapData, "loupan_code");
        String projectName = louPanCodeAndProjectNameMap.get(louPanCode);
        if (StringUtils.isBlank(projectName))
            return;

        String[] keys = new String[]{"lianJiaAlbum", projectName};
        List<AbstractDataPoint> list = getPointsFromMapping("lianJiaAlbum", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> StringUtils.equals(x.getImgType(), type))
                .collect(Collectors.toList());

        lianJiaAlbumBrainPointProducer.setInstanceList(mapData, points);
    }

    public void _processCityHousePriceSignal(Signal signal) {
        DataFinder cityFinder = signal.city();
        if (!cityFinder.isFound())
            return;
        String city = cityFinder.getValue();

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String[] keys = new String[]{"cityHousePrice", city, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("cityHousePrice", keys);
        if (list == null)
            return;
        for (AbstractDataPoint point : list) {
            DataChip dataChip = dataCar._brainDataChip(signal.getMapData(), point.getDataType());
            if (!dataChip.isExist())
                continue;
            ((DataPoint) point).setValue(dataChip.doubleValue());
        }
    }

    public void _processCityPrimaryAndSecondaryMarketsSignal(Signal signal) {
        DataFinder cityFinder = signal.city();
        if (!cityFinder.isFound())
            return;
        String city = cityFinder.getValue();

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String[] keys = new String[]{"cityPrimaryAndSecondaryMarkets", city, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("cityPrimaryAndSecondaryMarkets", keys);
        if (list == null)
            return;
        for (AbstractDataPoint point : list) {
            DataChip dataChip = dataCar._brainDataChip(signal.getMapData(), point.getDataType());
            if (!dataChip.isExist())
                continue;
            ((DataPoint) point).setValue(dataChip.doubleValue());
        }
    }

    public void _processDistrictPrimaryAndSecondaryMarketsSignal(Signal signal) {
        DataFinder cityFinder = signal.city();
        if (!cityFinder.isFound())
            return;
        String city = cityFinder.getValue();

        DataFinder districtFinder = signal.district();
        if (!districtFinder.isFound())
            return;
        String district = districtFinder.getValue();

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        String[] keys = new String[]{"districtPrimaryAndSecondaryMarkets", city + "-" + district, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("districtPrimaryAndSecondaryMarkets", keys);
        if (list == null)
            return;
        for (AbstractDataPoint point : list) {
            DataChip dataChip = dataCar._brainDataChip(signal.getMapData(), point.getDataType());
            if (!dataChip.isExist())
                continue;
            ((DataPoint) point).setValue(dataChip.doubleValue());
        }
    }

    public void _processSalesTypeStatSignal(Signal signal) {
        DataFinder cityFinder = signal.city();
        if (!cityFinder.isFound())
            return;
        String city = cityFinder.getValue();

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        HashMap mapData = signal.getMapData();
        String analysisType = AbstractBrainPointProducer.getDataValue(mapData, "analysis_type");
        String industryCustomerName = AbstractBrainPointProducer.getDataValue(mapData, "industry_customer_name");
        String splitName = city;
        boolean isContainCustomer = StringUtils.isNotBlank(industryCustomerName) && !StringUtils.equalsIgnoreCase(industryCustomerName, "null");
        if (isContainCustomer) {
            splitName = industryCustomerName + "_" + city;
        }

        String[] keys = new String[]{"salesTypeStat", analysisType, dataPeriod, splitName};
        List<AbstractDataPoint> list = getPointsFromMapping("salesTypeStat", keys);
        if (list == null && isContainCustomer) {
            keys = new String[]{"salesTypeStat", analysisType, dataPeriod, "nonStandard"};
            list = getPointsFromMapping("salesTypeStat", keys);
        }
        if (list == null)
            return;

        salesTypeStatBrainPointProducer.setInstanceList(mapData, list, splitName);
    }

    public void _processIndustryProjectSignal(Signal signal) {
        DataFinder unitFinder = signal.splitUnitId();
        if (!unitFinder.isFound())
            return;
        String unitId = unitFinder.getValue();
        String attribute = getAttribute(unitId);
        AllianceSplitUnit splitUnit = fullAllianceSplitMap.get(unitId);
        if (splitUnit == null)
            return;

        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        DataFinder statFinder = signal.statResult();
        if (!statFinder.isFound())
            return;
        String statResult = statFinder.getValue().replaceAll("\\\\\"", "\"");
        HashMap stat = mapper.fromJson(statResult, HashMap.class);
        HashMap anaStatHashMap = (HashMap) stat.get("anaStatHashMap");

        String analysisType = AbstractBrainPointProducer.getDataValue(signal.getMapData(), "type");

        for (Object indexObj : anaStatHashMap.keySet()) {
            List<String> indexCodes = AbstractSegment.matchText(AbstractSegment.p_indexCode, indexObj + "", 1);
            if (indexCodes.isEmpty())
                continue;
            String indexCode = indexCodes.get(0);
            String[] keys = new String[]{"freeCalculate", analysisType, splitUnit.get_id() + "", dataPeriod, indexCode};
            List<AbstractDataPoint> list = getPointsFromMapping("freeCalculate", keys);
            if (list == null)
                continue;

            List<AbstractDataPoint> points = list.stream()
                    .filter(x -> isAttributeEqual(x.getAttribute(), attribute))
                    .collect(Collectors.toList());
            if (points.isEmpty())
                continue;

            HashMap anaStat = (HashMap) anaStatHashMap.get(indexObj);
            int totalSample = Integer.parseInt(anaStat.get("totalSample") + "");
            List<Integer> countArray = (List<Integer>) anaStat.get("countArray");
            if (totalSample == 0)
                continue;

            for (AbstractDataPoint point : points) {
                String dataType = point.getDataType().toUpperCase();
                if (!StringUtils.startsWith(dataType, "P") || dataType.length() > 4)
                    continue;

                int count = 0;
                if (StringUtils.contains(dataType, "_")) {
                    String[] array = dataType.replace("P_", "").split("");
                    for (String i : array) {
                        count += countArray.get(Integer.parseInt(i));
                    }
                } else {
                    int i = Integer.parseInt(dataType.replace("P", ""));
                    count = countArray.get(i);
                }
                Double value = 100D * count / totalSample;
                ((DataPoint) point).setValue(value);
            }
        }
    }

    public void _processUserSignal(Signal signal) {
        HashMap mapData = signal.getMapData();
        String mobile = AbstractBrainPointProducer.getDataValue(mapData, "d_mobile");
        String ownerLabel = AbstractBrainPointProducer.getDataValue(mapData, "owner_label");

        String[] keys = new String[]{"voice"};
        List<AbstractDataPoint> list = getPointsFromMapping("voice", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = new ArrayList<>();
        list.stream()
                .filter(x -> x.getInstanceList() != null)
                .forEach(x -> points.addAll(x.getInstanceList()));

        points.stream()
                .filter(x -> StringUtils.isBlank(x.getOwnerLabel()))
                .filter(x -> StringUtils.equals(x.getMobile(), mobile))
                .forEach(x -> x.setOwnerLabel(ownerLabel));
    }

    public void _processLabelSignal(Signal signal) {
        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        HashMap mapData = signal.getMapData();
        String labelType = AbstractBrainPointProducer.getDataValue(mapData, "labelType");
        String[] keys = new String[]{"label", labelType, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("label", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> checkSplit(mapData, x))
                .filter(x -> checkAttribute(mapData, x))
                .filter(x -> checkIndex(mapData, x))
                .collect(Collectors.toList());

        labelBrainPointProducer.setInstanceList(this, mapData, points);
    }

    public void _processConclusionSignal(Signal signal) {
        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        HashMap mapData = signal.getMapData();
        String[] keys = new String[]{"conclusion", dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("conclusion", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> checkSplit(mapData, x))
                .filter(x -> checkAttribute(mapData, x))
                .filter(x -> checkIndex(mapData, x))
                .filter(x -> matchVoiceConditions(x, mapData))
                .collect(Collectors.toList());

        conclusionBrainPointProducer.setInstanceList(this, mapData, points);
    }

    public void _processCustomStatSignal(Signal signal) {
        DataFinder periodFinder = signal.dataPeriod();
        if (!periodFinder.isFound())
            return;
        String dataPeriod = periodFinder.getValue();

        HashMap mapData = signal.getMapData();
        String statType = AbstractBrainPointProducer.getDataValue(mapData, "statType");
        String[] keys = new String[]{"customStat", statType, dataPeriod};
        List<AbstractDataPoint> list = getPointsFromMapping("customStat", keys);
        if (list == null)
            return;

        List<AbstractDataPoint> points = list.stream()
                .filter(x -> checkSplit(mapData, x))
                .filter(x -> checkAttribute(mapData, x))
                .filter(x -> checkIndex(mapData, x))
                .collect(Collectors.toList());

        customStatBrainPointProducer.setInstanceList(this, mapData, points);
    }

    public boolean checkSplit(HashMap mapData, AbstractDataPoint point) {
        if (point.getSplitUnitDbId() == null)
            return true;

        String splitUnitId = AbstractBrainPointProducer.getDataValue(mapData, "splitUnitId");
        String splitUnitName = AbstractBrainPointProducer.getDataValue(mapData, "splitUnitName");

        if (AbstractBrainPointProducer.notBlank(splitUnitId)) {
            // 这里隐含一个假设，只有自由切分，才会走到这个逻辑
            // labelSignal解析的时候，也会走到这里，如果nebula存label时，切分ID写法不是splitUnitId，也可以避免
            Object signalType = mapData.get("SIGNAL_TYPE");
            if (null != signalType && StringUtils.equals(signalType + "", "label")) {
                return StringUtils.equals(point.getSplitName(), splitUnitName);
            }
            FeatureSplitUnit splitUnit = fullAllianceSplitMap.get(splitUnitId);
            if (splitUnit == null)
                splitUnit = fullSplitMap.get(splitUnitId);
            if (splitUnit == null)
                return false;
            return point.getSplitUnitDbId().equals(splitUnit.get_id());
        } else {
            return StringUtils.equals(point.getSplitName(), splitUnitName);
        }
    }

    public boolean checkAttribute(HashMap mapData, AbstractDataPoint point) {
        if (StringUtils.isBlank(point.getAttribute()))
            return true;

        String attributeNum = AbstractBrainPointProducer.getDataValue(mapData, "attribute");
        String[] attributeNums = attributeNum.split(",");
        List<String> attributeList = new ArrayList<>();
        for (String num : attributeNums) {
            String attribute = StandardAttributeEnum.attributeNameByIntValue(num);
            attributeList.add(attribute);
        }
        return attributeList.contains(point.getAttribute());
    }

    public boolean checkIndex(HashMap mapData, AbstractDataPoint point) {
        if (StringUtils.isBlank(point.getIndexName()))
            return true;

        String indexCode = AbstractBrainPointProducer.getDataValue(mapData, "indexCode");
        String indexId = AbstractBrainPointProducer.getDataValue(mapData, "indexId");
        String planarCode = AbstractBrainPointProducer.getDataValue(mapData, "planarCode");

        if (AbstractBrainPointProducer.notBlank(indexCode)) {
            String[] indexCodes = indexCode.split(",");
            return Arrays.asList(indexCodes).contains(point.getIndexCode());
        } else if (AbstractBrainPointProducer.notBlank(indexId)) {
            String[] indexIds = indexId.split(",");
            return Arrays.asList(indexIds).contains(point.getIndexId() + "");
        } else if (AbstractBrainPointProducer.notBlank(planarCode)) {
            String[] planarCodes = planarCode.split(",");
            return Arrays.asList(planarCodes).contains(point.getPlanarCode());
        }
        return false;
    }

    private HashMap<String, Object> keyToUpperCase(HashMap<String, Object> map) {
        if (map == null)
            return null;
        HashMap<String, Object> result = new HashMap<>();
        map.forEach((key, value) -> result.put(key.toUpperCase(), value));
        return result;
    }

    public void buildCommercialMatrixInstance() {
        for (ViewPointParam viewPointParam : viewpoints) {
            viewPointParam.buildCommercialMatrixInstance(this);
        }
    }

//    public int totalViewPoints() {
//        int total = 0;
//        for (ViewPointParam viewPointParam : viewpoints) {
//            if (viewPointParam.getViewPoints() != null)
//                total += viewPointParam.getViewPoints().size();
//        }
//        return total;
//    }

//    public void scanAndUpdateVisualizationMerge(int fromIndex, int toIndex) {
//        for (ViewPointParam pointParam : viewpoints) {
//            pointParam.scanAndUpdateVisualizationMerge(this, fromIndex, toIndex);
//        }
//    }

//    public void _postCalculate(int fromIndex, int toIndex) {
//        for (ViewPointParam pointParam : viewpoints) {
//            pointParam._postCalculate(fromIndex, toIndex);
//        }
//    }

//    public void scanVisualization() {
//        for (ViewPointParam pointParam : viewpoints) {
//            pointParam.scanVisualization();
//        }
//    }

//    public void _scanVisualization(boolean keepDynamicLayout, int fromIndex, int toIndex) {
//        for (ViewPointParam pointParam : viewpoints) {
//            pointParam._scanVisualization(this, keepDynamicLayout, fromIndex, toIndex);
//        }
//    }

    public void buildDataNetwork(/*int fromIndex, int toIndex*/) {
        for (ViewPointParam pointParam : viewpoints) {
            pointParam.buildDataNetwork(/*fromIndex, toIndex*/);
        }
        // 将所有视图中的点集合成一个网络
        for (ViewPointParam pointParam : viewpoints) {
            for (AbstractViewPoint viewPoint : pointParam.getViewPoints()) {
//                if (viewPoint.getViewPointNo() >= fromIndex && viewPoint.getViewPointNo() < toIndex) {
                if (viewPoint instanceof LegacyOrgViewPoint) {
                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) viewPoint;
                    for (ViewPointDataMatrixInstance viewPointDataMatrixInstance : orgViewPoint.getViewPointDataMatrixInstanceMap().values()) {
                        for (DynamicLayout layout : viewPointDataMatrixInstance.getVisualLayoutMap().values()) {
                            List<AbstractBlock> blockList = layout.collectBlock();
                            for (AbstractBlock block : blockList) {
                                block.setPointFromNetwork(true);
                                for (List<AbstractDataPoint> pointList : block.getPointNetworkMap().values()) {
                                    if (pointList == null)
                                        continue;
                                    for (AbstractDataPoint dataPoint : pointList) {
                                        pointsMapping(dataPoint);
                                    }
                                }
                            }
                        }
                    }
                    // build correlation links map

                    // 迁移到SuperBrain时注释掉
//                        for (CommercialMatrixLink link : orgViewPoint.getRelationLinks()) {
//                            ConcurrentHashMap<String, ConcurrentHashMap<String, List<CommercialMatrixLink>>> level1 = relationMatrixLinkMap.computeIfAbsent(orgViewPoint.getSplitUnit().get_id() + "", k -> new ConcurrentHashMap<>());
//                            ConcurrentHashMap<String, List<CommercialMatrixLink>> level2 = level1.computeIfAbsent(link.getFromCode(), k -> new ConcurrentHashMap<>());
//                            List<CommercialMatrixLink> level3 = level2.computeIfAbsent(link.getToCode(), k -> new ArrayList<>());
//                            level3.add(link);
//                        }
                }
//                }
            }
        }
    }

//    public void buildMathNet() {
//        for (ViewPointParam pointParam : viewpoints) {
//            for (AbstractViewPoint viewPoint : pointParam.getViewPoints()) {
//                if (viewPoint instanceof LegacyOrgViewPoint) {
//                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) viewPoint;
//                    if (orgViewPoint.getMathMatrixList() != null) {
//                        for (MathMatrix mathMatrix : orgViewPoint.getMathMatrixList()) {
//                            if (mathMatrix.getMatrixCar() != null) {
//                                for (List<DataPoint> list : mathMatrix.getMatrixCar().getDataMap().values()) {
//                                    for (DataPoint dataPoint : list) {
//                                        pointsMapping(dataPoint);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }

//    public void transformMatrix() {
//        for (ViewPointParam pointParam : viewpoints) {
//            for (AbstractViewPoint viewPoint : pointParam.getViewPoints()) {
//                if (viewPoint instanceof LegacyOrgViewPoint) {
//                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) viewPoint;
//                    if (orgViewPoint.getMathMatrixList() != null) {
//                        orgViewPoint.transformMatrix();
//                    }
//                }
//            }
//        }
//    }

    public synchronized void pointsMapping(AbstractDataPoint dataPoint) {
        String brainName = dataPoint.getBrainName();
        String networkType = dataPoint.getNetworkType();
        if (StringUtils.equalsIgnoreCase(networkType, "satis")
                || StringUtils.equalsIgnoreCase(networkType, "msd")
                || StringUtils.equalsIgnoreCase(networkType, "industryFree")
                || StringUtils.equalsIgnoreCase(networkType, "industryMark")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level1 = this.pointNetworkMap_1.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
//            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> level0 = pointNetworkMap_0.computeIfAbsent(brainName, k -> new ConcurrentHashMap<>());
//            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level1 = level0.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level2 = level1.computeIfAbsent(dataPoint.getSplitUnitDbId() + "", k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level4 = level3.computeIfAbsent(dataPoint.getIndexCode(), k -> new ArrayList<>());
            level4.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "nameList")
                || StringUtils.equalsIgnoreCase(networkType, "house")
                || StringUtils.equalsIgnoreCase(networkType, "sameBatch")
                || StringUtils.equalsIgnoreCase(networkType, "highNetWorthClient")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level1 = pointNetworkMap_2.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level2 = level1.computeIfAbsent(dataPoint.getSplitUnitDbId() + "", k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ArrayList<>());
            level3.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "text")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level1 = pointNetworkMap_3.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level2 = level1.computeIfAbsent(dataPoint.getSplitUnitDbId() + "", k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level4 = level3.computeIfAbsent(dataPoint.getPlanarCode(), k -> new ArrayList<>());
            level4.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "voice")
                || StringUtils.equalsIgnoreCase(networkType, "wordCloud")) {
            List<AbstractDataPoint> level1 = pointNetworkMap_4.computeIfAbsent(networkType, k -> new ArrayList<>());
            level1.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "saleScale")
                || StringUtils.equalsIgnoreCase(networkType, "conclusion")) {
            ConcurrentHashMap<String, List<AbstractDataPoint>> level1 = pointNetworkMap_5.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level2 = level1.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ArrayList<>());
            level2.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "knowledgePoint")) {
            ConcurrentHashMap<String, List<AbstractDataPoint>> level1 = pointNetworkMap_6.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level2 = level1.computeIfAbsent(dataPoint.getKnowledgePointType(), k -> new ArrayList<>());
            level2.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "industrySaleScale")
                || StringUtils.equalsIgnoreCase(networkType, "cityHousePrice")
                || StringUtils.equalsIgnoreCase(networkType, "cityPrimaryAndSecondaryMarkets")
                || StringUtils.equalsIgnoreCase(networkType, "districtPrimaryAndSecondaryMarkets")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level1 = pointNetworkMap_7.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level2 = level1.computeIfAbsent(dataPoint.getSplitName(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ArrayList<>());
            level3.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "postCalculate")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level1 = pointNetworkMap_8.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level2 = level1.computeIfAbsent(dataPoint.getSplitGroupId(), k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level4 = level3.computeIfAbsent(dataPoint.getIndexCode(), k -> new ArrayList<>());
            level4.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "lianJia")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level1 = pointNetworkMap_9.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level2 = level1.computeIfAbsent(dataPoint.getLouPanType(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level3 = level2.computeIfAbsent(dataPoint.getSplitName(), k -> new ArrayList<>());
            level3.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "salesTypeStat")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level1 = pointNetworkMap_10.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level2 = level1.computeIfAbsent(dataPoint.getAnalysisType(), k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level4 = level3.computeIfAbsent(dataPoint.getSplitName(), k -> new ArrayList<>());
            level4.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "lianJiaHouseLayout")
                || StringUtils.equalsIgnoreCase(networkType, "lianJiaAlbum")
                || StringUtils.equalsIgnoreCase(networkType, "globalVoice")) {
            ConcurrentHashMap<String, List<AbstractDataPoint>> level1 = pointNetworkMap_11.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level2 = level1.computeIfAbsent(dataPoint.getSplitName(), k -> new ArrayList<>());
            level2.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "global")
                || StringUtils.equalsIgnoreCase(networkType, "surroundings")
                || StringUtils.equalsIgnoreCase(networkType, "label")
                || StringUtils.equalsIgnoreCase(networkType, "customStat")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level1 = pointNetworkMap_12.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level2 = level1.computeIfAbsent(dataPoint.getAnalysisType(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level3 = level2.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ArrayList<>());
            level3.add(dataPoint);

        } else if (StringUtils.equalsIgnoreCase(networkType, "freeCalculate")) {
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>>> level1 = pointNetworkMap_13.computeIfAbsent(networkType, k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>>> level2 = level1.computeIfAbsent(dataPoint.getAnalysisType(), k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, ConcurrentHashMap<String, List<AbstractDataPoint>>> level3 = level2.computeIfAbsent(dataPoint.getSplitUnitDbId() + "", k -> new ConcurrentHashMap<>());
            ConcurrentHashMap<String, List<AbstractDataPoint>> level4 = level3.computeIfAbsent(dataPoint.getDataPeriod(), k -> new ConcurrentHashMap<>());
            List<AbstractDataPoint> level5 = level4.computeIfAbsent(dataPoint.getIndexCode(), k -> new ArrayList<>());
            level5.add(dataPoint);
        }
    }

    public List<AbstractDataPoint> getPointsFromMapping(String networkType, String[] keys) {
        if (StringUtils.equalsIgnoreCase(networkType, "satis")
                || StringUtils.equalsIgnoreCase(networkType, "msd")
                || StringUtils.equalsIgnoreCase(networkType, "industryFree")
                || StringUtils.equalsIgnoreCase(networkType, "industryMark")) {
            if (keys.length < 4 || pointNetworkMap_1.get(keys[0]) == null || pointNetworkMap_1.get(keys[0]).get(keys[1]) == null
                    || pointNetworkMap_1.get(keys[0]).get(keys[1]).get(keys[2]) == null) {
                log.debug("getPointsFromMapping 没有切分：{}, 分期：{}", keys[1], keys[2]);
                return null;
            }
            return pointNetworkMap_1.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "nameList")
                || StringUtils.equalsIgnoreCase(networkType, "house")
                || StringUtils.equalsIgnoreCase(networkType, "sameBatch")
                || StringUtils.equalsIgnoreCase(networkType, "highNetWorthClient")) {
            if (keys.length < 3 || pointNetworkMap_2.get(keys[0]) == null || pointNetworkMap_2.get(keys[0]).get(keys[1]) == null)
                return null;
            return pointNetworkMap_2.get(keys[0]).get(keys[1]).get(keys[2]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "text")) {
            if (keys.length < 4 || pointNetworkMap_3.get(keys[0]) == null || pointNetworkMap_3.get(keys[0]).get(keys[1]) == null
                    || pointNetworkMap_3.get(keys[0]).get(keys[1]).get(keys[2]) == null)
                return null;
            return pointNetworkMap_3.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "voice")
                || StringUtils.equalsIgnoreCase(networkType, "wordCloud")) {
            if (keys.length < 1)
                return null;
            return pointNetworkMap_4.get(keys[0]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "saleScale")
                || StringUtils.equalsIgnoreCase(networkType, "conclusion")) {
            if (keys.length < 2 || pointNetworkMap_5.get(keys[0]) == null)
                return null;
            return pointNetworkMap_5.get(keys[0]).get(keys[1]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "knowledgePoint")) {
            if (keys.length < 2 || pointNetworkMap_6.get(keys[0]) == null)
                return null;
            return pointNetworkMap_6.get(keys[0]).get(keys[1]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "industrySaleScale")
                || StringUtils.equalsIgnoreCase(networkType, "cityHousePrice")
                || StringUtils.equalsIgnoreCase(networkType, "cityPrimaryAndSecondaryMarkets")
                || StringUtils.equalsIgnoreCase(networkType, "districtPrimaryAndSecondaryMarkets")) {
            if (keys.length < 3 || pointNetworkMap_7.get(keys[0]) == null || pointNetworkMap_7.get(keys[0]).get(keys[1]) == null)
                return null;
            return pointNetworkMap_7.get(keys[0]).get(keys[1]).get(keys[2]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "postCalculate")) {
            if (keys.length < 4 || pointNetworkMap_8.get(keys[0]) == null || pointNetworkMap_8.get(keys[0]).get(keys[1]) == null
                    || pointNetworkMap_8.get(keys[0]).get(keys[1]).get(keys[2]) == null)
                return null;
            return pointNetworkMap_8.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "lianJia")) {
            if (keys.length < 3 || pointNetworkMap_9.get(keys[0]) == null || pointNetworkMap_9.get(keys[0]).get(keys[1]) == null)
                return null;
            return pointNetworkMap_9.get(keys[0]).get(keys[1]).get(keys[2]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "salesTypeStat")) {
            if (keys.length < 4 || pointNetworkMap_10.get(keys[0]) == null || pointNetworkMap_10.get(keys[0]).get(keys[1]) == null
                    || pointNetworkMap_10.get(keys[0]).get(keys[1]).get(keys[2]) == null)
                return null;
            return pointNetworkMap_10.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "lianJiaHouseLayout")
                || StringUtils.equalsIgnoreCase(networkType, "lianJiaAlbum")
                || StringUtils.equalsIgnoreCase(networkType, "globalVoice")) {
            if (keys.length < 2 || pointNetworkMap_11.get(keys[0]) == null)
                return null;
            return pointNetworkMap_11.get(keys[0]).get(keys[1]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "global")
                || StringUtils.equalsIgnoreCase(networkType, "surroundings")
                || StringUtils.equalsIgnoreCase(networkType, "label")
                || StringUtils.equalsIgnoreCase(networkType, "customStat")) {
            if (keys.length < 3 || pointNetworkMap_12.get(keys[0]) == null || pointNetworkMap_12.get(keys[0]).get(keys[1]) == null)
                return null;
            return pointNetworkMap_12.get(keys[0]).get(keys[1]).get(keys[2]);

        } else if (StringUtils.equalsIgnoreCase(networkType, "freeCalculate")) {
            if (keys.length < 5 || pointNetworkMap_13.get(keys[0]) == null || pointNetworkMap_13.get(keys[0]).get(keys[1]) == null
                    || pointNetworkMap_13.get(keys[0]).get(keys[1]).get(keys[2]) == null
                    || pointNetworkMap_13.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]) == null)
                return null;
            return pointNetworkMap_13.get(keys[0]).get(keys[1]).get(keys[2]).get(keys[3]).get(keys[4]);
        }

        return null;
    }

    public StreamGroup findGrouping(String groupingName) {
        for (StreamGroup group : groupings) {
            if (group.getName().equals(groupingName))
                return group;
        }
        return null;
    }

    public AbstractGrouping findSplitGrouping(String splitGroupId) {
        return groupings.stream()
                .map(StreamGroup::getGrouping)
                .filter(x -> StringUtils.equalsIgnoreCase(x.getId(), splitGroupId))
                .findFirst()
                .orElse(null);
    }

    public SplitGroup findSplitGroup(String splitGroupId) {
        AbstractGrouping grouping = groupings.stream()
                .map(StreamGroup::getGrouping)
                .filter(x -> StringUtils.equalsIgnoreCase(x.getId(), splitGroupId))
                .findFirst()
                .orElse(null);
        if (grouping instanceof LegacyOrgGrouping)
            return ((LegacyOrgGrouping) grouping).getSplitGroup();
        if (grouping instanceof MockGrouping)
            return ((MockGrouping) grouping).getSplitGroup();
        return null;
    }

    public AllianceSplitGroup findAllianceSplitGroup(String splitGroupId) {
        AbstractGrouping grouping = groupings.stream()
                .map(StreamGroup::getGrouping)
                .filter(x -> StringUtils.equalsIgnoreCase(x.getId(), splitGroupId))
                .findFirst()
                .orElse(null);
        if (grouping instanceof LegacyIndustryGrouping)
            return ((LegacyIndustryGrouping) grouping).getAllianceSplitGroup();
        return null;
    }

    public void registerSplitUnit(LegacyOrgIdentity orgIdentity) {
        SplitUnit mainUnit = orgIdentity.getUnit();
        fullSplitMap.putIfAbsent(mainUnit.get_id() + "", mainUnit);
        fullSplitStrIdMap.putIfAbsent(mainUnit.getId(), mainUnit);

        orgIdentity.getAllUnitMap().forEach((key, unit) -> {
            if (StringUtils.equals(unit.get_id() + "", mainUnit.get_id() + "")) {
                return;
            }
            fullSplitMap.putIfAbsent(unit.get_id() + "", mainUnit);
//            fullSplitStrIdMap.putIfAbsent(unit.getId(), mainUnit);
        });
    }

    private void registerSplitUnit(IndustryIdentity industryIdentity) {
        SplitUnit mainUnit = industryIdentity.getUnit();
        fullSplitMap.putIfAbsent(mainUnit.get_id() + "", mainUnit);
        fullSplitStrIdMap.putIfAbsent(mainUnit.getId(), mainUnit);
    }

    public void registerAllianceSplitUnit(List<AllianceSplitUnit> unitList) {
        for (AllianceSplitUnit unit : unitList) {
            String key = "" + unit.get_id();
            fullAllianceSplitMap.putIfAbsent(key, unit);
        }
    }

//    private void initDataMatrix() {
//        HashMap<String, DataMatrixInstance> idMap = new HashMap<>();
//        Iterator<MatrixStencil> it = framework.getMatrixStencilRepository().findAll().iterator();
//        while (it.hasNext()) {
//            MatrixStencil matrixStencil = it.next();
//            if (StringUtils.isBlank(matrixStencil.getJsonContent()))
//                continue;
//            DataMatrixInstance dataMatrixInstance = mapper.fromJson(matrixStencil.getJsonContent(), DataMatrixInstance.class);
//            if (StringUtils.isBlank(dataMatrixInstance.getName())) {
//                dataMatrixInstance.setName(matrixStencil.getStencilName());  // 前端商业主题 索引 页面 合并可视化时使用
//            }
//            matrixInstanceMap.put(matrixStencil.getStencilName(), dataMatrixInstance);
//            idMap.put(matrixStencil.getId(), dataMatrixInstance);
//        }
//        // 查询所有的 data matrix 对应的可视化模板
//        Iterator<MatrixVisualizationStencil> stencilIterator = framework.getMatrixVisualizationStencilRepository().findAll().iterator();
//        while (stencilIterator.hasNext()) {
//            MatrixVisualizationStencil visualizationStencil = stencilIterator.next();
//            DataMatrixInstance dataMatrixInstance = idMap.get(visualizationStencil.getMatrixStencilId());
//            if (dataMatrixInstance != null)
//                dataMatrixInstance.addVisualizationStencil(visualizationStencil);
//            else {
//                log.error("rh_matrix_stencil doesn't have {} ", visualizationStencil.getMatrixStencilId());
//            }
//        }
//        idMap.clear();
//    }

    public void initIndexGrid(CommandParam commandParam) {
        List<IndexGrid> list = commandParam.getIndexGridRepository().findAllFormal();
        for (IndexGrid grid : list) {
            if (StringUtils.containsIgnoreCase(grid.getIndexGridName(), "yjl"))
                continue;

            String indexCode = getIndexCode(grid);
            if (StringUtils.isBlank(indexCode))
                continue;
            indexCodeGridMap.putIfAbsent(indexCode, grid);

            if (StringUtils.equalsIgnoreCase(grid.getStrPlanarIndexEnum(), "SATIS_INDEX")) {
                String indexStrId = indexLongAndStrIdMap.get(grid.getLegacyIndexId());
                indexIdGridMap.putIfAbsent(indexStrId, grid);
            } else if (StringUtils.equalsIgnoreCase(grid.getStrPlanarIndexEnum(), "MS_INDEX")) {
                indexIdGridMap.putIfAbsent(grid.getLegacyIndexStrId(), grid);
            }
        }
    }

    public void initIndex(CommandParam commandParam) {
        commandParam.getLegacyIndexRepository().findAll().forEach(x -> indexLongAndStrIdMap.put(x.get_id(), x.getId()));

        commandParam.getMsdExamIndexRepository().findAll().forEach(x -> {
            if (StringUtils.isNotBlank(x.getMsdIndexId()))
                msdExamIndexIdAndStandardIdMap.put(x.getId(), x.getMsdIndexId());
        });

        commandParam.getMsdExamUniteIndexRepository().findAll().forEach(x -> {
            if (StringUtils.isNotBlank(x.getMsdUniteIndexId()))
                msdExamUniteIndexIdAndStandardIdMap.put(x.getId(), x.getMsdUniteIndexId());
        });
    }

    private String getIndexCode(IndexGrid indexGrid) {
        String indexCode = indexGrid.getLegacyIndexCode();
        if (StringUtils.isBlank(indexCode)) {
            return indexGrid.getStrPlanarIndexEnum().split("_")[0] + "_" + indexGrid.getPlanarIndexCode();
        }

        if (StringUtils.equalsIgnoreCase(indexCode, "ts")) {
            // 区分神客销售总分、物业总分
            return indexCode + "_" + indexGrid.getPlanarIndexCode();
        }
        return indexCode;
    }

    private void initGraphInteraction(CommandParam commandParam) {
        List<GraphInteraction> list = commandParam.getGraphInteractionRepository().findByStatus(YtConstants.STATUS_FORMAL);
        for (GraphInteraction interaction : list) {
            String planarCode = interaction.getCode();
            if (StringUtils.isNotBlank(planarCode)) {
                planarCodeAndGraphInteractionMap.putIfAbsent(planarCode, interaction);
            }
        }
    }

    private void initNetInteraction(CommandParam commandParam) {
        List<NetInteraction> list = commandParam.getNetInteractionRepository().findByStatus(YtConstants.STATUS_FORMAL);
        list.forEach(x -> idAndNetInteractionMap.put(x.getId(), x));
    }

    private void initMsdDataCapturePeriod(CommandParam commandParam) {
        msdDataCapturePeriods = commandParam.getMsdDataCapturePeriodApplication().searchDataCapturePeriodByCustomerIdAsc(customerId);
    }

    private String sortAttributes(String attribute) {
        if (StringUtils.isBlank(attribute) || !StringUtils.contains(attribute, "-"))
            return attribute;

        Set<String> allAttributes = new LinkedHashSet<>();
        allAttributes.addAll(Arrays.asList(BrainDataSegment.OWNER_FOUR_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.OWNER_SEVEN_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.HOUSE_TYPE_ARRAY));
        allAttributes.addAll(Arrays.asList(BrainDataSegment.DECORATION_TYPE_ARRAY));

        String[] attributeArray = attribute.split("-");
        List<String> sortAttributes = allAttributes.stream()
                .filter(x -> Arrays.asList(attributeArray).contains(x))
                .collect(Collectors.toList());

        return StringUtils.join(sortAttributes, "-");
    }

    private boolean isAttributeEqual(String attribute1, String attribute2) {
        String sort1 = sortAttributes(attribute1);
        String sort2 = sortAttributes(attribute2);
        return StringUtils.equals(sort1, sort2);
    }

    public long getMem() {
        return run.totalMemory() - run.freeMemory();
    }

    public String mem(long value) {
        double mib = d(value / 1024d / 1204d);
        if (mib < 1024d) {
            return d(mib) + " M";
        }
        return d(mib / 1024d) + " G";
    }

    public String freeMem() {
        return mem(run.freeMemory());
    }

    protected double d(double v) {
        return new BigDecimal(v).setScale(BigDecimal.ROUND_HALF_UP, RoundingMode.CEILING).doubleValue();
    }

    public void initRequired(KanbanParam kanbanParam) {
        CommandParam commandParam = kanbanParam.getCommandParam();
        customer = commandParam.getCustomerRepository().findById(customerId).orElse(null);
        splitMappingOfCustomer = commandParam.getSplitMappingRepository().findByCustomerId(customerId);
        splitGroupOfCustomer = commandParam.getSplitGroupRepository().findByCustomerID(customerId);
        standardParams = commandParam.getStandardParamRepository().findByCustomerId(customerId);

        if (StringUtils.isNotBlank(standardParamId))
            this.standardParam = standardParams.stream().filter(x -> StringUtils.equals(x.getId(), standardParamId)).findFirst().orElse(null);
        if (this.standardParam != null) {
            String categoryId = this.standardParam.getCustomerOrgCategoryId();
            this.category = commandParam.getOrgCategoryRepository().findById(categoryId)
                    .orElseThrow(() -> new NullPointerException("组织结构不存在，ID=" + categoryId));
        }

        List<MLDataSetExam> mlDataSetExams = commandParam.getMlDataSetExamRepository().findByCustomerName(customer.getName());
        if (mlDataSetExams != null && !mlDataSetExams.isEmpty()) {
            List<String> examIds = mlDataSetExams.stream().map(MLDataSetExam::getExamId).collect(Collectors.toList());
            List<SatisfactionSurvyExam> examList = commandParam.getSatisfactionSurvyExamRepository().findByIdList(examIds);
            examList.forEach(exam ->
                    mlDataSetExams.stream()
                            .filter(x -> StringUtils.equalsIgnoreCase(x.getExamId(), exam.getId()))
                            .findFirst()
                            .ifPresent(x -> examDbIdAndMLDataSetExam.put(exam.get_id(), x))
            );
        }
    }

//    public void clear() {
//        if (null != groupings && !groupings.isEmpty()) {
//            groupings.forEach(grouping -> {
//                if (null != grouping) {
//                    grouping.clear();
//                }
//            });
//            groupings.clear();
//            groupings = null;
//        }
//        if (null != viewpoints && !viewpoints.isEmpty()) {
//            viewpoints.forEach(param -> {
//                if (null != param) {
//                    param.clear();
//                }
//            });
//            viewpoints.clear();
//            viewpoints = null;
//        }
//        if (null != dafeStreams && !dafeStreams.isEmpty()) {
//            dafeStreams.clear();
//            dafeStreams = null;
//        }
//        if (null != matrixInstanceMap && !matrixInstanceMap.isEmpty()) {
//            matrixInstanceMap.forEach((key, dataMatrixInstance) -> {
//                dataMatrixInstance.clear();
//            });
//
//            matrixInstanceMap.clear();
//            matrixInstanceMap = null;
//        }
//        if (null != indexLongAndStrIdMap && !indexLongAndStrIdMap.isEmpty()) {
//            indexLongAndStrIdMap.clear();
//            indexLongAndStrIdMap = null;
//        }
//        if (null != indexCodeGridMap && !indexCodeGridMap.isEmpty()) {
//            indexCodeGridMap.forEach((key, indexGrid) -> {
//                indexGrid.clear();
//            });
//            indexCodeGridMap.clear();
//            indexCodeGridMap = null;
//        }
//        if (null != indexIdGridMap && !indexIdGridMap.isEmpty()) {
//            indexIdGridMap.forEach((key, indexGrid) -> {
//                indexGrid.clear();
//            });
//            indexIdGridMap.clear();
//            indexIdGridMap = null;
//        }
//        if (null != msdExamIndexIdAndStandardIdMap && !msdExamIndexIdAndStandardIdMap.isEmpty()) {
//            msdExamIndexIdAndStandardIdMap.clear();
//            msdExamIndexIdAndStandardIdMap = null;
//        }
//        if (null != msdExamUniteIndexIdAndStandardIdMap && !msdExamUniteIndexIdAndStandardIdMap.isEmpty()) {
//            msdExamUniteIndexIdAndStandardIdMap.clear();
//            msdExamUniteIndexIdAndStandardIdMap = null;
//        }
//        if (null != idAndNetInteractionMap && !idAndNetInteractionMap.isEmpty()) {
//            idAndNetInteractionMap.forEach((key, interaction) -> {
//                interaction.clear();
//            });
//            idAndNetInteractionMap.clear();
//            idAndNetInteractionMap = null;
//        }
//        if (null != planarCodeAndGraphInteractionMap && !planarCodeAndGraphInteractionMap.isEmpty()) {
//            planarCodeAndGraphInteractionMap.clear();
//            planarCodeAndGraphInteractionMap = null;
//        }
//        if (null != fullSplitMap && !fullSplitMap.isEmpty()) {
//            fullSplitMap.clear();
//            fullSplitMap = null;
//        }
//        if (null != fullSplitStrIdMap && !fullSplitStrIdMap.isEmpty()) {
//            fullSplitStrIdMap.clear();
//            fullSplitStrIdMap = null;
//        }
//        if (null != fullAllianceSplitMap && !fullAllianceSplitMap.isEmpty()) {
//            fullAllianceSplitMap.clear();
//            fullAllianceSplitMap = null;
//        }
//        if (null != fullSplitMapping && !fullSplitMapping.isEmpty()) {
//            fullSplitMapping.clear();
//            fullSplitMapping = null;
//        }
//        if (null != expAndPostProcessMap && !expAndPostProcessMap.isEmpty()) {
//            expAndPostProcessMap.forEach((key, list) -> {
//                if (null != list && !list.isEmpty()) {
//                    list.clear();
//                }
//            });
//            expAndPostProcessMap.clear();
//            expAndPostProcessMap = null;
//        }
//        if (null != splitMappingOfCustomer && !splitMappingOfCustomer.isEmpty()) {
//            splitMappingOfCustomer.clear();
//            splitMappingOfCustomer = null;
//        }
//        if (null != splitGroupOfCustomer && !splitGroupOfCustomer.isEmpty()) {
//            splitGroupOfCustomer.clear();
//            splitGroupOfCustomer = null;
//        }
//        if (null != textParseCache && !textParseCache.isEmpty()) {
//            textParseCache.forEach((key, textParseResult) -> {
//                if (null != textParseResult) {
//                    textParseResult.clear();
//                }
//            });
//            textParseCache.clear();
//            textParseCache = null;
//        }
//        if (null != sqlAndDataListCache && !sqlAndDataListCache.isEmpty()) {
//            sqlAndDataListCache.forEach((key, list) -> {
//                if (null != list && !list.isEmpty()) {
//                    list.forEach(map -> {
//                        if (null != map && !map.isEmpty()) {
//                            map.clear();
//                        }
//                    });
//                    list.clear();
//                }
//            });
//            sqlAndDataListCache.clear();
//            sqlAndDataListCache = null;
//        }
//        this.clearPointNetWorkMap(true);
//        if (null != relationMatrixLinkMap && !relationMatrixLinkMap.isEmpty()) {
//            relationMatrixLinkMap.forEach((key, map) -> {
//                if (null != map && !map.isEmpty()) {
//                    map.forEach((subKey, subMap) -> {
//                        if (null != subMap && !subMap.isEmpty()) {
//                            subMap.forEach((_key, list) -> {
//                                if (null != list && !list.isEmpty()) {
//                                    list.forEach(CommercialMatrixLink::clear);
//                                    list.clear();
//                                }
//                            });
//                            subMap.clear();
//                        }
//                    });
//                    map.clear();
//                }
//            });
//            relationMatrixLinkMap.clear();
//            relationMatrixLinkMap = null;
//        }
//        if (null != globalMap && !globalMap.isEmpty()) {
//            globalMap.clear();
//            globalMap = null;
//        }
//        if (null != globalIdMap && !globalIdMap.isEmpty()) {
//            globalIdMap.clear();
//            globalIdMap = null;
//        }
//        if (null != globalAllianceMap && !globalAllianceMap.isEmpty()) {
//            globalAllianceMap.clear();
//            globalAllianceMap = null;
//        }
//        if (null != globalAllianceIdMap && !globalAllianceIdMap.isEmpty()) {
//            globalAllianceIdMap.clear();
//            globalAllianceIdMap = null;
//        }
//        if (null != categoryIdAndCustomerNameMap && !categoryIdAndCustomerNameMap.isEmpty()) {
//            categoryIdAndCustomerNameMap.clear();
//            categoryIdAndCustomerNameMap = null;
//        }
//        if (null != productLabelAndProjectNamesMap && !productLabelAndProjectNamesMap.isEmpty()) {
//            productLabelAndProjectNamesMap.clear();
//            productLabelAndProjectNamesMap = null;
//        }
//        if (null != louPanCodeAndProjectNameMap && !louPanCodeAndProjectNameMap.isEmpty()) {
//            louPanCodeAndProjectNameMap.clear();
//            louPanCodeAndProjectNameMap = null;
//        }
//    }

    public void clearPointNetWorkMap(boolean setNull) {
        if (null != pointNetworkMap_1 && !pointNetworkMap_1.isEmpty()) {
            pointNetworkMap_1.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, grandChildMap) -> {
                                if (null != grandChildMap && !grandChildMap.isEmpty()) {
                                    grandChildMap.forEach((_key, list) -> {
                                        if (null != list && !list.isEmpty()) {
                                            list.clear();
                                        }
                                    });
                                    grandChildMap.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_1.clear();
            if (setNull)
                pointNetworkMap_1 = null;
        }
        if (null != pointNetworkMap_2 && !pointNetworkMap_2.isEmpty()) {
            pointNetworkMap_2.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, list) -> {
                                if (null != list && !list.isEmpty()) {
                                    list.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_2.clear();
            if (setNull)
                pointNetworkMap_2 = null;
        }
        if (null != pointNetworkMap_3 && !pointNetworkMap_3.isEmpty()) {
            pointNetworkMap_3.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, grandChildMap) -> {
                                if (null != grandChildMap && !grandChildMap.isEmpty()) {
                                    grandChildMap.forEach((_key, list) -> {
                                        if (null != list && !list.isEmpty()) {
                                            list.clear();
                                        }
                                    });
                                    grandChildMap.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_3.clear();
            if (setNull)
                pointNetworkMap_3 = null;
        }
        if (null != pointNetworkMap_4 && !pointNetworkMap_4.isEmpty()) {
            pointNetworkMap_4.forEach((key, list) -> {
                if (null != list && !list.isEmpty()) {
                    list.forEach(AbstractDataPoint::clear);
                    list.clear();
                }
            });
            pointNetworkMap_4.clear();
            if (setNull)
                pointNetworkMap_4 = null;
        }
        if (null != pointNetworkMap_5 && !pointNetworkMap_5.isEmpty()) {
            pointNetworkMap_5.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, list) -> {
                        if (null != list && !list.isEmpty()) {
                            list.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_5.clear();
            if (setNull)
                pointNetworkMap_5 = null;
        }
        if (null != pointNetworkMap_6 && !pointNetworkMap_6.isEmpty()) {
            pointNetworkMap_6.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, list) -> {
                        if (null != list && !list.isEmpty()) {
                            list.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_6.clear();
            if (setNull)
                pointNetworkMap_6 = null;
        }
        if (null != pointNetworkMap_7 && !pointNetworkMap_7.isEmpty()) {
            pointNetworkMap_7.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, list) -> {
                                if (null != list && !list.isEmpty()) {
                                    list.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_7.clear();
            if (setNull)
                pointNetworkMap_7 = null;
        }
        if (null != pointNetworkMap_8 && !pointNetworkMap_8.isEmpty()) {
            pointNetworkMap_8.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, grandChildMap) -> {
                                if (null != grandChildMap && !grandChildMap.isEmpty()) {
                                    grandChildMap.forEach((_key, list) -> {
                                        if (null != list && !list.isEmpty()) {
                                            list.clear();
                                        }
                                    });
                                    grandChildMap.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_8.clear();
            if (setNull)
                pointNetworkMap_8 = null;
        }
        if (null != pointNetworkMap_9 && !pointNetworkMap_9.isEmpty()) {
            pointNetworkMap_9.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, list) -> {
                                if (null != list && !list.isEmpty()) {
                                    list.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_9.clear();
            if (setNull)
                pointNetworkMap_9 = null;
        }
        if (null != pointNetworkMap_10 && !pointNetworkMap_10.isEmpty()) {
            pointNetworkMap_10.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, grandChildMap) -> {
                                if (null != grandChildMap && !grandChildMap.isEmpty()) {
                                    grandChildMap.forEach((_key, list) -> {
                                        if (null != list && !list.isEmpty()) {
                                            list.clear();
                                        }
                                    });
                                    grandChildMap.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_10.clear();
            if (setNull)
                pointNetworkMap_10 = null;
        }
        if (null != pointNetworkMap_11 && !pointNetworkMap_11.isEmpty()) {
            pointNetworkMap_11.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, list) -> {
                        if (null != list && !list.isEmpty()) {
                            list.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_11.clear();
            if (setNull)
                pointNetworkMap_11 = null;
        }
        if (null != pointNetworkMap_12 && !pointNetworkMap_12.isEmpty()) {
            pointNetworkMap_12.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, list) -> {
                                if (null != list && !list.isEmpty()) {
                                    list.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_12.clear();
            if (setNull)
                pointNetworkMap_12 = null;
        }
        if (null != pointNetworkMap_13 && !pointNetworkMap_13.isEmpty()) {
            pointNetworkMap_13.forEach((key, map) -> {
                if (null != map && !map.isEmpty()) {
                    map.forEach((subKey, subMap) -> {
                        if (null != subMap && !subMap.isEmpty()) {
                            subMap.forEach((grandChildKey, grandChildMap) -> {
                                if (null != grandChildMap && !grandChildMap.isEmpty()) {
                                    grandChildMap.forEach((_key, _map) -> {
                                        if (null != _map && !_map.isEmpty()) {
                                            _map.forEach((listKey, list) -> {
                                                if (null != list && !list.isEmpty()) {
                                                    list.clear();
                                                }
                                            });
                                            _map.clear();
                                        }
                                    });
                                    grandChildMap.clear();
                                }
                            });
                            subMap.clear();
                        }
                    });
                    map.clear();
                }
            });
            pointNetworkMap_13.clear();
            if (setNull)
                pointNetworkMap_13 = null;
        }
    }

//    public void previewInit() {
//        mainProcessFlag = true;
//        mathMatrixFlag = false;
//        for (ViewPointParam viewPointParam : viewpoints) {
//            viewPointParam.setFramework(framework);
//            viewPointParam.setViewPoints(new ArrayList<>());
//        }
//        this.initRequired();
//        // init all stream group
//        for (StreamGroup group : groupings) {
//            group.init(framework, this.getStandardParam());
//            fullSplitMapping.putAll(group.getGrouping().getSplitMapping());
//        }
//        initDataMatrix();
//        initIndex();
//        initIndexGrid();
//        initNetInteraction();
//        initGraphInteraction();
//    }

//    public void evictViewPoint(String viewPoint,
//                               String indices,
//                               String visualizationId,
//                               String newTemplate) {
//        Iterator<ViewPointParam> pointParamIterator = viewpoints.iterator();
//        while (pointParamIterator.hasNext()) {
//            ViewPointParam pointParam = pointParamIterator.next();
//            Iterator<AbstractViewPoint> pointIterator = pointParam.getViewPoints().iterator();
//            while (pointIterator.hasNext()) {
//                AbstractViewPoint abstractViewPoint = pointIterator.next();
//                if (abstractViewPoint instanceof LegacyOrgViewPoint) {
//                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) abstractViewPoint;
//                    if (!orgViewPoint.getSplitUnit().getId().equals(viewPoint)) {
//                        pointIterator.remove();
//                    }
//                    else {
//                        orgViewPoint.evictStencil(indices, visualizationId, newTemplate);
//                        if (orgViewPoint.dataMatrixInstanceMapEmpty())
//                            pointIterator.remove();
//                    }
//                }
//            }
//            if (pointParam.getViewPoints().size() == 0)
//                pointParamIterator.remove();
//        }
//        if (viewpoints.size() == 0)
//            throw new IllegalStateException("evictViewPoint之后，没有匹配的viewpoint");
//    }

//    public DynamicLayout findFirstViewLayout(String visualizationId) {
//        DynamicLayout result = null;
//        for (ViewPointParam viewPointParam : viewpoints) {
//            for (AbstractViewPoint abstractViewPoint : viewPointParam.getViewPoints()) {
//                if (abstractViewPoint instanceof LegacyOrgViewPoint) {
//                    LegacyOrgViewPoint orgViewPoint = (LegacyOrgViewPoint) abstractViewPoint;
//                    for (ViewPointDataMatrixInstance matrixInstance : orgViewPoint.getViewPointDataMatrixInstanceMap().values()) {
//                        result = matrixInstance.getVisualLayoutMap().get(visualizationId);
//                        if (result != null)
//                            break;
//                    }
//                }
//                if (result != null)
//                    break;
//            }
//            if (result != null)
//                break;
//        }
//        return result;
//    }

    public int totalTargetVisuals() {
        if (targetVisualList == null) {
            targetVisualList = new ArrayList<>();
            if (StringUtils.isNotBlank(targetVisuals)) {
                targetVisualList.addAll(Arrays.asList(targetVisuals.split("#")));
            }
        }
        return targetVisualList.size();
    }

    public boolean containTargetVisual(String stencilName) {
        if (targetVisualList.size() == 0)
            return true;
        return targetVisualList.contains(stencilName);
    }

//    private String shotId(CommercialMapShot shot) {
//        String shotId = "original";
//        if (null != shot) {
//            shotId = shot.getShotId();
//        }
//        return shotId;
//    }
}
