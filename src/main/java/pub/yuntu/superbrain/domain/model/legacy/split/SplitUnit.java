package pub.yuntu.superbrain.domain.model.legacy.split;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.AbstractEntity;
import pub.yuntu.superbrain.domain.model.legacy.stream.task.feature.FeatureSplitUnit;
import pub.yuntu.superbrain.domain.model.standard.SpecialAttribute;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>
 * Created by dongdong on 2017/4/15.
 */

@Entity
@Table(name = "ana_split_unit")
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Data
@Slf4j
public class SplitUnit extends FeatureSplitUnit implements Cloneable, Serializable {

    @Transient
    SplitUnit msdSalesSplitUnit;
    @Transient
    SplitUnit msdPropertySplitUnit;
    @Transient
    List<String> mixedRespondentNumbberList;
    @Transient
    Long splitGroupDbId;
    @Transient
    String splitGroupName;
    @Transient
    Integer globalSortSampleLimit;
    @Transient
    String attrExp1;
    @Transient
    String attrExp2;
    @Column(name = "customer_id")
    private String customerID;
    @Column(name = "group_id")
    private String groupID;
    @Column(name = "mixed_unit_id")
    private String mixedUnitID;
    private String name;
    private String expression;
    @Column(name = "display_name")
    private String displayName;
    @Column(name = "display_order")
    private Integer displayOrder;
    @Column(name = "org_unit_id")
    private String orgUnitId;
    @Column(name = "org_unit_name")
    private String orgUnitName;
    @Column(name = "org_unit_code")
    private String orgUnitCode;
    @Column(name = "respondent_option1_id")
    private String respondentOption1ID;
    @Column(name = "respondent_option1_name")
    private String respondentOption1Name;
    @Column(name = "respondent_option1_system_code")
    private String respondentOption1SystemCode;
    @Column(name = "respondent_option2_id")
    private String respondentOption2ID;
    @Column(name = "respondent_option2_name")
    private String respondentOption2Name;
    @Column(name = "respondent_option2_system_code")
    private String respondentOption2SystemCode;
    @Column(name = "respondent_option3_id")
    private String respondentOption3ID;
    @Column(name = "respondent_option3_name")
    private String respondentOption3Name;
    @Column(name = "respondent_option3_system_code")
    private String respondentOption3SystemCode;
    @Column(name = "mapping_cities")
    private String mappingCities;
    @Column(name = "target_score")
    private String targetScore;
    @Column(name = "vocabulary")
    private String vocabulary;
    @Transient
    private String orgCategoryTierName;
    @Transient
    private int orgCategoryTierLevel;
    // 来自组织结构分类的类型：地产、物业、地产神客、物业神客、其他
    @Transient
    private String type;
    @Transient
    private String attributeType;
    @Transient
    private SpecialAttribute specialAttribute;
    @Transient
    @Setter(AccessLevel.PROTECTED)
    private SplitUnitID splitUnitID;

    public SplitUnitID getSplitUnitID() {
        return new SplitUnitID(this.getId());
    }

    public boolean isMsdSales() {
        if (StringUtils.isEmpty(this.getType())) return Boolean.FALSE;
        return StringUtils.contains(this.getType(), "地产神客");
    }

    public boolean isMsdProperty() {
        if (StringUtils.isEmpty(this.getType())) return Boolean.FALSE;
        return StringUtils.contains(this.getType(), "物业神客");
    }

    public String getDisplayName(boolean filter) {
        if (StringUtils.isNotEmpty(displayName))
            return filterOwnerType(displayName, filter);
        return filterOwnerType(name, filter);
    }

    public String getDisplayName() {
        if (StringUtils.isNotBlank(displayName))
            return displayName;
        return name;
    }

    @Override
    public SplitUnit clone() {
        SplitUnit cloned = null;
        try {
            cloned = (SplitUnit) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return cloned;
    }

    public String cityName() {
        if (this.name.contains("_")) {
            return this.getName().split("_")[1];
        }
        return null;
    }

    public List<String> mixedRespondentNumbberList() {
        if (mixedRespondentNumbberList == null) {
            mixedRespondentNumbberList = new ArrayList<>();
            if (StringUtils.isNotEmpty(respondentOption1SystemCode))
                mixedRespondentNumbberList.add(respondentOption1SystemCode);
            if (StringUtils.isNotEmpty(respondentOption2SystemCode))
                mixedRespondentNumbberList.add(respondentOption2SystemCode);
            if (StringUtils.isNotEmpty(respondentOption3SystemCode))
                mixedRespondentNumbberList.add(respondentOption3SystemCode);
        }
        return mixedRespondentNumbberList;
    }

    public String orgCodes(int length) {
        return orgUnitCode.substring(0, length);
    }

    public String splitGroupId() {
        return this.groupID;
    }

    public String mixedUnitId() {
        return this.getMixedUnitID();
    }

    public String name() {
        return this.name;
    }

    private String filterOwnerType(String name, boolean filter) {
        name = name.replaceAll("\\+上半年", "");
        name = name.replaceAll("\\+下半年", "");
        if (filter) {
            if (name.contains("+")) {
                name = name.replaceAll("\\+准业主1", "");
                name = name.replaceAll("\\+准业主2", "");
                name = name.replaceAll("\\+准业主3", "");
                name = name.replaceAll("\\+准业主", "");
                name = name.replaceAll("\\+磨合期1", "");
                name = name.replaceAll("\\+磨合期2", "");
                name = name.replaceAll("\\+磨合期", "");
                name = name.replaceAll("\\+稳定期1", "");
                name = name.replaceAll("\\+稳定期2", "");
                name = name.replaceAll("\\+稳定期", "");
                name = name.replaceAll("\\+老业主1", "");
                name = name.replaceAll("\\+老业主2", "");
                name = name.replaceAll("\\+老业主3", "");
                name = name.replaceAll("\\+老业主", "");
            } else if (name.contains("-")) {
                name = name.replaceAll("\\-准业主1", "");
                name = name.replaceAll("\\-准业主2", "");
                name = name.replaceAll("\\-准业主3", "");
                name = name.replaceAll("\\-准业主", "");
                name = name.replaceAll("\\-磨合期1", "");
                name = name.replaceAll("\\-磨合期2", "");
                name = name.replaceAll("\\-磨合期", "");
                name = name.replaceAll("\\-稳定期1", "");
                name = name.replaceAll("\\-稳定期2", "");
                name = name.replaceAll("\\-稳定期", "");
                name = name.replaceAll("\\-老业主1", "");
                name = name.replaceAll("\\-老业主2", "");
                name = name.replaceAll("\\-老业主3", "");
                name = name.replaceAll("\\-老业主", "");
            } else {
                name = name.replaceAll("准业主1", "");
                name = name.replaceAll("准业主2", "");
                name = name.replaceAll("准业主3", "");
                name = name.replaceAll("准业主", "");
                name = name.replaceAll("磨合期1", "");
                name = name.replaceAll("磨合期2", "");
                name = name.replaceAll("磨合期", "");
                name = name.replaceAll("稳定期1", "");
                name = name.replaceAll("稳定期2", "");
                name = name.replaceAll("稳定期", "");
                name = name.replaceAll("老业主1", "");
                name = name.replaceAll("老业主2", "");
                name = name.replaceAll("老业主3", "");
                name = name.replaceAll("老业主", "");
            }
        }
        return name;
    }

    public Integer getDisplayOrder() {
        if (displayOrder == null)
            return new Integer(0);
        return displayOrder;
    }

    public List<String> mixedKeys() {
        List<String> list = new ArrayList<>();
        List<String> list1 = this.codeSplit(this.respondentOption1SystemCode);
        if (list1.size() == 0)
            return list1;
        list = list1;
        List<String> list2 = this.codeSplit(this.respondentOption2SystemCode);
        if (list2.size() != 0) {
            list = mergeList(list, list2);
        }
        List<String> list3 = this.codeSplit(this.respondentOption3SystemCode);
        if (list3.size() != 0) {
            list = mergeList(list, list3);
        }
        return list;
    }

    private List<String> mergeList(List<String> list1, List<String> list2) {
        List<String> list = new ArrayList<>();
        for (String s1 : list1) {
            for (String s2 : list2) {
                list.add(s1 + "_" + s2);
            }
        }
        return list;
    }

    private List<String> codeSplit(String code) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isBlank(code))
            return list;
        int steps = code.length() / 3;
        for (int i = 0; i < steps; i++) {
            list.add(code.substring(0, 3 + i * 3));
        }
        return list;
    }

    public String getWeightedTotalMapKey(String weightedAttribute) {
        String key = "";
        switch (weightedAttribute) {
            case "owner7":
                key = this.orgUnitCode + "_" + this.getRespondentOption1SystemCode();
                break;
            default:
                log.info("暂未支持的加权类型");
                break;
        }
        return key;
    }
}
