package pub.yuntu.superbrain.domain.model.kanban.ppt.component.chart;

import com.aspose.slides.*;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.superbrain.domain.model.creativity.block.ChartBlock;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.AbstractDataPoint;
import pub.yuntu.superbrain.domain.model.creativity.echart.data.DataPointWithValueList;
import pub.yuntu.superbrain.domain.model.kanban.ppt.KanbanPptParam;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.AxisV6;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.SeriesListV6;
import pub.yuntu.superbrain.domain.model.kanban.ppt.component.series.SeriesV6;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 单纯气泡图，没有组合其他类型序列
 * 气泡图不同于散点图，散点图有X,Y两个值（或一个值加分类Category）
 * 气泡图数据点有三个值，X,Y,SIZE（气泡大小）
 * @createTime 2022年04月18日 15:23:54
 */
@Slf4j
public class Bubble2Chart extends ChartV6 {

    public Bubble2Chart(KanbanPptParam param, ISlide slide, ChartBlock chartBlock) {
        super(param, slide, chartBlock);
    }

    @Override
    public void draw() {
        List<Chart> charts = getCharts(this);
        if (charts == null || charts.isEmpty()) return;

        // 模板中只有一个chart
        Chart asposeChart = charts.get(0);

        drawTitle(asposeChart); // 图表标题
        drawPlotArea(asposeChart); // 绘图区域尺寸
        drawLegend(asposeChart, chartConfig.getSeriesList()); // 图例
        drawAxis(asposeChart); // 轴范围

        IChartData asposeChartData = asposeChart.getChartData();
        IChartDataWorkbook fact = asposeChartData.getChartDataWorkbook();

        chartDataTableClearToMaxLine(asposeChart);

        // 从EChart转换后的序列
        List<SeriesV6> seriesList = chartConfig.getSeriesList().getSeriesList().stream()
                .filter(seriesV6 -> StringUtils.equalsIgnoreCase(seriesV6.getType(), "scatter"))
                .collect(Collectors.toList());
        if (seriesList.isEmpty()) {
            throw new IllegalArgumentException("没有检测到scatter类型的序列");
        }
        if (seriesList.size() > 2) {
            log.warn("scatter序列大于2，只处理前两个序列");
        }

        // 填充数据
        fillData(fact, seriesList, asposeChart);

        // 轴标题
        AxisV6 horizontalAxis = chartConfig.getAxis().getHorizontalAxis();
        horizontalAxis.axisTitle(asposeChart, asposeChart.getAxes().getHorizontalAxis(), horizontalAxis, AxisV6.AXIS_HORIZONTAL);
        AxisV6 verticalAxis = chartConfig.getAxis().getVerticalAxis();
        verticalAxis.axisTitle(asposeChart, asposeChart.getAxes().getVerticalAxis(), verticalAxis, AxisV6.AXIS_VERTICAL);
    }

    private void fillData(IChartDataWorkbook fact, List<SeriesV6> seriesList, Chart asposeChart) {
        SeriesV6 series1 = seriesList.get(0);
        List<? extends AbstractDataPoint> dataList1 = series1.getDataList();
        SeriesV6 series2 = seriesList.get(1);
        List<? extends AbstractDataPoint> dataList2 = series2.getDataList();

        // 名称转换为数值，作为x值
        int xIndex = 1;
        Map<String, Integer> nameIndexMapping = Maps.newHashMap();
        buildingNameIndexMap(dataList1, xIndex, nameIndexMapping);
        buildingNameIndexMap(dataList2, xIndex, nameIndexMapping);

        // 列号
        int titleRowNo = 0, xColNo = 0, s1YColNo = 1, s1SizeColNo = 2, s2YColoNo = 3, s2SizeColNo = 4, nameColNo = 5;
        // 填充表头
        addChartStaticData(fact, titleRowNo, xColNo, "x值");
        addChartStaticData(fact, titleRowNo, s1YColNo, series1.getName());
        addChartStaticData(fact, titleRowNo, s1SizeColNo, series1.getName() + "大小");
        addChartStaticData(fact, titleRowNo, s2YColoNo, series2.getName());
        addChartStaticData(fact, titleRowNo, s2SizeColNo, series2.getName() + "大小");
        addChartStaticData(fact, titleRowNo, nameColNo, "名称");
        // 填充第一个序列
        for (int iRowNo = 0; iRowNo < dataList1.size(); iRowNo++) {
            AbstractDataPoint abstractDataPoint = dataList1.get(iRowNo);
            DataPointWithValueList dataPoint = (DataPointWithValueList) abstractDataPoint;
            List<Object> valueList = (List<Object>) dataPoint.getValue();
            String name = valueList.get(0) + "";
            int nameIndex = nameIndexMapping.get(name);
            Double y = valueList.get(1) == null ? null : Double.parseDouble(valueList.get(1) + "");
            Double size = valueList.get(2) == null ? null : Double.parseDouble(valueList.get(2) + "");

            int rowNo = iRowNo + 1;
            addChartStaticData(fact, rowNo, xColNo, nameIndex);
            addChartStaticData(fact, rowNo, s1YColNo, y);
            addChartStaticData(fact, rowNo, s1SizeColNo, size);
            addChartStaticData(fact, rowNo, nameColNo, name);
        }
        // 填充第二个序列
        for (int iRowNo = 0; iRowNo < dataList2.size(); iRowNo++) {
            AbstractDataPoint abstractDataPoint = dataList2.get(iRowNo);
            DataPointWithValueList dataPoint = (DataPointWithValueList) abstractDataPoint;
            List<Object> valueList = (List<Object>) dataPoint.getValue();
            String name = valueList.get(0) + "";
            int nameIndex = nameIndexMapping.get(name);
            Double y = valueList.get(1) == null ? null : Double.parseDouble(valueList.get(1) + "");
            Double size = valueList.get(2) == null ? null : Double.parseDouble(valueList.get(2) + "");

            int rowNo = iRowNo + 1;
            addChartStaticData(fact, rowNo, xColNo, nameIndex);
            addChartStaticData(fact, rowNo, s2YColoNo, y);
            addChartStaticData(fact, rowNo, s2SizeColNo, size);
            addChartStaticData(fact, rowNo, nameColNo, name);
        }
        // 设置X/Y轴最大/小值
        minMaxAxes(asposeChart, dataList1, nameIndexMapping);
        minMaxAxes(asposeChart, dataList2, nameIndexMapping);
    }

    private static void buildingNameIndexMap(List<? extends AbstractDataPoint> dataList, int xIndex, Map<String, Integer> nameIndexMapping) {
        for (AbstractDataPoint dataPoint : dataList) {
            List<Object> valueList = (List<Object>) dataPoint.getValue();
            String name = valueList.get(0) + "";
            if (nameIndexMapping.containsKey(name)) continue;
            nameIndexMapping.put(name, xIndex);
            xIndex++;
        }
    }

    private void minMaxAxes(Chart asposeChart, List<? extends AbstractDataPoint> dataList, Map<String, Integer> nameIndexMapping) {
        double maxX = dataList.stream()
                .filter(chartPoint -> null != chartPoint.getValue())
                .mapToInt(point -> {
                    List<Object> valueList = (List<Object>) point.getValue();
                    String name = valueList.get(0) + "";
                    return nameIndexMapping.get(name);
                }).max().orElse(100);
        double minX = dataList.stream()
                .filter(chartPoint -> null != chartPoint.getValue())
                .mapToInt(point -> {
                    List<Object> valueList = (List<Object>) point.getValue();
                    String name = valueList.get(0) + "";
                    return nameIndexMapping.get(name);
                }).min().orElse(0);
        double maxY = dataList.stream()
                .filter(chartPoint -> null != chartPoint.getValue())
                .mapToDouble(point -> {
                    List<Double> valueList = (List<Double>) point.getValue();
                    return valueList.get(1);
                }).max().orElse(100d);
        double minY = dataList.stream()
                .filter(chartPoint -> null != chartPoint.getValue())
                .mapToDouble(point -> {
                    List<Double> valueList = (List<Double>) point.getValue();
                    return valueList.get(1);
                }).min().orElse(0d);

        maxX = Math.ceil(maxX);
        minX = Math.floor(minX);

        maxY = Math.ceil(maxY);
        minY = Math.floor(minY);

        IAxesManager axes = asposeChart.getAxes();
        IAxis asposeHorizontalAxis = axes.getHorizontalAxis();
        IAxis asposeVerticalAxis = axes.getVerticalAxis();

        // 禁止自动计算最大/小值，最小值可能计算出负值，坐标轴显示错位
        // horizontalAxis.setAutomaticMaxValue(false);
        asposeHorizontalAxis.setAutomaticMinValue(false);
        // verticalAxis.setAutomaticMaxValue(false);
        asposeVerticalAxis.setAutomaticMinValue(false);

        // 处理横轴
        AxisV6 _horizontalAxis = chartConfig.getAxis().getHorizontalAxis();
        Object max = _horizontalAxis.getMax();
        if (null != max) {
            if (max instanceof String) {
                String maxText = String.valueOf(max);
                if (StringUtils.equalsIgnoreCase(maxText, "dataMax")) {
                    asposeHorizontalAxis.setMaxValue(maxX);
                }

                if (maxX > 50000) {
                    asposeHorizontalAxis.setMajorUnit(20000);
                }
            }
            if (max instanceof Integer) {
                asposeHorizontalAxis.setMaxValue(Integer.parseInt(max + ""));
            }
            if (max instanceof Double) {
                asposeHorizontalAxis.setMaxValue(Double.parseDouble(max + ""));
            }
        } else {
            asposeHorizontalAxis.setMaxValue(100d);
        }

        if (maxX <= 1) {
            asposeHorizontalAxis.setMaxValue(maxX);
            asposeHorizontalAxis.setMajorUnit(0.1d);
            // 横轴标签数字格式
            asposeHorizontalAxis.setNumberFormat("0.0");
        }

        Object min = _horizontalAxis.getMin();
        if (null != min) {
            if (min instanceof String) {
                String minText = String.valueOf(min);
                if (StringUtils.equalsIgnoreCase(minText, "dataMin")) {
                    asposeHorizontalAxis.setMinValue(minX);
                }
            }
            if (min instanceof Integer) {
                asposeHorizontalAxis.setMinValue(Integer.parseInt(min + ""));
            }
            if (min instanceof Double) {
                asposeHorizontalAxis.setMinValue(Double.parseDouble(min + ""));
            }
        } else {
            asposeHorizontalAxis.setMinValue(0d);
        }

        // 处理纵轴
        AxisV6 _verticalAxis = chartConfig.getAxis().getVerticalAxis();
        max = _verticalAxis.getMax();
        if (null != max) {
            if (max instanceof String) {
                String maxText = String.valueOf(max);
                if (StringUtils.equalsIgnoreCase(maxText, "dataMax")) {
                    asposeVerticalAxis.setMaxValue(maxY);
                }

                if (maxY > 50000) {
                    asposeVerticalAxis.setMajorUnit(20000);
                }
            }
            if (max instanceof Integer) {
                asposeVerticalAxis.setMaxValue(Integer.parseInt(max + ""));
            }
            if (max instanceof Double) {
                asposeVerticalAxis.setMaxValue(Double.parseDouble(max + ""));
            }
        } else {
            asposeVerticalAxis.setMaxValue(100d);
        }

        if (maxY > 50000) {
            asposeVerticalAxis.setMajorUnit(20000);
        }

        min = _horizontalAxis.getMin();
        if (null != min) {
            if (min instanceof String) {
                String minText = String.valueOf(min);
                if (StringUtils.equalsIgnoreCase(minText, "dataMin")) {
                    asposeVerticalAxis.setMinValue(minY);
                }
            }
            if (min instanceof Integer) {
                asposeVerticalAxis.setMinValue(Integer.parseInt(min + ""));
            }
            if (min instanceof Double) {
                asposeVerticalAxis.setMinValue(Double.parseDouble(min + ""));
            }
        } else {
            asposeVerticalAxis.setMinValue(0d);
        }
//        if (maxX > 1) {
//            if (maxX + 10 < 100) maxX += 10;
//        } else {
//            maxX = 1;
//        }
//        if (maxY > 1) {
//            if (maxY + 10 < 100) maxY += 10;
//            if (maxY > 90 && maxY < 100) maxY = 100;
//        } else {
//            maxY = 1;
//        }
//        if (minX - 10 > 0) minX -= 10;
//        if (minY - 10 > 0) minY -= 10;
//        minX = minX < 0 ? 0 : minX;
//        minY = minY < 0 ? 0 : minY;
    }

    @Override
    public void drawStyle() {
        // 绘制EChart中配置的样式
        SeriesListV6 seriesList = chartConfig.getSeriesList(); // EChart转换后的配置
        SeriesV6 scatterSeries = eChartConfigSeriesV6(seriesList, "scatter");

        List<Chart> charts = getCharts(this);
        Chart asposeChart = charts.get(0);
        IChartData asposeChartData = asposeChart.getChartData();
        drawBubbleStyle(asposeChartData.getSeries().get_Item(0), scatterSeries); // 模板定义，第一序列为bubble
    }

}
