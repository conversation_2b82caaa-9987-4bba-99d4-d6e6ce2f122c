package pub.yuntu.superbrain.domain.model.creativity.param;

import lombok.Data;
import pub.yuntu.foundation.reference.project.Project;

import java.util.HashMap;

/**
 * Created by dongdong on 2021/11/5 12:17.
 */
@Data
public class TaskPeriod {
    String strDataPeriod;
    String[] dataPeriods;
    HashMap<String, MatchedCapturePeriod> capturePeriodMap;
    HashMap<String, MatchedCapturePeriod> msdSalesCapturePeriodMap;
    HashMap<String, MatchedCapturePeriod> msdPropertyCapturePeriodMap;
    HashMap<String, Project> msdSalesProjectMap;
    HashMap<String, Project> msdPropertyProjectMap;
    String[] beforePeriods; // 舆情发生前
    String[] afterPeriods; // 舆情发生后

    public TaskPeriod(){}

    public TaskPeriod(String strDataPeriod) {
        this.strDataPeriod = strDataPeriod;
        this.dataPeriods = this.strDataPeriod.split(",");
        this.capturePeriodMap = new HashMap<>();
        this.msdSalesCapturePeriodMap = new HashMap<>();
        this.msdPropertyCapturePeriodMap = new HashMap<>();
    }

    public void addCapturePeriod(String type, String period, MatchedCapturePeriod matchedCapturePeriod) {
        switch (type) {
            case "org":
                capturePeriodMap.put(period, matchedCapturePeriod);
                break;
            case "msdSales":
                msdSalesCapturePeriodMap.put(period, matchedCapturePeriod);
                break;
            case "msdProperty":
                msdPropertyCapturePeriodMap.put(period, matchedCapturePeriod);
                break;
        }
    }

    public void addProject(String type, String period, Project project) {
        switch (type) {
            case "msdSales":
                if (msdSalesProjectMap == null)
                    msdSalesProjectMap = new HashMap<>();
                msdSalesProjectMap.put(period, project);
                break;
            case "msdProperty":
                if (msdPropertyProjectMap == null)
                    msdPropertyProjectMap = new HashMap<>();
                msdPropertyProjectMap.put(period, project);
                break;
        }
    }

    public MatchedCapturePeriod findCapturePeriod(String dataPeriod) {
        return capturePeriodMap.get(dataPeriod);
    }

    public MatchedCapturePeriod findMsdSalesCapturePeriod(String dataPeriod) {
        return msdSalesCapturePeriodMap.get(dataPeriod);
    }

    public MatchedCapturePeriod findMsdPropertyCapturePeriod(String dataPeriod) {
        return msdPropertyCapturePeriodMap.get(dataPeriod);
    }

    public String latestPeriod() {
        return dataPeriods[dataPeriods.length - 1];
    }

    public String previousPeriod() {
        if (dataPeriods.length == 1)
            return null;
        return  dataPeriods[dataPeriods.length - 2];
    }

    public void clear() {
        setStrDataPeriod(null);
        setDataPeriods(null);
        if (null != capturePeriodMap) {
            capturePeriodMap.forEach((k, v) -> v.clear());
            capturePeriodMap.clear();
            setCapturePeriodMap(null);
        }
        if (null != msdSalesCapturePeriodMap) {
            msdSalesCapturePeriodMap.forEach((k, v) -> v.clear());
            msdSalesCapturePeriodMap.clear();
            setMsdSalesCapturePeriodMap(null);
        }
        if (null != msdPropertyCapturePeriodMap) {
            msdPropertyCapturePeriodMap.forEach((k, v) -> v.clear());
            msdPropertyCapturePeriodMap.clear();
            setMsdPropertyCapturePeriodMap(null);
        }
        if (null != msdSalesProjectMap) {
            msdSalesProjectMap.clear();
            setMsdSalesProjectMap(null);
        }
        if (null != msdPropertyProjectMap) {
            msdPropertyProjectMap.clear();
            setMsdPropertyProjectMap(null);
        }
        setBeforePeriods(null);
        setAfterPeriods(null);
    }
}
