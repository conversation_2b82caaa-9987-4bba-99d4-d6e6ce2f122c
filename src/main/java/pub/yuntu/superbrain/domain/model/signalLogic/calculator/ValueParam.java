package pub.yuntu.superbrain.domain.model.signalLogic.calculator;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import pub.yuntu.foundation.date.DateUtil;
import pub.yuntu.foundation.json.JsonMapper;
import pub.yuntu.foundation.math.MathUtil;
import pub.yuntu.foundation.string.WgStringUtil;
import pub.yuntu.superbrain.domain.model.brain.command.ShareInfo;
import pub.yuntu.superbrain.domain.model.businessNet.BusinessNetNode;
import pub.yuntu.superbrain.domain.model.legacy.indexGrid.IndexGrid;
import pub.yuntu.superbrain.domain.model.signal.*;
import pub.yuntu.superbrain.domain.model.signalLogic.output.dataSource.DataFinder;

import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON> on 2022/9/15 14:58.
 */
@Data
@Slf4j
public class ValueParam {
    // 配置参数
    String type;
    String name;
    String numValue;
    String strValue;
    String fieldName;
    String signalType;
    List<CalculatorCondition> conditions;  // 如果不写conditions 或 conditions为空，即无条件通过筛选
    String conditionRelation;  // 条件关系，填写conditions的下标，中括号之内是“且”的关系，中括号与中括号之间是“或”的关系，如：[0,1][2]
    String defaultValue;
    String dataType;
    String operation; // 对数值的处理，如：取日期中的年份+月份
    Map<String, String> keepInfos;  // key=保留的新字段名称，value=信号里的原始字段名称
    String fromVirtualGroup;

    // 运行过程参数
    public static Pattern numberPattern = Pattern.compile("-?[0-9]+.?[0-9E-]*");
    @JsonIgnore
    JsonMapper mapper = JsonMapper.nonEmptyMapper();

    public static boolean isNum(String s) {
        return numberPattern.matcher(s).matches();
    }

    public ValueParam deepClone() {
        String json = mapper.toJson(this);
        return mapper.fromJson(json, ValueParam.class);
    }

    public Double getNumValue(BaseSignal signal) {
        if (StringUtils.isBlank(type))
            return null;

        switch (type) {
            case "numValue":
                return Double.parseDouble(numValue);
            case "field":
                String strValue = getValueByField(signal);
                return StringUtils.isNotBlank(strValue) && WgStringUtil.isNum(strValue) ? Double.parseDouble(strValue) : null;
            case "dataType":
                return getValueByDataType(signal);
        }
        return null;
    }

    public String getStrValue(BaseSignal signal) {
        if (StringUtils.isBlank(type))
            return null;

        switch (type) {
            case "strValue":
                return strValue;
            case "numValue":
                return numValue;
            case "field":
                return getValueByField(signal);
            case "dataType":
                return getValueByDataType(signal) + "";
        }
        return null;
    }

    public String getValueByField(BaseSignal signal) {
        if (StringUtils.contains(fieldName, ".")) {
            return valueFromMap(signal);
        } else {
            return fieldValue(signal);
        }
    }

    public String fieldValue(BaseSignal signal) {
        String result = AbstractCalculator.generateInfo(fieldName, signal);

        if (StringUtils.isNotBlank(result) && WgStringUtil.isNum(result)) {
            double value = Double.parseDouble(result);
            if ((signal instanceof IndustryMarkSignal) && StringUtils.equalsIgnoreCase(fieldName, "score")) {
                value = value * 100D;
            }
            result = value + "";
        }

        return result;
    }

    public String valueFromMap(BaseSignal signal) {
        // statResult.proportion
        if (!StringUtils.contains(fieldName, "."))
            return null;

        String result = null;
        String[] keys = fieldName.split("\\.");
        HashMap statMap = new HashMap<>();

        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            if (i == 0) {
                String statMapStr = signal.getDataValue(key);
                if (!BaseSignal.notBlank(statMapStr))
                    return null;
                statMapStr = statMapStr.replaceAll("\\\\\"", "\"");
                statMap = mapper.fromJson(statMapStr, HashMap.class);

            } else if (i < keys.length - 1) {
                statMap = (HashMap) statMap.get(key);

            } else {
                result = statMap.get(key) + "";
                if (WgStringUtil.isNum(result)) {
                    double value = Double.parseDouble(result);
                    if (StringUtils.containsIgnoreCase(key, "proportion") || StringUtils.containsIgnoreCase(key, "percent")) {
                        value = value * 100D;
                    }
                    result = value + "";
                }
            }
        }

        return result;
    }

    public String getStringValue(BaseSignal signal) {
        if (StringUtils.isBlank(type)) return null;

        String result = null;
        switch (type) {
            case "field":
                if (StringUtils.isBlank(fieldName)) {
                    log.error("没有指定字段名称");
                    return null;
                }
                String dataValue = signal.getDataValue(StringUtils.toRootUpperCase(fieldName));
                if (StringUtils.isBlank(operation)) {
                    return dataValue;
                }
                result = valueByOperation(dataValue);
                break;
            default:
                log.error("暂不支持的类型：{}", type);
                break;
        }
        return result;
    }

    private String valueByOperation(String dataValue) {
        if (StringUtils.isBlank(operation)) {
            log.error("没有指定处理类型");
            return null;
        }

        String result = null;
        switch (operation) {
            // 从日期格式的字符串中提取年份+月份
            case "截取月份":
                String[] array = dataValue.split("-");
                if (array.length < 3) {
                    log.error("错误的日期格式：{}，仅支持形如：2022-10-26", dataValue);
                    return null;
                }
                try {
                    if (dataValue.contains(" ")) { // 2022-10-26 0:00:00
                        dataValue = dataValue.split(" ")[0];
                    }
                    Calendar calendar = DateUtil.toCalendarDate(dataValue);
                    int monthNum = calendar.get(Calendar.MONTH) + 1;
                    String month = monthNum < 10 ? ("0" + monthNum) : (monthNum + "");
                    result = String.format("%s-%s", calendar.get(Calendar.YEAR), month);
                } catch (Throwable e) {
                    log.error("错误的日期格式：{}", dataValue);
                    return null;
                }
                break;
            default:
                log.error("暂不支持的操作：{}", operation);
                break;
        }
        return result;
    }

    public Double getValueByDataType(BaseSignal signal) {
        if (signal instanceof AnaSignal) {
            return getAnaValue(signal);
        } else if (signal instanceof IndustryAnaSignal) {
            return getIndustryAnaValue(signal);
        } else if (signal instanceof TextSignal) {
            return getTextValue(signal);
        } else if (signal instanceof NameListSignal || signal instanceof HighNetWorthClientSignal) {
            return getStatValue(signal);
        }
        return null;
    }

    public Double getAnaValue(BaseSignal signal) {
        DataFinder indexFinder = signal.indexId();
        if (!indexFinder.isFound())
            return null;
        String signalIndexId = indexFinder.getValue();
        IndexGrid indexGrid = getIndexGrid(signal.getBaseWave().getShareInfo(), signalIndexId);

        String finalDataType = getDataType(indexGrid);
        if (StringUtils.isBlank(finalDataType))
            return null;

        String indexCode = indexGrid.getLegacyIndexCode();
        HashMap dataMap = appendIndexCode(indexCode, signal.getMapData());
        Double value = null;
        if (StringUtils.equalsIgnoreCase(finalDataType, "cs")) {
            value = msdValueByLevel(dataMap, indexCode, indexGrid.getIndexLevel());
        } else {
            String strValue = findRhAnaDataInMap(dataMap, finalDataType, false, indexCode);
            if (BaseSignal.notBlank(strValue)) {
                value = Double.parseDouble(strValue);
                String upperType = finalDataType.toUpperCase();
                if (StringUtils.startsWith(upperType, "P") || upperType.equals("NPS"))
                    value = value * 100D;
            }
        }

        return value;
    }

    public Double getIndustryAnaValue(BaseSignal signal) {
        DataFinder indexCodeFinder = signal.indexCode();
        if (!indexCodeFinder.isFound())
            return null;
        String indexCode = indexCodeFinder.getValue();

        HashMap dataMap;
        if (StringUtils.equals(signal.getFromDatabase(), "mysql")) {
            dataMap = appendIndexCode(indexCode.toUpperCase(), signal.getMapData());
        } else {
            dataMap = signal.getMapData();
        }

        IndexGrid indexGrid = signal.getBaseWave().getShareInfo().getIndexCodeAndIndexGridMap().get(indexCode);
        String finalDataType = getDataType(indexGrid);
        if (StringUtils.isBlank(finalDataType))
            return null;

        Double value = null;
        String strValue = findRhAnaDataInMap(dataMap, finalDataType, false, indexCode.toUpperCase());
        if (BaseSignal.notBlank(strValue)) {
            value = Double.parseDouble(strValue);
            String upperType = finalDataType.toUpperCase();
            if (StringUtils.startsWith(upperType, "P") || upperType.equals("NPS"))
                value = value * 100D;
        }

        return value;
    }

    public Double getTextValue(BaseSignal signal) {
        DataFinder planarCodeFinder = signal.indexCode();
        if (!planarCodeFinder.isFound())
            return null;
        String planarCode = planarCodeFinder.getValue();

        Double value = null;
        String strValue = findRhAnaDataInMap(signal.getMapData(), dataType, false, "C" + planarCode);
        if (BaseSignal.notBlank(strValue)) {
            value = Double.parseDouble(strValue);
            if (StringUtils.containsIgnoreCase(dataType, "PERCENT"))
                value = value * 100D;
        }
        return value;
    }

    public Double getStatValue(BaseSignal signal) {
        DataFinder statResultFinder = signal.statResult();
        if (!statResultFinder.isFound())
            return null;
        String statResult = statResultFinder.getValue().replaceAll("\\\\\"", "\"");
        HashMap statResultMap = mapper.fromJson(statResult, HashMap.class);
        statResultMap.forEach((key, value) -> signal.getMapData().put(key + "", value));

        Double value = null;
        String strValue = findRhAnaDataInMap(signal.getMapData(), dataType, false, null);
        if (BaseSignal.notBlank(strValue)) {
            value = Double.parseDouble(strValue);
        }
        return value;
    }

    public Double msdValueByLevel(HashMap dataMap,
                                  String indexCode,
                                  Integer indexLevel) {
        Double value = null;
        if (indexLevel == 5 || indexLevel == 6) {
            // 单一指标的得分率 = standard_score / calculated_score
            String standard_score = findRhAnaDataInMap(dataMap, "ss", false, indexCode);
            String calculated_score = findRhAnaDataInMap(dataMap, "cs", false, indexCode);
            if (BaseSignal.notBlank(standard_score) && !StringUtils.equals(standard_score, "-999")
                    && BaseSignal.notBlank(calculated_score)) {
                value = Double.parseDouble(standard_score) / Double.parseDouble(calculated_score) * 100D;
            }

        } else {
            String standard_score = findRhAnaDataInMap(dataMap, "ss", false, indexCode);
            if (BaseSignal.notBlank(standard_score) && !StringUtils.equals(standard_score, "-999")) {
                value = Double.parseDouble(standard_score);
                if (indexLevel == 3 || indexLevel == 4) {
                    // 模块分没乘100
                    value = value * 100D;
                }
            }
        }
        return value;
    }

    public IndexGrid getIndexGrid(ShareInfo shareInfo, String signalIndexId) {
        String indexId = BusinessNetNode.transformIndexId(signalIndexId, shareInfo);
        return shareInfo.getIndexIdAndIndexGridMap().get(indexId);
    }

    public String getDataType(IndexGrid indexGrid) {
        if (!StringUtils.equalsIgnoreCase(dataType, "default"))
            return dataType;
        if (indexGrid == null)
            return null;
        return indexGrid.getStrDefaultDataType();
    }

    public HashMap appendIndexCode(String indexCode, HashMap dataMap) {
        HashMap newMap = new HashMap();
        Set keySet = dataMap.keySet();
        for (Object key : keySet) {
            newMap.put(indexCode + "_" + key, dataMap.get(key));
        }
        return newMap;
    }

    public String findRhAnaDataInMap(Map data, String dataType, boolean isSecret, String code) {
        String result = "";
        switch (dataType.toUpperCase()) {
            case "P1":
            case "P2":
            case "P3":
            case "P4":
            case "P5":
            case "P6":
            case "P7":
            case "P8":
            case "P9":
            case "P10":
            case "P11":
            case "P12":
            case "P13":
            case "P14":
            case "P15":
                int index = Integer.parseInt(dataType.substring(1));
                result = getRhWeightedPercent(data, index, code);
                break;
            case "P_45":
                String s_weighted45 = data.get(code + "_WEIGHTED_4_OR_5_PERCENTAGE") + "";
                if (isSecret) {
                    double weighted45 = 0D;
                    if (StringUtils.isNotBlank(s_weighted45) && !StringUtils.equalsIgnoreCase(s_weighted45, "null")) {
                        weighted45 = Double.parseDouble(s_weighted45);
                    }
                    result = "" + weighted45;
                } else {
                    double total = Double.parseDouble("" + (null == data.get(code + "_TOTAL_SAMPLE") ? 0 : data.get(code + "_TOTAL_SAMPLE")));
                    if (total > 0D) {
                        String s_p5 = getRhWeightedPercent(data, 5, code);
                        String s_p4 = getRhWeightedPercent(data, 4, code);
                        if (StringUtils.isNotBlank(s_weighted45) && !StringUtils.equalsIgnoreCase(s_weighted45, "null")) {
                            result = s_weighted45;
                            if (s_weighted45.equals("0") || s_weighted45.equals("0.0")) {
                                // 判断是否真的为0
                                if (StringUtils.isNotBlank(s_p5) && StringUtils.isNotBlank(s_p4)) {
                                    double p5 = Double.parseDouble(s_p5);
                                    double p4 = Double.parseDouble(s_p4);
                                    if (p5 + p4 > 0) {
                                        result = "" + (p4 + p5);
                                    }
                                }
                            }
                        } else {
                            if (StringUtils.isNotBlank(s_p5) && StringUtils.isNotBlank(s_p4)) {
                                double p5 = Double.parseDouble(s_p5);
                                double p4 = Double.parseDouble(s_p4);
                                result = "" + (p4 + p5);
                            }
                        }
                    }
                }
                break;
            case "SAMPLE":
            case "TOTAL_SAMPLE":
                result = data.get(code + "_TOTAL_SAMPLE") + "";
                if (StringUtils.isBlank(result) || StringUtils.equalsIgnoreCase(result, "null"))
                    result = data.get(code + "_SAMPLE") + "";
                break;
            case "SS":
                String ss = data.get(code + "_STANDARD_SCORE") + "";
                if (StringUtils.isNotBlank(ss) && !StringUtils.equalsIgnoreCase(ss, "null"))
                    result = MathUtil.getSixPoint(Double.parseDouble(ss));
                break;
            case "CS":
            case "WEIGHT":
                // 权重分（问卷标准分）
                String cs = data.get(code + "_CALCULATED_SCORE") + "";
                if (StringUtils.isNotBlank(cs) && !StringUtils.equalsIgnoreCase(cs, "null"))
                    result = MathUtil.getSixPoint(Double.parseDouble(cs));
                break;
            case "CHECK_TIMES":
                // 检查次数
                String sw = data.get(code + "_STANDARD_WEIGHT") + "";
                if (StringUtils.isNotBlank(sw) && !StringUtils.equalsIgnoreCase(sw, "null"))
                    result = MathUtil.getSixPoint(Double.parseDouble(sw));
                break;
            case "DEDUCT_TIMES":
                // 扣分次数
                String cw = data.get(code + "_CALCULATED_WEIGHT") + "";
                sw = data.get(code + "_STANDARD_WEIGHT") + "";
                if (StringUtils.isNotBlank(cw) && !StringUtils.equalsIgnoreCase(cw, "null")
                        && StringUtils.isNotBlank(sw) && !StringUtils.equalsIgnoreCase(sw, "null")) {
                    result = (int) Double.parseDouble(cw) + "/" + (int) Double.parseDouble(sw);
                }
                break;
            case "GOOD_COUNT":
            case "GOOD_PERCENT":
            case "BAD_COUNT":
            case "BAD_PERCENT":
            case "TOTAL_COUNT":
            case "TOTAL_PERCENT":
            case "AVERAGE_MENTION_TIMES":
                result = data.get(code + "_" + dataType.toUpperCase()) + "";
                if (StringUtils.isBlank(result) || StringUtils.equalsIgnoreCase(result, "null"))
                    result = data.get(code + "_" + dataType.toUpperCase().replaceAll("_", "")) + "";
                break;
            case "WT":
                result = data.get("amount") + "";
                break;
            case "NPS":
                String str5 = getRhWeightedPercent(data, 5, code);
                if (StringUtils.isBlank(str5))
                    return "";
                double p5 = Double.parseDouble(str5);
                double p1 = 0D;
                String sp1 = getRhWeightedPercent(data, 1, code);
                if (StringUtils.isNotBlank(sp1)) {
                    p1 = Double.parseDouble(sp1);
                }
                double p2 = 0D;
                String sp2 = getRhWeightedPercent(data, 2, code);
                if (StringUtils.isNotBlank(sp2)) {
                    p2 = Double.parseDouble(sp2);
                }
                double p3 = 0D;
                String sp3 = getRhWeightedPercent(data, 3, code);
                if (StringUtils.isNotBlank(sp3)) {
                    p3 = Double.parseDouble(sp3);
                }
                result = "" + (p5 - (p1 + p2 + p3));
                break;
            case "HNWC_TOTAL":
            case "HNWC_PROPORTION":
            case "HNWC_COUNT":
                result = data.get(dataType.toLowerCase().split("_")[1]) + "";
                break;
        }
        return result;
    }

    public String getRhWeightedPercent(Map data, int weightIndex, String code) {
        String result = "";
        String s_total = getRhTotal(data, weightIndex, code);
        if (StringUtils.isBlank(s_total) || StringUtils.equalsIgnoreCase(s_total, "null"))
            return result;

        double total = Double.parseDouble(s_total);
        if (total == 0D)
            return result;

        String s_weighted = data.get(code + "_WEIGHTED_" + weightIndex + "_PERCENTAGE") + "";
        double weighted = 0D;
        if (StringUtils.isNotBlank(s_weighted) && !StringUtils.equalsIgnoreCase(s_weighted, "null")) {
            weighted = Double.parseDouble(s_weighted);
        }
        if (weighted > 0D) {
            result = "" + weighted;
        } else {
            String value = data.get(code + "_C" + weightIndex) + "";
            if (StringUtils.isNotBlank(value) && !StringUtils.equalsIgnoreCase(value, "null")) {
                double c = Double.parseDouble(value);
                result = MathUtil.getEightPoint(c / total);
            }
        }
        return result;
    }

    private String getRhTotal(Map data, int index, String code) {
        String totalByOption = data.get(code + "_SAMPLE_OF_OPTIONS") + "";
        if (StringUtils.isNotBlank(totalByOption) && !StringUtils.equalsIgnoreCase(totalByOption, "null")) {
            return getRhCount(data, index, code);
        }
        return "" + data.get(code + "_TOTAL_SAMPLE");
    }

    private String getRhCount(Map data, int index, String code) {
        String totalByOption = data.get(code + "_SAMPLE_OF_OPTIONS") + "";
        if (StringUtils.isNotBlank(totalByOption) && !StringUtils.equalsIgnoreCase(totalByOption, "null")) {
            String[] str = totalByOption.split(";");
            HashMap<String, String> map = new HashMap<>();
            for (String s : str) {
                String[] arr = s.split("\\|\\|\\|");
                String[] keys = arr[0].split(",");
                for (String key : keys)
                    map.put(key.trim(), arr[1].trim());
            }
            String val = map.get("" + index);
            if (StringUtils.isEmpty(val))
                val = "";
            return val;
        }
        return "" + data.get(code + "_C" + index);
    }

    public boolean isSignalMatch(BaseSignal signal) {
        if (StringUtils.isNotBlank(signalType) && !StringUtils.equals(signal.getType(), signalType))
            return false;

        // 需要自身数据，但 signal 来自其他神经元
        if (StringUtils.isBlank(fromVirtualGroup) && signal.getFromIdentity() != null)
            return false;

        // 要求 signal 来自指定神经元
        if (StringUtils.isNotBlank(fromVirtualGroup)
                && (signal.getFromIdentity() == null || !StringUtils.equals(signal.getFromIdentity().getGroupName(), fromVirtualGroup)))
            return false;

        return AbstractCalculator.isConditionMatch(signal, conditions, conditionRelation);
    }

//    public void clear() {
//        setType(null);
//        setName(null);
//        setNumValue(null);
//        setStrValue(null);
//        setFieldName(null);
//        setSignalType(null);
//        setConditionRelation(null);
//        setDefaultValue(null);
//        setDataType(null);
//        setOperation(null);
//        if (null != conditions) {
//            for (CalculatorCondition condition : conditions) {
//                condition.clear();
//            }
//            conditions.clear();
//        }
//        if (null != keepInfos) {
//            keepInfos.clear();
//        }
//    }

}
