package pub.yuntu.superbrain.domain.mapstruct;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import pub.yuntu.superbrain.domain.dto.Conclusion2TaskLogDTO;
import pub.yuntu.superbrain.domain.model.dsp.batch.Conclusion2TaskLog;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023年07月28日 10:36:24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface Conclusion2TaskLogMapper extends BaseMapper<Conclusion2TaskLogDTO, Conclusion2TaskLog> {
    Conclusion2TaskLogMapper INSTANCE = Mappers.getMapper(Conclusion2TaskLogMapper.class);
}
