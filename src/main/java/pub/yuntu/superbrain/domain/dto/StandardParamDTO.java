package pub.yuntu.superbrain.domain.dto;

import lombok.Data;
import pub.yuntu.superbrain.domain.model.legacy.standard.StandardParamID;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2023年07月27日 22:51:02
 */
@Data
public class StandardParamDTO extends BaseDTO implements Serializable, Cloneable {

    private StandardParamID standardParamID;
    private String customerId;
    private Long customerDbId;
    private String customerOrgCategoryId;
    private String customerOrgCategoryName;
    private String orgGroupTierId;
    private String orgGroupTierName;
    private Integer orgGroupTierLevel;
    private String orgGroupSplitId;
    private Long orgGroupSplitDbId;
    private String orgGroupSplitUnitId; // 集团切分单元ID
    private Long orgGroupSplitUnitDbId; // 集团切分单元数字ID
    private String orgGroupSplitUnitName; // 集团切分单元名称
    private String orgAreaTierId;
    private String orgAreaTierName;
    private Integer orgAreaTierLevel;
    private String orgAreaSplitId;
    private Long orgAreaSplitDbId;
    private String orgCompanyTierId;
    private String orgCompanyTierName;
    private Integer orgCompanyTierLevel;
    private String orgCompanySplitId;
    private Long orgCompanySplitDbId;
    private String orgCityTierId;
    private String orgCityTierName;
    private Integer orgCityTierLevel;
    private String orgCitySplitId;
    private Long orgCitySplitDbId;
    private String orgProjectTierId;
    private String orgProjectTierName;
    private Integer orgProjectTierLevel;
    private String orgProjectSplitId;
    private Long orgProjectSplitDbId;
    private String orgStageTierId;
    private String orgStageTierName;
    private Integer orgStageTierLevel;
    private String orgStageSplitId;
    private Long orgStageSplitDbId;
    private String attrOwnerFourId;
    private String attrHouseTypeId;
    private String attrDecorationTypeId;
    private String attrOwnerSevenId;

    private String attrVisitId; // 神客：明查、暗访
    private String attrCompetitorId; // 神客：本品、竞品
    private String attrWeekendId; // 神客：周末、非周末
    private String attrInvestigationId; // 神客：首访、复访

    private String industryBenchmarkPeriod; // 行业标杆分期
    private String industryAveragePeriod; // 行业总体分期
    private String sampleTableConfigName; // 样本量表格配置，取值：DEVELOPER-sample-table-GCP(集团公司项目)、DEVELOPER-sample-table-GAC(集团区域公司)
    private Integer displayTopTree; // 是否显示TopTree图表
    private String specifiedCurrentYear; // 指定当年年份
    private String specifiedLastYear; // 指定上一年年份
    private String specifiedCurrentMonth; // 指定当月
    private String specifiedPeriodList; // 指定年度分期序列
    private String tmOrgCategoryTiers; // 目标管理 - 使用的指定组织层级
    private String tmPrecedingExamScoreMark; // 目标管理 - 前置问卷算分方式 P1\P45

    private String orgGroupSplitName;
    private String orgAreaSplitName;
    private String orgCompanySplitName;
    private String orgCitySplitName;
    private String orgProjectSplitName;
    private String orgStageSplitName;
    private String attrOwnerFourName;
    private String attrHouseTypeName;
    private String attrDecorationTypeName;
    private String attrOwnerSevenName;
    private String attrVisitName;
    private String attrCompetitorName;
    private String attrWeekendName;
    private String attrInvestigationName;
    private CustomerDTO customer;
    private String individualBenchmark;

    @Override
    public StandardParamDTO clone() {
        StandardParamDTO cloned = null;
        try {
            cloned = (StandardParamDTO) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return cloned;
    }

}
