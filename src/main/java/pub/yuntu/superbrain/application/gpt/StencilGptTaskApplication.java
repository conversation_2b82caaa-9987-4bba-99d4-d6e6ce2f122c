package pub.yuntu.superbrain.application.gpt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import pub.yuntu.foundation.log.YtLog;
import pub.yuntu.foundation.user.BasicUser;
import pub.yuntu.superbrain.gpt.domain.stencil.StencilGptTask;
import pub.yuntu.superbrain.gpt.domain.stencil.StencilGptTaskService;
import pub.yuntu.superbrain.infrastructure.persistence.gpt.StencilGptTaskRepository;

import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.Map;

/**
 * GPT - 流计算批量执行定义(StencilGptTask)表服务应用类
 *
 * <AUTHOR> <PERSON>
 * @since 2024-05-13 11:36:11
 */
@Component
@Slf4j
public class StencilGptTaskApplication {

    @Autowired
    StencilGptTaskRepository stencilGptTaskRepository;
    @Autowired
    StencilGptTaskService stencilGptTaskService;

    @Transactional
    public StencilGptTask createStencilGptTask(
            String name,
            String comments,
            String jsonContent,
            BasicUser user) {
        Map<String, String> params = new HashMap<>();
        params.put("name", name);
        params.put("comments", comments);
        params.put("jsonContent", jsonContent);
        log.info(YtLog.LogBuilder.aLog()
                .params(params)
                .user(user.getName())
                .operation("createStencilGptTask")
                .desc("创建新StencilGptTask")
                .build().toJsonString());

        return stencilGptTaskService.createStencilGptTask(stencilGptTaskRepository,
                name,
                comments,
                jsonContent,
                user.getName());
    }

    @Transactional
    public StencilGptTask updateStencilGptTask(String id,
                                               String name,
                                               String comments,
                                               String jsonContent,
                                               BasicUser user) {
        Map<String, String> params = new HashMap<>();
        params.put("name", name);
        params.put("comments", comments);
        params.put("jsonContent", jsonContent);
        log.info(YtLog.LogBuilder.aLog()
                .params(params)
                .user(user.getName())
                .operation("updateStencilGptTask")
                .desc("更新StencilGptTask")
                .build().toJsonString());

        return stencilGptTaskService.updateStencilGptTask(stencilGptTaskRepository,
                id,
                name,
                comments,
                jsonContent,
                user.getName());
    }

    @Transactional
    public StencilGptTask updateJson(String id,
                                     String jsonContent,
                                     BasicUser user) {
        Map<String, String> params = new HashMap<>();
        params.put("jsonContent", jsonContent);
        log.info(YtLog.LogBuilder.aLog()
                .params(params)
                .user(user.getName())
                .operation("updateStencilGptTask")
                .desc("更新StencilGptTask")
                .build().toJsonString());

        return stencilGptTaskService.updateJson(stencilGptTaskRepository,
                id,
                jsonContent,
                user.getName());
    }

    /**
     * 分页查询
     */
    @Transactional
    public Page<StencilGptTask> findPage(
            String name,
            String comments,
            String jsonContent,
            int page,
            int size) {
        return stencilGptTaskService.findPage(stencilGptTaskRepository
                , name
                , comments
                , jsonContent
                , page, size);
    }

    /**
     * 通过主键删除数据
     */
    @Transactional
    public void removeStencilGptTask(String id, BasicUser user) {
        stencilGptTaskService.removeStencilGptTask(stencilGptTaskRepository, id, user.getName());
    }
}

