package pub.yuntu.superbrain.resource.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import pub.yuntu.foundation.mvc.MvcResult;
import pub.yuntu.superbrain.domain.model.dsp.DspFramework;
import pub.yuntu.superbrain.domain.model.dsp.constants.DspConstantsOperator;
import pub.yuntu.superbrain.domain.model.dsp.logData.BatchStat;
import pub.yuntu.superbrain.domain.model.dsp.logData.DspDataLog;
import pub.yuntu.superbrain.domain.model.dsp.logData.DspDataOptJson;
import pub.yuntu.superbrain.domain.model.dsp.param.DspParam;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @Description 结论逻辑 2.0 测试接口
 * @createTime 2023年08月23日 14:42:58
 */
@RestController
@Slf4j
public class DspApi {

    private final String FILE_PATH = "/Users/<USER>/800_Code/YT-Code/YT-Superbrain/src/main/java/pub/yuntu/superbrain/domain/model/dsp/json/%s.json";

    @Autowired
    DspFramework dspFramework;

    @RequestMapping(value = "/api/dsp/processingStencil/{stencilId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<MvcResult> processingStencil(@PathVariable(value = "stencilId") String stencilId,
                                                    @RequestParam(value = "user") String user) {
        dspFramework.processingStencil(stencilId, user);
        return EntityModel.of(new MvcResult(true));
    }

    @RequestMapping(value = "/api/dsp/processingShot/{shotId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<MvcResult> processingShot(@PathVariable(value = "shotId") String shotId,
                                                 @RequestParam(value = "user") String user) {
        dspFramework.processingShot(shotId, user);
        return EntityModel.of(new MvcResult(true));
    }

    @RequestMapping(value = "/api/dsp/processing/{jsonName}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public void processing(@PathVariable(value = "jsonName") String jsonName,
                           @RequestParam(value = "user") String user) {
        String configText = null;
        if (StringUtils.isBlank(jsonName)) {
            jsonName = "sample";
        }
        String filePath = String.format(FILE_PATH, jsonName);
        try {
            configText = new String(Files.readAllBytes(Paths.get(filePath)), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // DspParam dspParam = JsonUtil.jsonMapper.fromJson(configText, DspParam.class);
        DspParam dspParam = DspConstantsOperator.parseDspParamFromText(configText);
        if (null == dspParam) {
            throw new NullPointerException("解析DSP 参数失败，结果为 Null");
        }

        dspParam.setDataLog(new DspDataLog()
                .setOptSource(
                        new DspDataOptJson().setJsonName(jsonName)
                ).init(user, dspFramework.getDspComponents().getYmlConfig().conclusion2DataLogPath()));
        dspFramework.processing(dspParam);
        dspParam.getDataLog().writeToFile(dspFramework.getDspComponents().getConclusion2DataLogRepository(), new BatchStat());
    }

}
