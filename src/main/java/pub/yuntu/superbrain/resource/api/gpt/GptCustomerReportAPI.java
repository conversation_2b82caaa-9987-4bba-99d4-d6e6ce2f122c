package pub.yuntu.superbrain.resource.api.gpt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import pub.yuntu.foundation.mvc.MvcResult;
import pub.yuntu.superbrain.application.gpt.GptCustomerReportApplication;
import pub.yuntu.superbrain.domain.model.visualization.GptCustomerReport;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2025/1/2 17:01.
 */
@RestController
@Slf4j
public class GptCustomerReportAPI {
    @Autowired
    GptCustomerReportApplication gptCustomerReportApplication;

    @RequestMapping(value = "/api/gptCustomerReport", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<GptCustomerReport> createGptCustomerReport(@RequestParam(value = "customerId") String customerId,
                                                                  @RequestParam(value = "orgCategoryId") String orgCategoryId,
                                                                  @RequestParam(value = "orgCategoryName") String orgCategoryName,
                                                                  @RequestParam(value = "orgCategoryTierId") String orgCategoryTierId,
                                                                  @RequestParam(value = "orgCategoryTierName") String orgCategoryTierName,
                                                                  @RequestParam(value = "departmentId") String departmentId,
                                                                  @RequestParam(value = "departmentName") String departmentName,
                                                                  @RequestParam(value = "roleId") String roleId,
                                                                  @RequestParam(value = "roleName") String roleName,
                                                                  @RequestParam(value = "gptStencilId") String gptStencilId,
                                                                  @RequestParam(value = "gptStencilName") String gptStencilName,
                                                                  @RequestParam(value = "displayOrder") Integer displayOrder,
                                                                  @RequestParam(value = "user") String user) {

        GptCustomerReport gptCustomerReport = gptCustomerReportApplication.createGptCustomerReport(
                customerId,
                orgCategoryId,
                orgCategoryName,
                orgCategoryTierId,
                orgCategoryTierName,
                departmentId,
                departmentName,
                roleId,
                roleName,
                gptStencilId,
                gptStencilName,
                displayOrder,
                user);
        return EntityModel.of(gptCustomerReport);
    }

    @RequestMapping(value = "/api/gptCustomerReport", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<GptCustomerReport> updateGptCustomerReport(@RequestParam(value = "id") String id,
                                                                  @RequestParam(value = "category") String category,
                                                                  @RequestParam(value = "menuName") String menuName,
                                                                  @RequestParam(value = "displayOrder") Integer displayOrder,
                                                                  @RequestParam(value = "user") String user) {

        GptCustomerReport gptCustomerReport = gptCustomerReportApplication.updateGptCustomerReport(
                id,
                category,
                menuName,
                displayOrder,
                user);
        return EntityModel.of(gptCustomerReport);
    }

    @RequestMapping(value = "/api/gptCustomerReport/updateReportStatus", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<GptCustomerReport> updateReportStatus(@RequestParam(value = "id") String id,
                                                             @RequestParam(value = "reportStatus") String reportStatus,
                                                             @RequestParam(value = "user") String user) {

        GptCustomerReport gptCustomerReport = gptCustomerReportApplication.updateReportStatus(id, reportStatus, user);
        return EntityModel.of(gptCustomerReport);
    }

    @RequestMapping(value = "/api/gptCustomerReport/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public EntityModel<MvcResult> delete(@RequestParam(value = "id") String id) {
        MvcResult result = new MvcResult(true);
        try {
            gptCustomerReportApplication.delete(id);
        } catch (Exception e) {
            result.setResult(false);
            result.setMessage(e.getMessage());
        }
        return EntityModel.of(result);
    }

    @RequestMapping(value = "/api/gptCustomerReport/searchCustomerReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public CollectionModel<GptCustomerReport> searchCustomerReport(@RequestParam(value = "customerId") String customerId,
                                                                   @RequestParam(value = "orgCategoryId") String orgCategoryId,
                                                                   @RequestParam(value = "orgCategoryTierId") String orgCategoryTierId,
                                                                   @RequestParam(value = "departmentId") String departmentId,
                                                                   @RequestParam(value = "roleId") String roleId) {
        List<GptCustomerReport> gptCustomerReportList = gptCustomerReportApplication.searchCustomerReport(
                customerId, orgCategoryId, orgCategoryTierId, departmentId, roleId);
        return CollectionModel.of(gptCustomerReportList);
    }

    @RequestMapping(value = "/api/gptCustomerReport/changeShowNameDropdown", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public MvcResult changeShowNameDropdown(@RequestParam(value = "id") String id,
                                            @RequestParam(value = "showNameDropdown") String showNameDropdown,
                                            @RequestParam(value = "user") String user) {
        MvcResult mvcResult = new MvcResult(true);
        try {
            gptCustomerReportApplication.changeShowNameDropdown(id, showNameDropdown, user);
        } catch (Exception e) {
            log.error("", e);
            mvcResult.setResult(false);
            mvcResult.setMessage(e.getMessage());
        }
        return mvcResult;
    }

}
