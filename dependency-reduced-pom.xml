<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>spring-boot-starter-parent</artifactId>
    <groupId>org.springframework.boot</groupId>
    <version>2.3.3.RELEASE</version>
    <relativePath>pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>pub.yuntu</groupId>
  <artifactId>YT-Superbrain</artifactId>
  <name>YT :: Superbrain</name>
  <version>0.0.1-SNAPSHOT</version>
  <description>YT-Superbrain</description>
  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.*</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>com.vesoft</pattern>
                  <shadedPattern>com.vesoft.v3</shadedPattern>
                  <includes>
                    <include>com.vesoft.**</include>
                  </includes>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <fork>true</fork>
          <excludes>
            <exclude>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>UTF-8</encoding>
          <showWarnings>true</showWarnings>
          <showDeprecation>true</showDeprecation>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>dev</id>
      <distributionManagement>
        <repository>
          <id>yt-nexus-releases</id>
          <name>Nexus Release Repository</name>
          <url>http://120.26.8.63:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
          <id>yt-nexus-snapshots</id>
          <name>Nexus Snapshot Repository</name>
          <url>http://120.26.8.63:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
      <properties>
        <profiles.active>dev</profiles.active>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <distributionManagement>
        <repository>
          <id>yt-nexus-releases</id>
          <name>Nexus Release Repository</name>
          <url>http://120.26.8.63:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
          <id>yt-nexus-snapshots</id>
          <name>Nexus Snapshot Repository</name>
          <url>http://120.26.8.63:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
      <properties>
        <profiles.active>test</profiles.active>
      </properties>
    </profile>
    <profile>
      <id>product</id>
      <distributionManagement>
        <repository>
          <id>yt-nexus-releases</id>
          <name>Nexus Release Repository</name>
          <url>http://172.20.1.1:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
          <id>yt-nexus-snapshots</id>
          <name>Nexus Snapshot Repository</name>
          <url>http://172.20.1.1:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
      <properties>
        <profiles.active>product</profiles.active>
      </properties>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>pub.yuntu</groupId>
      <artifactId>YT-Foundation-Library</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>lombok</artifactId>
          <groupId>org.projectlombok</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-security-core</artifactId>
          <groupId>org.springframework.security</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-api</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.17.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.4.0-jre</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>pub.yuntu</groupId>
      <artifactId>YT-API</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-rest</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-hateoas</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter</artifactId>
      <version>2.2.0.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-netflix-zuul</artifactId>
      <version>2.2.0.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
      <version>5.2.8.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>5.2.8.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-ehcache</artifactId>
      <version>5.4.20.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.ehcache</groupId>
      <artifactId>ehcache</artifactId>
      <version>2.10.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.21</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.12</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.14</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-math3</artifactId>
      <version>3.6.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>xerces</groupId>
      <artifactId>xercesImpl</artifactId>
      <version>2.11.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.12</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>spring-boot-test</artifactId>
          <groupId>org.springframework.boot</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-boot-test-autoconfigure</artifactId>
          <groupId>org.springframework.boot</groupId>
        </exclusion>
        <exclusion>
          <artifactId>assertj-core</artifactId>
          <groupId>org.assertj</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hamcrest</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-vintage-engine</artifactId>
          <groupId>org.junit.vintage</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mockito-core</artifactId>
          <groupId>org.mockito</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mockito-junit-jupiter</artifactId>
          <groupId>org.mockito</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsonassert</artifactId>
          <groupId>org.skyscreamer</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-test</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-core</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-slides</artifactId>
      <version>21.3</version>
      <classifier>jdk16</classifier>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.abel533</groupId>
      <artifactId>ECharts</artifactId>
      <version>3.0.0.6</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-config</artifactId>
      <version>2.2.0.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <version>2.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>pub.yuntu</groupId>
      <artifactId>libsvm</artifactId>
      <version>1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>de.bwaldvogel</groupId>
      <artifactId>liblinear</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.thunlp</groupId>
      <artifactId>THULAC_java</artifactId>
      <version>1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.huaban</groupId>
      <artifactId>jieba-analysis</artifactId>
      <version>1.0.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.bytedeco</groupId>
      <artifactId>javacv-platform</artifactId>
      <version>1.3.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.github.fanyong920</groupId>
      <artifactId>jvppeteer</artifactId>
      <version>1.1.3</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.kennycason</groupId>
      <artifactId>kumo-core</artifactId>
      <version>1.28</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.kennycason</groupId>
      <artifactId>kumo-tokenizers</artifactId>
      <version>1.28</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
      <version>2.3.3.RELEASE</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>lettuce-core</artifactId>
          <groupId>io.lettuce</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>3.3.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <version>2.8.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.alink</groupId>
      <artifactId>alink_core_flink-1.13_2.11</artifactId>
      <version>1.6.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-streaming-scala_2.11</artifactId>
      <version>1.13.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-table-planner_2.11</artifactId>
      <version>1.13.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-clients_2.11</artifactId>
      <version>1.13.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-runtime-web_2.11</artifactId>
      <version>1.11.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.2.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.hankcs</groupId>
      <artifactId>hanlp</artifactId>
      <version>portable-1.8.4</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>1.4.2.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>1.4.2.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.nebula-contrib</groupId>
      <artifactId>nebula-jdbc</artifactId>
      <version>3.3.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.vesoft</groupId>
      <artifactId>client</artifactId>
      <version>3.6.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.4</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-jexl3</artifactId>
      <version>3.3</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
